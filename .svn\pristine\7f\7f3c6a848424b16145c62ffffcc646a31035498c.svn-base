<template>
  <div>
    <div class="bg-white rounded-sm p-3 mb-1">
      <div class="input-group input-group-solid" v-on:click="openFilter">
        <span class="pl-3 font-weight-bolder">筛选：</span>
        <span class="form-control form-control-sm text-truncate">
          <span v-if="title">{{title}}&nbsp;</span>
          <span v-if="emergencyLevel == 1">低&nbsp;</span><span v-if="emergencyLevel == 2">中&nbsp;</span><span v-if="emergencyLevel == 3">高&nbsp;</span>
          <template v-for="item in empList"><span :key="item.recordId" v-if="item.status && item.status == '1'">{{ item.name }}&nbsp;</span></template>
        </span>
      </div>
    </div>
    <div class="bg-white rounded-sm p-3 mb-3">
      <div class="d-flex align-items-center">
        <template v-for="item in categoryList">
          <button :key="item.id" :class="['btn mr-3 px-2 py-1 rounded-sm',(activeCategory == item.id || (!activeCategory && !item.id)) ? 'btn-primary' : 'btn-light bg-white']" v-on:click="queryList(item.id, '1')">{{item.name}}</button>
        </template>
      </div>
    </div>
    <div class="mb-3 pl-3">管理和跟踪所有项目任务</div>
    <section v-if="taskList && taskList.length > 0">
      <template v-for="item in taskList">
        <div class="bg-white p-3 mb-3" :key="item.recordId" v-on:click="showDetails(item)">
          <div class="d-flex justify-content-between align-items-center">
            <div class="text-truncate text-primary" style="width: 10rem;">
              <span v-if="item.emergencyLevel && item.emergencyLevel == 1" class="badge badge-secondary rounded-xl mr-1">低</span>
              <span v-else-if="item.emergencyLevel && item.emergencyLevel == 2" class="badge badge-warning rounded-xl mr-1">中</span>
              <span v-else-if="item.emergencyLevel && item.emergencyLevel == 3" class="badge badge-danger rounded-xl mr-1">高</span>
              <span class="pr-1">T-{{item.recordId}}</span>
            </div>
            <div>
              <span class="text-danger pr-1" v-if="item.finishDay > 0">逾期{{item.finishDay}}天</span>
              <del v-if="item.status == 2">{{item.completeTime}}</del>
              <span v-else>{{item.completeTime}}</span>
            </div>
          </div>
          <div class="pt-2 text-truncate font-weight-bolder">{{item.title}}</div>
          <div>负责人：{{item.principals.name}}</div>
          <div class="text-truncate" v-if="item.participates">参与人：{{item.participates}}</div>
          <div class="text-truncate" v-if="item.shares">管理人：{{item.shares}}</div>
          <div class="pt-1 text-truncate">{{item.content}}</div>
          <div class="d-flex justify-content-between align-items-center pt-2">
            <span class="badge alert-warning py-1 px-1" v-if="item.finishDay <= 0 && item.status == 1">进行中</span>
            <span class="badge alert-success py-1 px-1" v-if="item.status == 2">结束</span>
            <span class="badge alert-danger py-1 px-1" v-if="item.finishDay > 0 && item.status == 1">逾期</span>
            <div class="d-flex align-items-center">
              <div class="progress mr-1" style="height: 6px;width: 88px;">
                <div :class="['progress-bar', item.finishDay > 0 ? 'bg-danger' : 'bg-success']" role="progressbar"
                     :style="'width:' + item.schedule + '%'" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
              <div class="text-muted text-center" style="width: 25px;">{{item.schedule}}%</div>
            </div>
          </div>
          <div v-if="item.lastUpdDate">结束时间：{{item.lastUpdDate}}</div>
        </div>
      </template>
    </section>
    <b-modal ref="filter" hide-footer hide-header>
      <div>
        <div class="pb-3">
          <span class="text-muted">标题或内容</span>
          <input class="form-control" v-model="title"/>
        </div>
        <div class="pb-3">
          <span class="text-muted">优先级</span>
          <select class="form-control" v-model="emergencyLevel">
            <option value=""></option>
            <option value="1">低</option>
            <option value="2">中</option>
            <option value="3">高</option>
          </select>
        </div>
        <div style="max-height: 36vh;overflow-y: auto;">
          <div class="pb-3 border-bottom">
            <div class="text-muted">负责人筛选</div>
            <input class="form-control form-control-sm" style="width: 10rem;" v-model="empFilter"/>
          </div>
          <div class="row pt-3">
            <template v-for="item in empList">
              <div class="col-4" :key="item.recordId" v-if="item.name.indexOf(empFilter) !== -1">
                <button :class="['btn btn-sm text-left', item.status && item.status == '1' ?'btn-primary' : 'btn-outline-secondary', 'w-100 mb-2']" v-on:click="setStatus(item)">{{ item.name }}</button>
              </div>
            </template>
          </div>
        </div>
        <div class="d-flex justify-content-end pt-10">
          <button class="btn btn-outline-secondary" v-on:click="cancelFilter">清空</button>
          <button class="btn btn-outline-primary ml-3" v-on:click="applyFilter">应用</button>
        </div>
      </div>
    </b-modal>
    <div style="height: 100px;"></div>
  </div>
</template>

<script>
export default {
  name: "taskMain",
  created:function(){
    this.scroll();
  },
  destroyed () {
    window.removeEventListener('scroll',this.scrollEvent,false);
  },
  beforeRouteLeave(to, from, next){
    window.removeEventListener('scroll',this.scrollEvent,false);
    next();
  },
  activated(){
    this.scroll();
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.employeeId){
        return;
      }
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return{
      userMsg: {},
      activeCategory: '',
      categoryList: [{id: '', name: "总任务"}, {id: '1', name: "进行中"}, {id: '4', name: "待结束"}, {id: '2', name: "已结束"}, {id: '3', name: "逾期"}],
      taskList:[],
      empList: [],
      title:"",
      emergencyLevel:"",
      shareIds:"",
      empFilter: "",
      pageNo: 0,
      pageSize: 7,
      isLoading: false,
      isMore: true,
    }
  },
  methods:{
    enableLoadFlag(flag) {
      this.$parent.enableLoadFlag(flag);
    },
    loadData() {
      window.localStorage.setItem("tabId", '4');
      this.activeCategory = window.localStorage.getItem("task_activeCategory") ? window.localStorage.getItem("task_activeCategory") : this.activeCategory;
      this.title = window.localStorage.getItem("task_title") ? window.localStorage.getItem("task_title") : this.title;
      this.emergencyLevel = window.localStorage.getItem("task_emergencyLevel") ? window.localStorage.getItem("task_emergencyLevel") : this.emergencyLevel;
      this.shareIds = window.localStorage.getItem("task_shareIds") ? window.localStorage.getItem("task_shareIds") : this.shareIds;
      this.getTaskList();
      this.getEmpList();
    },
    queryList(id, flag){
      this.pageNo = 0;
      this.isLoading = false;
      this.isMore = true;
      if (flag == '1'){
        this.activeCategory = id;
      }
      this.getTaskList();
    },
    scroll(){
      window.addEventListener('scroll', this.scrollEvent);
    },
    scrollEvent(){
      const top = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop;
      const bottomOfWindow = document.body.scrollHeight - top - window.innerHeight <= 100;
      if (bottomOfWindow && this.isMore && !this.isLoading){
        this.isLoading = true;
        this.pageNo = this.pageNo + 1;
        this.getTaskList();
      }
    },
    getTaskList () {
      if (this.pageNo == 0) {
        this.taskList = [];
      }
      window.localStorage.setItem("task_activeCategory", this.activeCategory);
      window.localStorage.setItem("task_title", this.title);
      window.localStorage.setItem("task_emergencyLevel", this.emergencyLevel);
      window.localStorage.setItem("task_shareIds", this.shareIds);
      let query = {};
      query.empId = this.userMsg.employeeId;
      query.finsh = this.activeCategory;
      query.title = this.title;
      query.emergencyLevel = this.emergencyLevel;
      query.principals = this.shareIds;
      query.pageNo = this.pageNo * this.pageSize;
      query.pageSize = this.pageSize;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/getTaskList", query).then(result => {
        if (result && result.data && result.data.length > 0){
          if (this.pageNo > 0) {
            for (let i=0;i<result.data.length;i++){
              this.taskList.push(result.data[i]);
            }
          }else {
            this.taskList = result.data;
          }
        }else {
          this.isMore = false;
        }
        this.isLoading = false;
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    showDetails:function(item){
      this.$router.push({path: '/task/taskDetails', query: {taskId: item.recordId}});
    },
    getEmpList () {
      const query = {};
      query.departId = this.userMsg.departId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/getEmpList", query).then(result => {
        if (result && result.data){
          this.empList = result.data;
          for (let i=0;i<this.empList.length;i++) {
            if (this.shareIds && this.isIdInString(this.empList[i].recordId, this.shareIds)){
              this.empList[i].status = '1';
            }else {
              this.empList[i].status = '';
            }
          }
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    isIdInString(targetId, idString) {
      const idArray = idString.split(',').map(id => id.trim());
      return idArray.includes(targetId);
    },
    openFilter() {
      this.$refs['filter'].show();
    },
    setStatus(item) {
      if (item.status){
        item.status = '';
      }else {
        item.status = '1';
      }
      for (let i=0;i<this.empList.length;i++) {
        if (this.empList[i].recordId == item.recordId){
          this.$set(this.empList, i, item);
          break;
        }
      }
    },
    cancelFilter() {
      this.title = "";
      this.emergencyLevel = "";
      for (let i=0;i<this.empList.length;i++) {
        this.empList[i].status = '';
      }
      this.shareIds = "";
      this.$refs['filter'].hide();
      this.queryList();
    },
    applyFilter() {
      this.shareIds = "";
      for (let i=0;i<this.empList.length;i++) {
        if (this.empList[i].status && this.empList[i].status == "1"){
          this.shareIds = this.shareIds ? this.shareIds + "," + this.empList[i].recordId : this.empList[i].recordId;
        }
      }
      this.$refs['filter'].hide();
      this.queryList();
    }
  }
}
</script>
