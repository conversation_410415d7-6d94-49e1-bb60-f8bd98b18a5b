<template>
  <div>
    <div class="bg-white rounded-sm p-3 mb-1">
      <div class="input-group input-group-solid" v-on:click="openFilter">
        <span class="pl-3 font-weight-bolder">筛选：</span>
        <span class="form-control form-control-sm text-truncate">
          <span v-if="condition">{{condition}}&nbsp;</span>
          <span v-if="applicationsType == 1">草稿&nbsp;</span><span v-else-if="applicationsType == 2">审批中&nbsp;</span><span v-else-if="applicationsType == 3">通过&nbsp;</span><span v-else-if="applicationsType == 4">驳回&nbsp;</span>
          <template v-for="item in auditTypeList"><span :key="item.recordId" v-if="item.status && item.status == '1'">{{ item.name }}&nbsp;</span></template>
        </span>
      </div>
    </div>
    <div class="bg-white rounded-sm p-3 mb-3">
      <div class="d-flex align-items-center">
        <template v-for="item in categoryList">
          <button :key="item.id" :class="['btn mr-3 px-2 py-1 rounded-sm',(activeCategory == item.id || (!activeCategory && !item.id)) ? 'btn-primary' : 'btn-light bg-white']" v-on:click="queryList(item.id, '1')">{{item.name}}</button>
        </template>
      </div>
    </div>
    <div class="mb-3 pl-3">管理和处理所有审批的申请</div>
    <section v-if="auditList && auditList.length > 0">
      <template v-for="item in auditList">
        <div class="bg-white p-3 mb-3" :key="item.recordId" v-on:click="showDetails(item)">
          <div class="d-flex justify-content-between align-items-center">
            <div class="text-truncate text-primary" style="width: 12rem;">
              <span :class="['badge', 'alert-' + item.typeColor, 'px-1 py-1']">
                <i :class="['fa', item.typeIconClass, 'text-' + item.typeColor, 'font-size-sm']"></i>
              </span>
              <span class="pr-1">{{ item.typeCode }}-{{item.no}}</span>
            </div>
            <div class="font-weight-bolder">{{item.typeName}}</div>
          </div>
          <div class="pt-1 text-truncate font-weight-bolder">{{item.auditResult}}</div>
          <div class="pt-1 text-truncate text-primary" v-if="item.applicationsType == 2 && item.positionName">
            [待{{item.positionName}}审批]<span v-if="item.approveUserName">-{{item.approveUserName}}</span>
          </div>
          <div class="pt-1 text-truncate text-warning" v-else-if="item.applicationsType == 1">
            [草稿]
          </div>
          <div class="pt-1 text-truncate text-danger" v-else-if="item.applicationsType == 3 && item.applicationsResult=='reject'">
            [已驳回]
          </div>
          <div class="pt-1 text-truncate text-success" v-else-if="item.applicationsType == 3 && item.applicationsResult=='assent'">
            [通过]
          </div>
          <div class="pt-1 d-flex justify-content-between align-items-center">
            <div>提交人：{{item.name}}</div>
            <div>提交时间：{{item.createdDate}}</div>
          </div>
        </div>
      </template>
    </section>
    <div style="height: 100px;"></div>
    <b-modal ref="filter" hide-footer hide-header>
      <div>
        <div class="pb-7">
          <span class="text-muted">标题或内容</span>
          <input class="form-control" v-model="condition"/>
        </div>
        <div class="pb-7">
          <span class="text-muted">审批状态</span>
          <select class="form-control" v-model="applicationsType">
            <option value=""></option>
            <option value="1">草稿</option>
            <option value="2">审批中</option>
            <option value="3">通过</option>
            <option value="4">驳回</option>
          </select>
        </div>
        <div style="max-height: 36vh;overflow-y: auto;">
          <div class="pb-3 border-bottom">
            <div class="text-muted">审批类型</div>
            <input class="form-control form-control-sm" style="width: 10rem;" v-model="typeFilter"/>
          </div>
          <div class="row pt-3">
            <template v-for="item in auditTypeList.filter(i => i.name && i.name.indexOf(typeFilter) !== -1).sort((a, b) => (b.status === '1' ? 1 : 0) - (a.status === '1' ? 1 : 0))">
              <div class="col-4" :key="item.recordId">
                <button :class="['btn btn-sm text-left text-truncate', item.status && item.status == '1' ? 'btn-primary' : 'btn-outline-secondary', 'w-100 mb-2']" v-on:click="setStatus(item)">{{ item.name }}</button>
              </div>
            </template>
          </div>
        </div>
        <div class="d-flex justify-content-end">
          <button class="btn btn-outline-secondary" v-on:click="cancelFilter">清空</button>
          <button class="btn btn-outline-primary ml-3" v-on:click="applyFilter">应用</button>
        </div>
      </div>
    </b-modal>
  </div>
</template>
<script>
export default {
  name: 'auditMain',
  created:function(){
    this.scroll();
  },
  destroyed () {
    window.removeEventListener('scroll',this.scrollEvent,false);
  },
  beforeRouteLeave(to, from, next){
    window.removeEventListener('scroll',this.scrollEvent,false);
    next();
  },
  activated(){
    this.scroll();
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.employeeId){
        return;
      }
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data() {
    return {
      userMsg: {},
      activeCategory: '1',
      categoryList: [{id: '1', name: "待办"}, {id: '2', name: "已办"}, {id: '3', name: "我的"}, {id: '4', name: "抄送"}, {id: '', name: "所有"}],
      auditList: [],
      auditTypeList: [],
      condition: "",
      applicationsType: "",
      typeFilter: "",
      pageNo: 0,
      pageSize: 7,
      isLoading: false,
      isMore: true,
      typeIds: "",
    }
  },
  methods: {
    enableLoadFlag(flag) {
      this.$parent.enableLoadFlag(flag);
    },
    loadData() {
      window.localStorage.setItem("tabId", '1');
      this.activeCategory = window.localStorage.getItem("audit_activeCategory") ? window.localStorage.getItem("audit_activeCategory") : this.activeCategory;
      this.condition = window.localStorage.getItem("audit_condition") ? window.localStorage.getItem("audit_condition") : this.condition;
      this.applicationsType = window.localStorage.getItem("audit_applicationsType") ? window.localStorage.getItem("audit_applicationsType") : this.applicationsType;
      this.typeIds = window.localStorage.getItem("audit_typeIds") ? window.localStorage.getItem("audit_typeIds") : this.typeIds;
      this.getAuditList();
      this.getAuditTypeList();
    },
    queryList(id, flag){
      this.pageNo = 0;
      this.isLoading = false;
      this.isMore = true;
      if (flag == '1'){
        this.activeCategory = id;
      }
      this.getAuditList();
    },
    scroll(){
      window.addEventListener('scroll', this.scrollEvent);
    },
    scrollEvent(){
      const top = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop;
      const bottomOfWindow = document.body.scrollHeight - top - window.innerHeight <= 100;
      if (bottomOfWindow && this.isMore && !this.isLoading){
        this.isLoading = true;
        this.pageNo = this.pageNo + 1;
        this.getAuditList();
      }
    },
    getAuditList() {
      if (this.pageNo == 0) {
        this.auditList = [];
      }
      window.localStorage.setItem("audit_activeCategory", this.activeCategory);
      window.localStorage.setItem("audit_condition", this.condition);
      window.localStorage.setItem("audit_applicationsType", this.applicationsType);
      window.localStorage.setItem("audit_typeIds", this.typeIds);
      let query = {};
      query.empId = this.userMsg.employeeId;
      query.flag = this.activeCategory;
      query.condition = this.condition;
      if (this.applicationsType && Number(this.applicationsType) == 3){
        query.applicationsType = 3;
        query.applicationsResult = "assent";
      }else if (this.applicationsType && Number(this.applicationsType) == 4){
        query.applicationsType = 3;
        query.applicationsResult = "reject";
      }else {
        query.applicationsType = this.applicationsType;
      }
      query.auditType = this.typeIds;
      query.pageNo = this.pageNo * this.pageSize;
      query.pageSize = this.pageSize;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/getAuditList", query).then(result => {
        if (result && result.data && result.data.length > 0){
          if (this.pageNo > 0) {
            for (let i=0;i<result.data.length;i++){
              this.auditList.push(result.data[i]);
            }
          }else {
            this.auditList = result.data;
          }
        }else {
          this.isMore = false;
        }
        this.isLoading = false;
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    showDetails (item) {
      this.$router.push({path: '/audit/auditDetails', query: {auditId: item.recordId}});
    },
    getAuditTypeList() {
      this.auditTypeList = [];
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("hr/approve/getAuditTypeName").then(result => {
        if (result && result.data){
          this.auditTypeList = result.data;
          for (let i=0;i<this.auditTypeList.length;i++) {
            if (this.typeIds && this.isIdInString(this.auditTypeList[i].recordId, this.typeIds)){
              this.auditTypeList[i].status = '1';
            }else {
              this.auditTypeList[i].status = '';
            }
          }
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    isIdInString(targetId, idString) {
      const idArray = idString.split(',').map(id => id.trim());
      return idArray.includes(targetId);
    },
    openFilter() {
      this.$refs['filter'].show();
    },
    setStatus(item) {
      if (item.status){
        item.status = '';
      }else {
        item.status = '1';
      }
      for (let i=0;i<this.auditTypeList.length;i++) {
        if (this.auditTypeList[i].recordId == item.recordId){
          this.$set(this.auditTypeList, i, item);
          break;
        }
      }
    },
    cancelFilter() {
      this.condition = "";
      this.applicationsType = "";
      for (let i=0;i<this.auditTypeList.length;i++) {
        this.auditTypeList[i].status = '';
      }
      this.typeIds = "";
      this.$refs['filter'].hide();
      this.queryList();
    },
    applyFilter() {
      this.typeIds = "";
      for (let i=0;i<this.auditTypeList.length;i++) {
        if (this.auditTypeList[i].status && this.auditTypeList[i].status == "1"){
          this.typeIds = this.typeIds ? this.typeIds + "," + this.auditTypeList[i].recordId : this.auditTypeList[i].recordId;
        }
      }
      this.$refs['filter'].hide();
      this.queryList();
    },
  }
}
</script>
