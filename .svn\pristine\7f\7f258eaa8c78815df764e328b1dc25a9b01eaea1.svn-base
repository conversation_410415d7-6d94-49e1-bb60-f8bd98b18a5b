<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">信息管理</h3>
    </div>
    <section class="bg-white p-2 rounded-sm">
      <div class="d-flex align-items-center">
        <img :src="userMsg.downloadUrl" alt="用户头像" class="avatar-common">
        <div class="flex-grow-1 pl-3">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="font-weight-bolder">{{ userMsg.empName }}</h5>
            <span class="badge alert-primary d-flex align-items-center" style="font-size: 15px;">
              {{ userMsg.departName }}
            </span>
          </div>
        </div>
      </div>
    </section>
    <div style="padding-bottom: 30px;">
      <template v-for="item in showList">
        <section class="bg-white p-2 rounded-xl mt-6" :key="item.id">
          <div class="d-flex align-items-center">
            <div class="flex-grow-1">
              <div class="d-flex justify-content-between align-items-center">
                <span style="font-size: 15px;">{{item.name}}</span>
                <span class="badge alert-success d-flex align-items-center" v-if="item.value">{{item.value}}</span>
              </div>
              <template v-for="row in item.itemList">
                <div class="d-flex justify-content-between align-items-center pt-3" :key="row.id">
                  <span class="text-muted" style="font-size: 13px;">{{row.name}}</span>
                  <span style="padding: 2px 6px;font-size: 13px;">{{row.value}}</span>
                </div>
              </template>
            </div>
          </div>
        </section>
      </template>
    </div>
    <div style="height: 100px;"></div>
  </div>
</template>
<script>
export default {
  data() {
    return{
      userMsg: {},
      showList: []
    }
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  methods: {
    loadData() {
      this.showList = [
        {id: 1, name: "身份信息", value: this.userMsg.enableId == 1 ? "已激活":"已失效",
          itemList: [
            {id: 1, name: "工号", value: ""},
            {id: 2, name: "所属部门", value: this.userMsg.departmentName},
            {id: 3, name: "职位", value: this.userMsg.position},
            {id: 4, name: "入职日期", value: this.userMsg.hiredDate},
            {id: 5, name: "直接上级", value: this.userMsg.superName},
            {id: 6, name: "员工级别", value: this.userMsg.employeeLevel},
          ]
        },
        {id: 2, name: "联系方式", value: "",
          itemList: [
            {id: 1, name: "手机号码", value: this.userMsg.phone},
            {id: 2, name: "电子邮箱", value: this.userMsg.email},
            {id: 3, name: "现住地址", value: this.userMsg.householdAddressT},
            {id: 4, name: "紧急联系人", value: this.userMsg.contactNameOneT},
            {id: 5, name: "紧急联系人电话", value: this.userMsg.contactPhoneOneT},
          ]
        },
        {id: 3, name: "学历信息", value: "",
          itemList: [
            {id: 1, name: "最高学历", value: this.userMsg.levelOfEducationTStr},
            {id: 2, name: "毕业院校", value: this.userMsg.graduationSchoolHeightT},
            {id: 3, name: "教育模式", value: this.userMsg.firstEducationModelT},
            {id: 4, name: "专业", value: this.userMsg.firstProfessionalT},
            {id: 5, name: "毕业时间", value: this.userMsg.graduationTimeHeightT},
          ]
        },
        {id: 4, name: "其他信息", value: "",
          itemList: [
            {id: 1, name: "身份证号", value: this.userMsg.idCardT},
            {id: 2, name: "出生日期", value: this.userMsg.birthdayT},
            {id: 3, name: "性别", value: this.userMsg.sexTStr},
            {id: 4, name: "民族", value: this.userMsg.nationT},
            {id: 5, name: "籍贯", value: this.userMsg.hometownT},
            {id: 6, name: "政治面貌", value: this.userMsg.politicalStatusT},
            {id: 7, name: "婚姻状况", value: this.userMsg.marriageTStr},
          ]
        }
      ];
    }
  }
}
</script>
