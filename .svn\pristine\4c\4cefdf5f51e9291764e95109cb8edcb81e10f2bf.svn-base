<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">维护历史</h3>
    </div>
    <div class="card">
      <div class="card-body p-3">
        <div class="font-size-lg font-weight-bolder">
          <div>公司:&nbsp;{{product.icloudCompanyName}}</div>
          <div class="pt-3" v-if="product && product.company">客户:&nbsp;{{product.company.name}}</div>
        </div>
        <div class="pt-3">
          <div class="font-size-lg">{{product.no}}</div>
          <div class="pt-1">{{product.name}}&nbsp;{{product.specification}}</div>
        </div>
        <div class="row pt-3 pb-3 border-bottom">
          <div class="col-4 text-left">
            当前库存:&nbsp;{{product.stocks ? product.stocks : 0}}
          </div>
          <div class="col-4 text-center">
            当前价格:&nbsp;{{product.price ? product.price : 0}}
          </div>
          <div class="col-4 text-right">
            生产周期:&nbsp;{{product.leadTime ? product.leadTime : 0}}
          </div>
        </div>
        <div class="alert-secondary ml-1 mr-1 mt-1 pl-3 pr-3" v-if="historyList && historyList.length > 0">
          <div class="timeline timeline-2">
            <div class="timeline-bar"></div>
            <template v-for="item in historyList">
              <div class="timeline-item" :key="item.recordId">
                <div class="timeline-badge bg-success"></div>
                <div class="timeline-content">
                  <div class="d-flex align-items-center justify-content-between">
                    <span class="mr-3">产品维护&nbsp;{{item.createdDate}}</span>
                    <span class="text-muted text-right">操作人:&nbsp;{{item.userName}}</span>
                  </div>
                  <div class="text-danger pt-1">
                    <span v-if="item.initStocks != item.stocks">库存:&nbsp;{{item.initStocks}}-{{item.stocks}}&nbsp;&nbsp;&nbsp;</span>
                    <span v-if="item.initPrice != item.price">价格:&nbsp;{{item.initPrice}}-{{item.price}}&nbsp;&nbsp;&nbsp;</span>
                    <span v-if="item.initLeadTime != item.leadTime">生产周期:&nbsp;{{item.initLeadTime}}-{{item.leadTime}}&nbsp;&nbsp;&nbsp;</span>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div style="height: 100px;"></div>
  </div>
</template>

<script>
export default {
  name: "sotck_history",
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.recordId = this.$route.query.recordId;
      if (this.recordId){
        this.companyId = this.$route.query.companyId;
        this.erpId = this.$route.query.erpId;
        this.pageNo = 0;
        this.getProductDeail();
      }else {
        alert("请刷新重试");
      }
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return {
      recordId: "",
      companyId: "",
      erpId: "",
      historyList: [],
      product: {},
    }
  },
  methods:{
    getProductDeail: function (){
      this.product = {};
      const entity = {};
      entity.recordId = this.recordId;
      entity.erpId = this.erpId;
      entity.companyId = this.companyId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/getCustomerProduct", entity).then(result => {
        if (result && result.data){
          this.product = result.data;
          this.historyList = this.product.stockRecordList;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    }
  }
}
</script>