<template>
  <div class="bg-white py-5 px-4 rounded-xl shadow-sm">
    <div class="row">
      <div class="col-6">
        <FunctionEvent
            title="今日钜惠"
            tag="更多"
            productName="铝基板U6-2835-180PCS-30S3PX2-K1.0厚1铜35λ1A2"
            :originalPrice="299"
            :discountedPrice="99"
            :remainingStock="45"
            :totalStock="200"
            timeLimitText="仅剩3小时"
            buyButtonText="立即下单"
            @buy="handleBuy"
        />
      </div>
      <div class="col-6">
        <FunctionBuy
            title="待采清单"
            tag="更多"
            productName="铝基/航宇1200*1000"
            :originalPrice="299"
            :discountedPrice="99"
            :remainingStock="45"
            :totalStock="200"
            timeLimitText="仅剩3天"
            buyButtonText="立即报价"
            @buy="handleBuy"
        />
      </div>
    </div>
  </div>
</template>

<script>
import FunctionEvent from '@/view/pages/wx/business/mainUtils/FunctionEvent.vue';
import FunctionBuy from '@/view/pages/wx/business/mainUtils/FunctionBuy.vue';
export default {
  name: "Plaza",
  components: {
    FunctionEvent,
    FunctionBuy
  },
  methods: {
    handleBuy() {
      // 处理购买逻辑
      console.log('用户点击了购买按钮');
    }
  }
}
</script>

<style scoped>

</style>
