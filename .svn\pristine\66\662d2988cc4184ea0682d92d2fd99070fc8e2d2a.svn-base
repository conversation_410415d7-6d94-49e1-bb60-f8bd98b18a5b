import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

export default new Router({
  mode: 'history',
  base: process.env.NODE_ENV === "production" ? "/" : "/",
  routes: [
    {
      // 报表系统
      path: "/report",
      name: "report",
      component: () => import("@/view/pages/wx/report/report_main"),
    },
    {
      // 工作台
      path: "/worktable",
      component: () => import("@/view/layout/reportLayout"),
      children: [
        {
          path: "/worktable",
          name: "worktable",
          component: () => import("@/view/pages/wx/worktable")
        }
      ]
    },
    {
      // 小易助手
      path: "/work",
      component: () => import("@/view/layout/SystemLayout"),
      children: [
        {
          path: "/work",
          name: "work",
          component: () => import("@/view/pages/wx/kybsoft/work/work")
        },
        {
          path: "/submit",
          name: "submit",
          component: () => import("@/view/pages/wx/kybsoft/submit/submit")
        },
        {
          path: "/contract",
          name: "contract",
          component: () => import("@/view/pages/wx/kybsoft/contract/contract")
        },
        {
          path: "/my",
          name: "my",
          component: () => import("@/view/pages/wx/kybsoft/my/my")
        },
      ]
    },
    {
      // 小易助手-我的
      path: "/my/message",
      component: () => import("@/view/layout/SystemLayout"),
      children: [
        {
          path: "/my/message",
          name: "message",
          component: () => import("@/view/pages/wx/kybsoft/my/message")
        },
        {
          path: "/my/logo",
          name: "logo",
          component: () => import("@/view/pages/wx/kybsoft/my/logo")
        },
        {
          path: "/my/password",
          name: "password",
          component: () => import("@/view/pages/wx/kybsoft/my/password")
        },
        {
          path: "/my/phone",
          name: "phone",
          component: () => import("@/view/pages/wx/kybsoft/my/phone")
        },
        {
          path: "/my/notice",
          name: "notice",
          component: () => import("@/view/pages/wx/kybsoft/my/notice")
        },
        {
          path: "/my/myGroup",
          name: "myGroup",
          component: () => import("@/view/pages/wx/kybsoft/my/myGroup")
        },
        {
          path: "/my/myPosition",
          name: "myPosition",
          component: () => import("@/view/pages/wx/kybsoft/my/myPosition")
        },
      ]
    },
    {
      // 大生态圈-我的
      path: "/b/userMsg",
      component: () => import("@/view/layout/SystemLayout"),
      children: [
        {
          path: "/b/userMsg",
          name: "userMsg",
          component: () => import("@/view/pages/wx/business/page/person/main/userMsg")
        },
        {
          path: "/b/company",
          name: "company",
          component: () => import("@/view/pages/wx/business/page/person/main/company")
        },
        {
          path: "/b/company/companyDetails",
          name: "companyDetails",
          component: () => import("@/view/pages/wx/business/page/person/main/companyDetails")
        },
        {
          path: "/b/company/manageEmp",
          name: "manageEmp",
          component: () => import("@/view/pages/wx/business/page/person/main/manageEmp")
        },
        {
          path: "/b/partner",
          name: "partner",
          component: () => import("@/view/pages/wx/business/page/person/main/partner")
        },
        {
          path: "/b/service",
          name: "service",
          component: () => import("@/view/pages/wx/business/page/person/main/service")
        },
        {
          path: "/b/help",
          name: "help",
          component: () => import("@/view/pages/wx/business/page/person/main/help")
        },
        {
          path: "/b/historyBack",
          name: "historyBack",
          component: () => import("@/view/pages/wx/business/page/person/main/historyBack")
        },
        {
          path: "/b/historyDemand",
          name: "historyDemand",
          component: () => import("@/view/pages/wx/business/page/person/main/historyDemand")
        },
      ]
    },
    {
      path: "/b/productInquiry",
      component: () => import("@/view/layout/SystemLayout"),
      children: [
        {
          path: "/b/productInquiry",
          name: "productInquiry",
          component: () => import("@/view/pages/wx/business/page/main/productInQuiry")
        },
        {
          path: "/b/mainQuotation",
          name: "mainQuotation",
          component: () => import("@/view/pages/wx/business/page/main/mainQuotation"),
        },
        {
          path: "/b/mainQuotation/productQuotation",
          name: "productQuotation",
          component: () => import("@/view/pages/wx/business/page/main/productQuotation")
        },
        {
          path: "/b/mainQuotation/rawMaterialQuotation",
          name: "rawMaterialQuotation",
          component: () => import("@/view/pages/wx/business/page/main/rawMaterialQuotation")
        },
        {
          path: "/b/mainQuotation/rawMaterialQuotation/sotck_history",
          name: "sotck_history",
          component: () => import("@/view/pages/wx/business/page/main/sotck_history")
        },
        {
          path: "/b/mainBiddingHall",
          name: "mainBiddingHall",
          component: () => import("@/view/pages/wx/business/page/main/startInquiry"),
        },
        {
          path: "/b/mainMantence",
          name: "mainMantence",
          component: () => import("@/view/pages/wx/business/page/main/mainMantence"),
        },
        {
          path: "/b/mainMantence/waiteRepair",
          name: "waiteRepair",
          component: () => import("@/view/pages/wx/business/page/main/detail/waiteRepair"),
        },
        {
          path: "/b/mainMantence/timePackage",
          name: "timePackage",
          component: () => import("@/view/pages/wx/business/page/main/detail/timePackage"),
        },
        {
          path: "/b/mainMantence/timeReport",
          name: "timeReport",
          component: () => import("@/view/pages/wx/business/page/main/detail/timeReport"),
        },
        {
          path: "/b/mainMantence/inputEquipment",
          name: "inputEquipment",
          component: () => import("@/view/pages/wx/business/page/main/detail/inputEquipment"),
        },
        {
          path: "/b/mainMantence/equipmentManage",
          name: "inputEquipment",
          component: () => import("@/view/pages/wx/business/page/main/detail/equipmentManage"),
        },
      ]
    },
    {
      // 小易助手-任务明细
      path: "/task/taskDetails",
      component: () => import("@/view/layout/SystemLayout"),
      children: [
        {
          path: "/task/taskDetails",
          name: "taskDetails",
          component: () => import("@/view/pages/wx/kybsoft/work/task/details/taskDetails")
        },
      ]
    },
    {
      // 小易助手-报告明细
      path: "/report/reportDetails",
      component: () => import("@/view/layout/SystemLayout"),
      children: [
        {
          path: "/report/reportDetails",
          name: "reportDetails",
          component: () => import("@/view/pages/wx/kybsoft/work/report/details/reportDetails")
        },
      ]
    },
    {
      // 小易助手-日程明细
      path: "/dayThing/dayThingDetails",
      component: () => import("@/view/layout/SystemLayout"),
      children: [
        {
          path: "/dayThing/dayThingDetails",
          name: "dayThingDetails",
          component: () => import("@/view/pages/wx/kybsoft/work/dayThing/details/dayThingDetails")
        },
      ]
    },
    {
      // 小易助手-审批明细
      path: "/audit/auditDetails",
      component: () => import("@/view/layout/SystemLayout"),
      children: [
        {
          path: "/audit/auditDetails",
          name: "auditDetails",
          component: () => import("@/view/pages/wx/kybsoft/work/audit/details/auditDetails")
        },
      ]
    },
    {
      // 账户中心
      path: "/callback",
      component: () => import("@/view/pages/wx/auth/Auth"),
      children: [
        {
          name: "callback",
          path: "/callback",
          component: () => import("@/view/pages/wx/auth/callback")
        },
        {
          name: "userAuth",
          path: "/userAuth",
          component: () => import("@/view/pages/wx/auth/userAuth")
        }
      ]
    },
    {
      // 简介
      path: "/README",
      component: () => import("@/view/layout/Layout"),
      children: [
        {
          path: "/README",
          name: "README",
          component: () => import("@/view/pages/wx/README/README")
        }
      ]
    },
    {
      path: "/noticeList",
      component: () => import("@/view/layout/Layout"),
      children: [
        {
          path: "/noticeList",
          name: "noticeList",
          component: () => import("@/view/pages/wx/README/noticeList")
        }
      ]
    },
    {
      path: "/mechanism",
      component: () => import("@/view/layout/Layout"),
      children: [
        {
          path: "/mechanism",
          name: "mechanism",
          component: () => import("@/view/pages/wx/README/mechanism")
        }
      ]
    },
    {
      path: "/performanceDetails",
      component: () => import("@/view/layout/Layout"),
      children: [
        {
          path: "/performanceDetails",
          name: "performanceDetails",
          component: () => import("@/view/pages/wx/README/performanceDetails")
        },
      ]
    },
    {
      // 物流系统
      path: "/logistics",
      component: () => import("@/view/layout/Layout"),
      children: [
        {
          path: "/logistics",
          name: "logistics",
          component: () => import("@/view/pages/wx/logistics/logistics")
        }
      ]
    },
    {
      // MES工具
      path: "/scanWork",
      component: () => import("@/view/layout/Layout"),
      children: [
        {
          path: "/scanWork",
          name: "scanWork",
          component: () => import("@/view/pages/wx/produce/scanWork")
        },
        {
          path: "/produce",
          name: "produce",
          component: () => import("@/view/pages/wx/produce/work/produce")
        },
        {
          path: "/countList",
          name: "countList",
          component: () => import("@/view/pages/wx/produce/work/countList")
        },
        {
          path: "/information",
          name: "information",
          component: () => import("@/view/pages/wx/produce/work/information")
        },
      ]
    },
    {
      // 大生态圈
      path: "/business",
      component: () => import("@/view/layout/SystemLayout"),
      children: [
        {
          path: "/business",
          name: "business",
          component: () => import("@/view/pages/wx/business/business")
        },
        {
          path: "/orderList",
          name: "orderList",
          component: () => import("@/view/pages/wx/business/page/orderList/OrderList")
        },
        {
          path: "/bus_person",
          name: "bus_person",
          component: () => import("@/view/pages/wx/business/page/person/bus_person")
        },
        {
          path: "/PublishDemand",
          name: "PublishDemand",
          component: () => import("@/view/pages/wx/business/page/PublishDemand/PublishDemand")
        },
        {
          path: "/quotation",
          name: "quotation",
          component: () => import("@/view/pages/wx/business/page/quotation/quotation")
        },
      ]
    },
    {
      // 辅助页面跳转
      path: "/jumpNode",
      name: "jumpNode",
      component: () => import("@/view/pages/utils/jumpNode")
    },
    {
      path: "*",
      redirect: "/404"
    },
    {
      // the 404 route, when none of the above matches
      path: "/404",
      name: "404",
      component: () => import("@/view/pages/error/Error-6")
    }
  ]
});
