<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">产品询价</h3>
    </div>
    <div class="card">
      <div class="card-body p-3">
        <div class="border-bottom pb-3">
          <div class="font-size-lg font-weight-bolder">产品应用</div>
          <template v-for="item in productTypeList">
            <label class="checkbox" :key="item.recordId">
              <input type="checkbox" class="form-control" id="position" :value="item.checked" v-model="item.checked" v-on:change="checkboxChange(item, productTypeList)"/>
              <span></span>
              <label class="pl-3 pt-2">{{item.value}}</label>
            </label>
          </template>
        </div>
        <div class="border-bottom pt-3 pb-3">
          <div class="d-flex justify-content-between align-items-center">
            <div class="font-weight-bolder font-size-lg">筛选工艺</div>
            <button class="btn btn-sm btn-primary px-3 py-1" v-on:click="addCraft()">筛选</button>
          </div>
          <div class="text-muted pt-3" v-if="craftMaterialValue">{{craftMaterialValue}}</div>
        </div>
        <div class="border-bottom pt-3 pb-3">
          <div class="d-flex justify-content-between align-items-center">
            <div class="font-weight-bolder font-size-lg">询价清单</div>
            <button class="btn btn-sm btn-primary px-3 py-1" v-on:click="downloadPrint">下载</button>
          </div>
        </div>
        <template v-for="(item, index) in quotationDetailList">
          <div class="border-bottom pt-3 pb-3" :key="item.recordId">
            <div class="font-weight-bolder">
              {{item.concatValue}}
            </div>
            <div class="d-flex justify-content-between align-items-center pt-3" v-if="item.fixedOffer">
              <div>平米单价：￥{{item.profitPrice ? item.profitPrice : item.fixedOffer}}/㎡</div>
              <button class="btn btn-sm btn-danger px-1 py-1" v-on:click="delQuotationDetail(index)">删除</button>
            </div>
            <div v-else>
              <div class="d-flex justify-content-end pt-1">
                <button class="btn btn-sm btn-danger px-1 py-1" v-on:click="delQuotationDetail(index)">删除</button>
              </div>
              <template v-for="(row, recordId) in item.areaPriceList">
                <span class="pr-6" :key="recordId" v-if="row.areaPrice">{{row.areaSection}}:￥{{row.areaPrice}}/㎡</span>
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div style="height: 100px;"></div>
    <b-modal ref="addCraft" hide-footer>
      <template v-slot:modal-header="{ close }">
        <div class="font-size-lg font-weight-bolder">报价工艺</div>
        <div>
          <button class="btn btn-outline-danger px-2 py-1" @click="close">关闭</button>
          <button class="btn btn-outline-primary px-2 py-1 ml-3" @click="confirmSelection">确认</button>
        </div>
      </template>
      <div v-if="craftValue">{{craftValue}}</div>
      <div class="row">
        <div class="col-4">
          <div class="row" :style="auditFlag === 10 ? 'background-color: #b3d4f6;' : ''" v-if="exposurePrintingList && exposurePrintingList.length > 0">
            <div class="col-xl-12 pt-2 pb-2" v-on:click="selectCraftType(10)">曝光/丝印</div>
          </div>
          <div class="row" :style="auditFlag === 1 ? 'background-color: #b3d4f6;' : ''" v-if="boardLevelList && boardLevelList.length > 0">
            <div class="col-xl-12 pt-2 pb-2" v-on:click="selectCraftType(1)">PCB类型</div>
          </div>
          <div class="row" :style="auditFlag === 2 ? 'background-color: #b3d4f6;' : ''" v-if="materialTypeList && materialTypeList.length > 0">
            <div class="col-xl-12 pt-2 pb-2" v-on:click="selectCraftType(2)">覆铜板材</div>
          </div>
          <div class="row" :style="auditFlag === 3 ? 'background-color: #b3d4f6;' : ''" v-if="boardThicknessList && boardThicknessList.length > 0">
            <div class="col-xl-12 pt-2 pb-2" v-on:click="selectCraftType(3)">板材厚度</div>
          </div>
          <div class="row" :style="auditFlag === 4 ? 'background-color: #b3d4f6;' : ''" v-if="copperCladThicknessList && copperCladThicknessList.length > 0">
            <div class="col-xl-12 pt-2 pb-2" v-on:click="selectCraftType(4)">覆铜要求</div>
          </div>
          <div class="row" :style="auditFlag === 5 ? 'background-color: #b3d4f6;' : ''" v-if="surfaceProcessList && surfaceProcessList.length > 0">
            <div class="col-xl-12 pt-2 pb-2" v-on:click="selectCraftType(5)">镀层处理</div>
          </div>
          <div class="row" :style="auditFlag === 6 ? 'background-color: #b3d4f6;' : ''" v-if="solderMaskTypeList && solderMaskTypeList.length > 0">
            <div class="col-xl-12 pt-2 pb-2" v-on:click="selectCraftType(6)">阻焊类型</div>
          </div>
          <div class="row" :style="auditFlag === 7 ? 'background-color: #b3d4f6;' : ''" v-if="characterTypeList && characterTypeList.length > 0">
            <div class="col-xl-12 pt-2 pb-2" v-on:click="selectCraftType(7)">板面字符</div>
          </div>
          <div class="row" :style="auditFlag === 8 ? 'background-color: #b3d4f6;' : ''" v-if="shapingWayList && shapingWayList.length > 0">
            <div class="col-xl-12 pt-2 pb-2" v-on:click="selectCraftType(8)">成型方式</div>
          </div>
          <div class="row" :style="auditFlag === 9 ? 'background-color: #b3d4f6;' : ''" v-if="testMethodList && testMethodList.length > 0">
            <div class="col-xl-12 pt-2 pb-2" v-on:click="selectCraftType(9)">测试要求</div>
          </div>
        </div>
        <div class="col">
          <div class="row">
            <div class="col-xl-12" v-if="auditFlag === 10">
              <div class="checkbox-inline">
                <template v-for="item in exposurePrintingList">
                  <label class="checkbox p-3" :key="item.recordId">
                    <input type="checkbox" class="form-control" id="position" :value="item.checked" v-model="item.checked" v-on:change="handleCheckboxChange(item,exposurePrintingList)"/>
                    <span></span>
                    {{item.value}}
                  </label>
                </template>
              </div>
            </div>
            <div class="col-xl-12" v-if="auditFlag === 1">
              <div class="checkbox-inline">
                <template v-for="item in boardLevelList">
                  <label class="checkbox pb-1" :key="item.recordId" style="min-width: 7rem;">
                    <input type="checkbox" id="position" :value="item.checked" v-model="item.checked" v-on:change="handleCheckboxChange(item,boardLevelList)"/>
                    <span></span>
                    {{item.value}}
                  </label>
                </template>
              </div>
            </div>
            <div class="col-xl-12" v-if="auditFlag === 2">
              <div class="checkbox-inline">
                <template v-for="item in materialTypeList">
                  <label class="checkbox pb-1" :key="item.recordId" style="min-width: 7rem;">
                    <input type="checkbox" id="position" :value="item.checked" v-model="item.checked" v-on:change="handleCheckboxChange(item,materialTypeList)"/>
                    <span></span>
                    {{item.value}}
                  </label>
                </template>
              </div>
            </div>
            <div class="col-xl-12" v-if="auditFlag === 3">
              <div class="checkbox-inline">
                <template v-for="item in boardThicknessList">
                  <label class="checkbox pb-1" :key="item.recordId" style="min-width: 7rem;">
                    <input type="checkbox" class="form-control" id="position" :value="item.checked" v-model="item.checked" v-on:change="handleCheckboxChange(item,boardThicknessList)"/>
                    <span></span>
                    {{item.value}}
                  </label>
                </template>
              </div>
            </div>
            <div class="col-xl-12" v-if="auditFlag === 4">
              <div class="checkbox-inline">
                <template v-for="item in copperCladThicknessList">
                  <label class="checkbox pb-1" :key="item.recordId" style="min-width: 7rem;">
                    <input type="checkbox" class="form-control" id="position" :value="item.checked" v-model="item.checked" v-on:change="handleCheckboxChange(item,copperCladThicknessList)"/>
                    <span></span>
                    {{item.value}}
                  </label>
                </template>
              </div>
            </div>
            <div class="col-xl-12" v-if="auditFlag === 5">
              <div class="checkbox-inline">
                <template v-for="item in surfaceProcessList">
                  <label class="checkbox pb-1" :key="item.recordId" style="min-width: 7rem;">
                    <input type="checkbox" class="form-control" id="position" :value="item.checked" v-model="item.checked" v-on:change="handleCheckboxChange(item,surfaceProcessList)"/>
                    <span></span>
                    {{item.value}}
                  </label>
                </template>
              </div>
            </div>
            <div class="col-xl-12" v-if="auditFlag === 6">
              <div class="checkbox-inline">
                <template v-for="item in solderMaskTypeList">
                  <label class="checkbox pb-1" :key="item.recordId" style="min-width: 7rem;">
                    <input type="checkbox" class="form-control" id="position" :value="item.checked" v-model="item.checked" v-on:change="handleCheckboxChange(item,solderMaskTypeList)"/>
                    <span></span>
                    {{item.value}}
                  </label>
                </template>
              </div>
            </div>
            <div class="col-xl-12" v-if="auditFlag === 7">
              <div class="checkbox-inline">
                <template v-for="item in characterTypeList">
                  <label class="checkbox pb-1" :key="item.recordId" style="min-width: 7rem;">
                    <input type="checkbox" class="form-control" id="position" :value="item.checked" v-model="item.checked" v-on:change="handleCheckboxChange(item,characterTypeList)"/>
                    <span></span>
                    {{item.value}}
                  </label>
                </template>
              </div>
            </div>
            <div class="col-xl-12" v-if="auditFlag === 8">
              <div class="checkbox-inline">
                <template v-for="item in shapingWayList">
                  <label class="checkbox pb-1" :key="item.recordId" style="min-width: 7rem;">
                    <input type="checkbox" class="form-control" id="position" :value="item.checked" v-model="item.checked" v-on:change="handleCheckboxChange(item,shapingWayList)"/>
                    <span></span>
                    {{item.value}}
                  </label>
                </template>
              </div>
            </div>
            <div class="col-xl-12" v-if="auditFlag === 9">
              <div class="checkbox-inline">
                <template v-for="item in testMethodList">
                  <label class="checkbox pb-1" :key="item.recordId" style="min-width: 7rem;">
                    <input type="checkbox" class="form-control" id="position" :value="item.checked" v-model="item.checked" v-on:change="handleCheckboxChange(item,testMethodList)"/>
                    <span></span>
                    {{item.value}}
                  </label>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </b-modal>
    <div style="height: 100px;"></div>
  </div>
</template>

<script>
export default {
  name: "productInquiry",
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId) {
      this.userMsg = sysUser;
      this.loadData();
    } else {
      alert("请重新进入公众号");
    }
  },
  data() {
    return {
      quotationDetailList: [],
      detailId: "",
      boardLevelList: [], //PCB类型
      materialTypeList: [],// 覆铜板材
      boardThicknessList: [],// 板材厚度
      copperCladThicknessList: [], //覆铜要求
      surfaceProcessList: [], //镀层处理
      solderMaskTypeList: [], //阻焊类型
      characterTypeList: [], //板面字符
      shapingWayList: [], //成型方式
      testMethodList: [], //测试要求
      boardLeve: "",
      auditFlag: 10,
      craftValue: "",
      craftMaterialValue: "",
      craftMaterialValueList: [],
      productTypeList: [],
      showQuotationDetailList: [],
      exposurePrintingList: [],
      userMsg:{}
    }
  },
  methods:{
    loadData() {
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("is/offer/wxLoadMsg").then(result => {
        if (result && result.data){
          this.productTypeList = result.data.productTypeList;
          this.getQuotationDetailList();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    getQuotationDetailList(profession) {
      const query = {};
      if (profession) {
        query.profession = profession;
      }
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("is/offer/getWxQuotationDetailList", query).then(result => {
        if (result && result.data){
          this.quotationDetailList = result.data.quotationDetailList;
          this.showQuotationDetailList = result.data.quotationDetailList;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    checkboxChange(item, list) {
      let result = "";
      for (let i = 0; i < list.length; i++) {
        if (list[i].checked) {
          if (result == "") {
            result = list[i].recordId;
          } else {
            result = result + "," + list[i].recordId;
          }
        }
      }
      this.getQuotationDetailList(result);
    },
    delQuotationDetail(index) {
      this.quotationDetailList.splice(index, 1);
    },
    setCheck: function () {
      if (this.craftMaterialValueList && this.craftMaterialValueList.length > 0) {
        for (let craftMaterialValue of this.craftMaterialValueList) {
          for (let value of craftMaterialValue.valueList) {
            this.setChildCheck(value, this.boardLevelList);
            this.setChildCheck(value, this.materialTypeList);
            this.setChildCheck(value, this.boardThicknessList);
            this.setChildCheck(value, this.copperCladThicknessList);
            this.setChildCheck(value, this.surfaceProcessList);

            this.setChildCheck(value, this.solderMaskTypeList);
            this.setChildCheck(value, this.characterTypeList);
            this.setChildCheck(value, this.shapingWayList);
            this.setChildCheck(value, this.testMethodList);
            this.setChildCheck(value, this.exposurePrintingList);
          }
        }
      }
    },
    setChildCheck: function (value, list) {
      if (list && list.length > 0) {
        for (let obj of list) {
          if (obj.recordId == value.recordId) {
            this.$set(obj, 'checked', true);
            break;
          }
        }
      }
    },
    addCraft() {
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("is/offer/loadCraftList", {}).then(result => {
        if (result && result.data){
          this.boardLevelList = result.data.boardLevelList;
          this.materialTypeList = result.data.materialTypeList;
          this.boardThicknessList = result.data.boardThicknessList;
          this.copperCladThicknessList = result.data.copperCladThicknessList;
          this.surfaceProcessList = result.data.surfaceProcessList;

          this.solderMaskTypeList = result.data.solderMaskTypeList;
          this.characterTypeList = result.data.characterTypeList;
          this.shapingWayList = result.data.shapingWayList;
          this.testMethodList = result.data.testMethodList;
          //定义曝光、丝印集合
          this.exposurePrintingList = this.makeExposurePrintingList();
          this.setCheck();
          this.$refs['addCraft'].show();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {
        console.log(err)
      });
    },
    makeExposurePrintingList: function () {
      let exposurePrintingList = [];
      let query = {};
      query.value = "曝光";
      query.enName = "Exposure";
      query.recordId = "1"
      query.item = {};
      query.item.itemName = "曝光/丝印";
      query.item.enName = "ExposurePrinting";
      query.item.recordId = "1";
      exposurePrintingList.push(query);

      let queryTwo = {};
      queryTwo.value = "丝印";
      queryTwo.enName = "Screen printing";
      queryTwo.recordId = "2";
      queryTwo.item = {};
      queryTwo.item.itemName = "曝光/丝印";
      queryTwo.item.enName = "ExposurePrinting";
      queryTwo.item.recordId = "1";
      exposurePrintingList.push(queryTwo);
      return exposurePrintingList;
    },
    selectCraftType: function (num) {
      if (num && Number(num) > 0) {
        this.auditFlag = num;
      }
    },
    handleCheckboxChange(selectedOption, itemList) {
      let valueList = [];
      for (let i = 0; i < itemList.length; i++) {
        if (itemList[i].checked) {
          valueList.push(itemList[i]);
        }
      }
      let item = {};
      if (this.craftMaterialValueList && this.craftMaterialValueList.length > 0) {
        for (let craftMaterialValue of this.craftMaterialValueList) {
          if (craftMaterialValue.recordId == selectedOption.item.recordId) {
            item = craftMaterialValue;
            break;
          }
        }
      }
      item.valueList = valueList;
      if (!item.recordId) {
        item.recordId = selectedOption.item.recordId;
        item.itemName = selectedOption.item.itemName;
        this.craftMaterialValueList.push(item);
      }
      this.craftValue = "";
      for (let craftT of this.craftMaterialValueList) {
        if (craftT.valueList && craftT.valueList.length > 0) {
          if (this.craftValue) {
            this.craftValue = this.craftValue + " " + craftT.itemName + " ";
          } else {
            this.craftValue = craftT.itemName + " ";
          }
        }
        let i = 0;
        for (let value of craftT.valueList) {
          if (i == 0) {
            this.craftValue = this.craftValue + " " + value.value;
          } else {
            this.craftValue = this.craftValue + "," + value.value;
          }
          i++;
        }
      }
    },
    confirmSelection() {
      this.$refs['addCraft'].hide();
      this.craftMaterialValue = this.craftValue;
      this.quotationDetailList = this.showQuotationDetailList;
      let list = [];
      let i = 0;
      for (let craftMaterialValue of this.craftMaterialValueList) {
        if (craftMaterialValue.valueList && craftMaterialValue.valueList.length > 0) {
          i++;
          for (let value of craftMaterialValue.valueList) {
            list = list.concat(this.filterCollectionByCraft(value.value, this.quotationDetailList));
          }
          this.quotationDetailList = list;
          list = [];
        } else if (i == 0) {
          this.quotationDetailList = this.showQuotationDetailList;
        }
      }
    },
    filterCollectionByCraft(craftValue, collectionList) {
      let listT = [];
      for (let item of collectionList) {
        let flag = false;
        let array = [];
        array = item.concatValue.split(" ");
        for (let list of array) {
          if (list == craftValue) {
            flag = true;
          }
        }
        if (flag) {
          listT.push(item);
        }
      }
      return listT;
    },
    downloadPrint() {
      const query = {};
      this.detailId = "";
      for (let detail of this.quotationDetailList) {
        if (this.detailId === "") {
          this.detailId = detail.recordId;
        } else {
          this.detailId = this.detailId + "," + detail.recordId;
        }
      }
      query.detailId = this.detailId;
      const icloudUser = {}
      icloudUser.userName = this.userMsg.userName;
      icloudUser.phone = this.userMsg.phone;
      icloudUser.email = this.userMsg.email;
      icloudUser.country = this.userMsg.country;
      query.icloudUser = icloudUser;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPostArray("kybAudit/onlinePrint", query).then(data => {
        this.$parent.enableLoadFlag(false);
        const headers = data.headers;
        const a = document.createElement("a");
        const file = new Blob([data.data], {type: "application/pdf"});
        const fileURL = (window.URL || window.webkitURL).createObjectURL(file);
        a.href = fileURL;
        a.download = decodeURI(headers.title);
        document.body.appendChild(a);
        a.click();
        setTimeout(function () {document.body.removeChild(a);URL.revokeObjectURL(fileURL);}, 100);
      }).catch(err => {console.log(err);});
    }
  }
}
</script>