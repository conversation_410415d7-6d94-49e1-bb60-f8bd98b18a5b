<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">历史需求</h3>
    </div>
    <div class="card mb-3">
      <div class="card-body p-3">
        <div class="input-group input-group-solid">
          <span class="pl-3 font-weight-bolder">筛选：</span>
          <input class="form-control" v-model="searchContent" v-on:change="loadData">
        </div>
      </div>
    </div>
    <template v-for="(row, index) in historyDemandList">
      <div class="bg-white p-3 mb-3" :key="index">
        <div class="d-flex justify-content-between align-items-center border-bottom pb-2" style="font-size: 1.3rem;">
          <div class="text-primary">XQ00{{ row.recordId }}</div>
          <div>
            <span class="badge badge-danger" v-if="row.status === '1001'">待审核</span>
            <span class="badge badge-primary" v-if="row.status === '1002'">已审核</span>
          </div>
        </div>
        <div class="d-flex justify-content-between align-items-center font-size-lg pt-1">
          <div><span class="text-muted">物&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;品：</span>{{row.title}}</div>
          <div class="pl-3"><span class="text-muted">类&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;型：</span>{{row.typeStr}}</div>
          <div class="pl-3"><span class="text-muted">数&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;量：</span>{{row.quantity}}</div>
        </div>
        <div class="d-flex justify-content-between align-items-center pt-1 font-size-lg">
          <div>
            <span class="text-muted">预算范围：</span>{{row.minBudget}} 至 {{row.maxBudget}} {{row.budgetUnitStr}}
          </div>
          <div class="d-flex align-items-center pt-1 font-size-lg">
            <span class="text-muted">期望完成日期:</span>
            <div class="pl-3">
              {{ new Date(row.expectedDate).toLocaleDateString('zh-CN', {year: 'numeric', month: '2-digit', day: '2-digit'}) }}
            </div>
          </div>
        </div>
        <div class="d-flex align-items-center pt-1 font-size-lg">
          <div class="text-muted">问题描述:</div>
          <div class="pl-3">{{row.description}}</div>
        </div>
        <div class="d-flex align-items-center pt-1 font-size-lg">
          <div><span class="text-muted">创建时间：</span>{{row.createdDate}}</div>
        </div>
        <div class="d-flex justify-content-between align-items-center pt-1 font-size-lg">
          <div><span class="text-muted">联系人：</span>{{row.contactName}}</div>
          <div><span class="text-muted">手机号码：</span>{{row.contactPhone}}</div>
        </div>
      </div>
    </template>
    <div style="height: 100px;"></div>
  </div>
</template>

<script>
export default {
name: "historyDemand",
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      //加载历史需求
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data()
  {
    return{
      userMsg:{},
      historyDemandList:[],
      searchContent:"",
    }
  },
  methods: {
    loadData() {
      const query = {};
      query.recordId = this.userMsg.recordId;
      query.searchContent = this.searchContent;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/getHistoryDemandList",query).then(result => {
        if(result.data){
          this.historyDemandList = result.data;
        }else {
          alert("请刷新重试");
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    }
  }
}
</script>