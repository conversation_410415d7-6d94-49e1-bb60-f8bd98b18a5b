<template>
  <div>
    <div v-if="showAuditData && showAuditData.supplier">
      <div>
        <div class="d-flex justify-content-between align-items-center pb-1 border-bottom">
          <div class="font-weight-bolder">供应商资料</div>
          <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlag()">{{ flag && flag == '1' ? '收起' : '展开' }}</button>
        </div>
        <div v-if="flag == '1'">
          <div class="pt-1 font-weight-bolder text-primary">供应商全称:{{showAuditData.supplier.name}}</div>
          <div class="pt-1" style="word-break: break-all;">
            <span>编号:{{getVal(showAuditData.supplier.no)}}</span>
            <span class="pl-2">简称:{{getVal(showAuditData.supplier.shortName)}}</span>
            <span class="pl-2">状态:{{showAuditData.supplier.status == 60001 ? '审批中' :(showAuditData.supplier.status == 100301 ? '未审核' : '已审核')}}</span>
          </div>
          <div class="pt-1" style="word-break: break-all;">
            <span>电话:{{getVal(showAuditData.supplier.phone)}}</span>
            <span class="pl-2">传真:{{getVal(showAuditData.supplier.fax)}}</span>
            <span class="pl-2">邮编:{{getVal(showAuditData.supplier.zip)}}</span>
            <span class="pl-2">区域:{{getVal(showAuditData.supplier.areaCode)}}</span>
          </div>
          <div class="pt-1" style="word-break: break-all;">
            <span>网址:{{getVal(showAuditData.supplier.website)}}</span>
            <span class="pl-2">联系人:{{getVal(showAuditData.supplier.bizPerson)}}</span>
          </div>
          <div class="pt-1">地址:{{getVal(showAuditData.supplier.address)}}</div>
          <div class="pt-2" style="word-break: break-all;">
            <span>对账日期:<span class="text-primary">{{getVal(showAuditData.supplier.checkDate)}}号</span></span>
            <span class="pl-2">结算货币:<span class="text-primary">{{showAuditData.supplier.currencyType ? showAuditData.supplier.currencyType.value : '无'}}</span></span>
            <span class="pl-2">结算方式:<span class="text-primary">{{showAuditData.supplier.payWay ? showAuditData.supplier.payWay.value : '无'}}</span></span>
            <span class="pl-2">付款方式:<span class="text-primary">{{getVal(showAuditData.supplier.paycauseValue)}}</span></span>
            <span class="pl-2">发票类型:<span class="text-primary">{{showAuditData.supplier.taxDescript ? showAuditData.supplier.taxDescript.value : '无'}}</span></span>
            <span class="pl-2">交货方式:<span class="text-primary">{{showAuditData.supplier.freightWay ? showAuditData.supplier.freightWay.value : '无'}}</span></span>
            <span class="pl-2">送货方式:<span class="text-primary">{{showAuditData.supplier.deliveryWay ? showAuditData.supplier.deliveryWay.value : '无'}}</span></span>
            <span class="pl-2">业务费月结方式:<span class="text-primary">{{showAuditData.supplier.businessPayment ? showAuditData.supplier.businessPayment.value : '无'}}</span></span>
          </div>
          <div class="pt-2" style="word-break: break-all;">
            <span>公司类型:<span class="text-primary">{{getVal(showAuditData.supplier.companyType)}}</span></span>
            <span class="pl-2">供应类型:<span class="text-primary">{{showAuditData.supplier.suppliertype ? showAuditData.supplier.suppliertype.value : '无'}}</span></span>
            <span class="pl-2">品牌:<span class="text-primary">{{getVal(showAuditData.supplier.itemName)}}</span></span>
            <span class="pl-2">供应品牌:<span class="text-primary">{{getVal(showAuditData.supplier.supplyBrandName)}}</span></span>
            <span class="pl-2">供应商种类:<span class="text-primary">{{getVal(showAuditData.supplier.materialKindStr)}}</span></span>
            <span class="pl-2">所属行业:<span class="text-primary">{{getVal(showAuditData.supplier.industryName)}}</span></span>
            <span class="pl-2">开户银行:<span class="text-primary">{{getVal(showAuditData.supplier.bank)}}</span></span>
            <span class="pl-2">银行账号:<span class="text-primary">{{getVal(showAuditData.supplier.account)}}</span></span>
            <span class="pl-2">法人代表:<span class="text-primary">{{getVal(showAuditData.supplier.legalPerson)}}</span></span>
          </div>
          <div class="pt-2" style="word-break: break-all;">
            <span>供应商等级:<span class="text-primary">{{showAuditData.supplier.supplierlevel ? showAuditData.supplier.supplierlevel.value : '无'}}</span></span>
            <span class="pl-2">供应商分类:<span class="text-primary">{{getVal(showAuditData.supplier.classificationValue)}}</span></span>
            <span class="pl-2">大生态圈企业:<span class="text-primary">{{getVal(showAuditData.supplier.icloudCompanyName)}}</span></span>
            <span class="pl-2">创建日期:<span class="text-primary">{{getVal(showAuditData.supplier.createdDate)}}</span></span>
            <span class="pl-2">质保期限:<span class="text-primary">{{showAuditData.supplier.payDays ? showAuditData.supplier.payDays.value : '无'}}</span></span>
            <span class="pl-2">质量标准:<span class="text-primary">{{getVal(showAuditData.supplier.qualityStd)}}</span></span>
            <span class="pl-2">默认材料供应商:<span class="text-primary">{{showAuditData.supplier.isDefault == 1 ? '是' : '否'}}</span></span>
          </div>
          <div class="pt-1" style="word-break: break-all;">特殊类型备注:{{getVal(showAuditData.supplier.specialRemark)}}</div>
          <div class="pt-1" style="word-break: break-all;">供应商打印备注:{{getVal(showAuditData.supplier.printRemark)}}</div>
          <div class="pt-1" style="word-break: break-all;">备注:{{getVal(showAuditData.supplier.remark)}}</div>
        </div>
      </div>
      <div>
        <div class="d-flex justify-content-between align-items-center pb-1 border-bottom" v-if="oneFlag">
          <div class="font-weight-bolder">供应商资料比较</div>
          <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlagTwo">{{ flagTwo && flagTwo == '1' ? '收起' : '展开' }}</button>
        </div>
        <div v-if="flagTwo == '1' && oneFlag">
          <div class="d-flex justify-content-between">
            <div>
              <div>
                {{showAuditData.supplierTop.nameTop}}
              </div>
              <div>
                状态:{{showAuditData.supplierTop.status == 100401 ? '未审核' : (showAuditData.supplierTop.status == 100402 ? '已审核' : (showAuditData.supplierTop.status == 100403 ? '已失效' : '审批中'))}}
              </div>
              <div v-if="showAuditData.supplierTop.shortName != showAuditData.supplierAfter.shortName">
                简称:{{getVal(showAuditData.supplierTop.shortName)}}
              </div>
              <div v-if="showAuditData.supplierTop.name != showAuditData.supplierAfter.name">
                全称:{{getVal(showAuditData.supplierTop.name)}}
              </div>
              <div v-if="showAuditData.supplierTop.areaCode != showAuditData.supplierAfter.areaCode">
                区域:{{getVal(showAuditData.supplierTop.areaCode)}}
              </div>
              <div v-if="showAuditData.supplierTop.address != showAuditData.supplierAfter.address">
                地址:{{getVal(showAuditData.supplierTop.address)}}
              </div>
              <div v-if="showAuditData.supplierTop.zip != showAuditData.supplierAfter.zip">
                邮编:{{getVal(showAuditData.supplierTop.zip)}}
              </div>
              <div v-if="showAuditData.supplierTop.phone != showAuditData.supplierAfter.phone">
                电话:{{getVal(showAuditData.supplierTop.phone)}}
              </div>
              <div v-if="showAuditData.supplierTop.fax != showAuditData.supplierAfter.fax">
                传真:{{getVal(showAuditData.supplierTop.fax)}}
              </div>
              <div v-if="showAuditData.supplierTop.bank != showAuditData.supplierAfter.bank">
                开户银行:{{getVal(showAuditData.supplierTop.bank)}}
              </div>
              <div v-if="showAuditData.supplierTop.account != showAuditData.supplierAfter.account">
                银行帐号:{{getVal(showAuditData.supplierTop.account)}}
              </div>
              <div v-if="showAuditData.supplierTop.checkDate != showAuditData.supplierAfter.checkDate">
                对帐日期:{{getVal(showAuditData.supplierTop.checkDate)}}
              </div>
              <div v-if="showAuditData.supplierTop.payWay && showAuditData.supplierTop.payWay.value != showAuditData.supplierAfter.payWayValue">
                结算方式:{{showAuditData.supplierTop.payWay ? showAuditData.supplierTop.payWay.value : '无'}}
              </div>
              <div v-if="showAuditData.supplierTop.deliveryWay && showAuditData.supplierTop.deliveryWay.value != showAuditData.supplierAfter.deliveryWayValue">
                送货方式:{{showAuditData.supplierTop.deliveryWay ? showAuditData.supplierTop.deliveryWay.value : '无'}}
              </div>
              <div v-if="showAuditData.supplierTop.businessPayment && showAuditData.supplierTop.businessPayment.value != showAuditData.supplierAfter.businessPaymentValue">
                业务费月结方式:{{showAuditData.supplierTop.businessPayment ? showAuditData.supplierTop.businessPayment.value : '无'}}
              </div>
              <div v-if="showAuditData.supplierTop.freightWay && showAuditData.supplierTop.freightWay.value != showAuditData.supplierAfter.freightWayValue">
                交货方式:{{showAuditData.supplierTop.freightWay ? showAuditData.supplierTop.freightWay.value : '无'}}
              </div>
              <div v-if="showAuditData.supplierTop.taxDescript && showAuditData.supplierTop.taxDescript.value != showAuditData.supplierAfter.taxDescriptValue">
                发票类型:{{showAuditData.supplierTop.taxDescript ? showAuditData.supplierTop.taxDescript.value : '无'}}
              </div>
              <div v-if="showAuditData.supplierTop.payDays && showAuditData.supplierAfter.payDays && showAuditData.supplierTop.payDays.value != showAuditData.supplierAfter.payDays.value">
                质保期限:{{showAuditData.supplierTop.payDays ? showAuditData.supplierTop.payDays.value : '无'}}
              </div>
              <div v-if="showAuditData.supplierTop.legalPerson != showAuditData.supplierAfter.legalPerson">
                法人代表:{{getVal(showAuditData.supplierTop.legalPerson)}}
              </div>
              <div v-if="showAuditData.supplierTop.bizPerson != showAuditData.supplierAfter.bizPerson">
                联系人:{{getVal(showAuditData.supplierTop.bizPerson)}}
              </div>
              <div v-if="showAuditData.supplierTop.companyType != showAuditData.supplierAfter.companyType">
                公司类型:{{getVal(showAuditData.supplierTop.companyType)}}
              </div>
              <div v-if="showAuditData.supplierTop.industryName != showAuditData.supplierAfter.industryName">
                所属行业:{{getVal(showAuditData.supplierTop.industryName)}}
              </div>
              <div v-if="showAuditData.supplierTop.suppliertype && showAuditData.supplierTop.suppliertype.value != showAuditData.supplierAfter.typeValue">
                供应商类型:{{showAuditData.supplierTop.suppliertype ? showAuditData.supplierTop.suppliertype.value : '无'}}
              </div>
              <div v-if="showAuditData.supplierTop.supplierlevel && showAuditData.supplierTop.supplierlevel.value != showAuditData.supplierAfter.levelValue">
                供应商等级:{{showAuditData.supplierTop.supplierlevel ? showAuditData.supplierTop.supplierlevel.value : '无'}}
              </div>
              <div v-if="showAuditData.supplierTop.qualityStd != showAuditData.supplierAfter.qualityStd">
                质量标准:{{getVal(showAuditData.supplierTop.qualityStd)}}
              </div>
              <div v-if="showAuditData.supplierTop.website != showAuditData.supplierAfter.website">
                公司网址:{{getVal(showAuditData.supplierTop.website)}}
              </div>
              <div v-if="showAuditData.supplierTop.remark != showAuditData.supplierAfter.remark">
                备注:{{getVal(showAuditData.supplierTop.remark)}}
              </div>
              <div v-if="showAuditData.supplierTop.isDefault != showAuditData.supplierAfter.isDefault">
                默认材料供应商:{{getVal(showAuditData.supplierTop.isDefault == 1 ? '是' : '否')}}
              </div>
              <div v-if="showAuditData.supplierTop.specialRemark != showAuditData.supplierAfter.specialRemark">
                特殊类型备注:{{getVal(showAuditData.supplierTop.specialRemark)}}
              </div>
              <div v-if="showAuditData.supplierTop.paycause && showAuditData.supplierTop.paycause.value != showAuditData.supplierAfter.paycauseValue">
                付款方式:{{showAuditData.supplierTop.paycause ? showAuditData.supplierTop.paycause.value : '无'}}
              </div>
              <div v-if="showAuditData.supplierTop.itemName != showAuditData.supplierAfter.manufacturer">
                品牌:{{getVal(showAuditData.supplierTop.itemName)}}
              </div>
              <div v-if="showAuditData.supplierTop.icloudCompanyName != showAuditData.supplierAfter.icomName">
                大生态圈企业:{{getVal(showAuditData.supplierTop.icloudCompanyName)}}
              </div>
              <div v-if="showAuditData.supplierTop.supplyBrandName != showAuditData.supplierAfter.supplyBrandName">
                供应品牌:{{getVal(showAuditData.supplierTop.supplyBrandName)}}
              </div>
              <div v-if="showAuditData.supplierTop.materialKindStr != showAuditData.supplierAfter.materialKindStr">
                供应商种类:{{getVal(showAuditData.supplierTop.materialKindStr)}}
              </div>
              <div v-if="showAuditData.supplierTop.printRemark != showAuditData.supplierAfter.printRemark">
                供应商打印备注:{{getVal(showAuditData.supplierTop.printRemark)}}
              </div>
              <div v-if="showAuditData.supplierTop.classificationValue != showAuditData.supplierAfter.classificationValue">
                供应商分类:{{getVal(showAuditData.supplierTop.classificationValue)}}
              </div>
              <div v-if="showAuditData.supplierTop.currencyType && showAuditData.supplierTop.currencyType.value != showAuditData.supplierAfter.currencyTypeValue">
                结算货币:{{showAuditData.supplierTop.currencyType ? showAuditData.supplierTop.currencyType.value : '无'}}
              </div>
            </div>
            <div>
              <div>
                {{showAuditData.supplierAfter.nameAfter}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.status != showAuditData.supplierAfter.status">
                状态:{{showAuditData.supplierAfter.status == 100401 ? '未审核' : (showAuditData.supplierAfter.status == 100402 ? '已审核' : (showAuditData.supplierAfter.status == 100403 ? '已失效' : '审批中'))}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.shortName != showAuditData.supplierAfter.shortName">
                简称:{{getVal(showAuditData.supplierAfter.shortName)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.name != showAuditData.supplierAfter.name">
                全称:{{getVal(showAuditData.supplierAfter.name)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.areaCode != showAuditData.supplierAfter.areaCode">
                区域:{{getVal(showAuditData.supplierAfter.areaCode)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.address != showAuditData.supplierAfter.address">
                地址:{{getVal(showAuditData.supplierAfter.address)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.zip != showAuditData.supplierAfter.zip">
                邮编:{{getVal(showAuditData.supplierAfter.zip)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.phone != showAuditData.supplierAfter.phone">
                电话:{{getVal(showAuditData.supplierAfter.phone)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.fax != showAuditData.supplierAfter.fax">
                传真:{{getVal(showAuditData.supplierAfter.fax)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.bank != showAuditData.supplierAfter.bank">
                开户银行:{{getVal(showAuditData.supplierAfter.bank)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.account != showAuditData.supplierAfter.account">
                银行帐号:{{getVal(showAuditData.supplierAfter.account)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.checkDate != showAuditData.supplierAfter.checkDate">
                对帐日期:{{getVal(showAuditData.supplierAfter.checkDate)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.payWay && showAuditData.supplierTop.payWay.value != showAuditData.supplierAfter.payWayValue">
                结算方式:{{getVal(showAuditData.supplierAfter.payWayValue)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.deliveryWay && showAuditData.supplierTop.deliveryWay.value != showAuditData.supplierAfter.deliveryWayValue">
                送货方式:{{getVal(showAuditData.supplierAfter.deliveryWayValue)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.businessPayment && showAuditData.supplierTop.businessPayment.value != showAuditData.supplierAfter.businessPaymentValue">
                业务费月结方式:{{getVal(showAuditData.supplierAfter.businessPaymentValue)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.freightWay && showAuditData.supplierTop.freightWay.value != showAuditData.supplierAfter.freightWayValue">
                交货方式:{{getVal(showAuditData.supplierAfter.freightWayValue)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.taxDescript && showAuditData.supplierTop.taxDescript.value != showAuditData.supplierAfter.taxDescriptValue">
                发票类型:{{getVal(showAuditData.supplierAfter.taxDescriptValue)}}
              </div>

              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.payDays && showAuditData.supplierAfter.payDays && showAuditData.supplierTop.payDays.value != showAuditData.supplierAfter.payDays.value">
                质保期限:{{showAuditData.supplierAfter.payDays.value ? showAuditData.supplierAfter.payDays.value : '无'}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.legalPerson != showAuditData.supplierAfter.legalPerson">
                法人代表:{{getVal(showAuditData.supplierAfter.legalPerson)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.bizPerson != showAuditData.supplierAfter.bizPerson">
                联系人:{{getVal(showAuditData.supplierAfter.bizPerson)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.companyType != showAuditData.supplierAfter.companyType">
                公司类型:{{getVal(showAuditData.supplierAfter.companyType)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.industryName != showAuditData.supplierAfter.industryName">
                所属行业:{{getVal(showAuditData.supplierAfter.industryName)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.suppliertype && showAuditData.supplierTop.suppliertype.value != showAuditData.supplierAfter.typeValue">
                供应商类型:{{getVal(showAuditData.supplierAfter.typeValue)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.supplierlevel && showAuditData.supplierTop.supplierlevel.value != showAuditData.supplierAfter.levelValue">
                供应商等级:{{getVal(showAuditData.supplierAfter.levelValue)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.qualityStd != showAuditData.supplierAfter.qualityStd">
                质量标准:{{getVal(showAuditData.supplierAfter.qualityStd)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.website != showAuditData.supplierAfter.website">
                公司网址:{{getVal(showAuditData.supplierAfter.website)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.remark != showAuditData.supplierAfter.remark">
                备注:{{getVal(showAuditData.supplierAfter.remark)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.isDefault != showAuditData.supplierAfter.isDefault">
                默认材料供应商:{{getVal(showAuditData.supplierAfter.isDefault == 1 ? '是' : '否')}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.specialRemark != showAuditData.supplierAfter.specialRemark">
                特殊类型备注:{{getVal(showAuditData.supplierAfter.specialRemark)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.paycause && showAuditData.supplierTop.paycause.value != showAuditData.supplierAfter.paycauseValue">
                付款方式:{{getVal(showAuditData.supplierAfter.paycauseValue)}}
              </div>

              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.itemName != showAuditData.supplierAfter.manufacturer">
                品牌:{{getVal(showAuditData.supplierAfter.manufacturer)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.icloudCompanyName != showAuditData.supplierAfter.icomName">
                大生态圈企业:{{getVal(showAuditData.supplierAfter.icomName)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.supplyBrandName != showAuditData.supplierAfter.supplyBrandName">
                供应品牌:{{getVal(showAuditData.supplierAfter.supplyBrandName)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.materialKindStr != showAuditData.supplierAfter.materialKindStr">
                供应商种类:{{getVal(showAuditData.supplierAfter.materialKindStr)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.printRemark != showAuditData.supplierAfter.printRemark">
                供应商打印备注:{{getVal(showAuditData.supplierAfter.printRemark)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.classificationValue != showAuditData.supplierAfter.classificationValue">
                供应商分类:{{getVal(showAuditData.supplierAfter.classificationValue)}}
              </div>
              <div class="font-weight-bolder text-danger" v-if="showAuditData.supplierTop.currencyType && showAuditData.supplierTop.currencyType.value != showAuditData.supplierAfter.currencyTypeValue">
                结算货币:{{getVal(showAuditData.supplierAfter.currencyTypeValue)}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "supplier",
  props: {
    showAuditData: [Object, Array],
  },
  mounted()
  {
    this.handleQueryInfo(this.showAuditData);
  },
  data() {
    return{
      flag: "1",
      oneFlag: false,
      flagTwo: "2",
    }
  },
  methods: {
    getVal(val) {
      if (val){
        return val;
      }
      return "无";
    },
    changeFlag() {
      if (this.flag == "1"){
        this.flag = "2";
      }else {
        this.flag = "1";
      }
    },
    changeFlagTwo() {
      if (this.flagTwo == "1"){
        this.flagTwo = "2";
      }else {
        this.flagTwo = "1";
      }
    },
    handleQueryInfo(vo)
    {
      if(!vo || !vo.backupTwoList || vo.backupTwoList.length == 0)
      {
        return;
      }
      if(!vo.supplier || !vo.supplier.recordId)
      {
        return;
      }
      var supplier = vo.supplier
      var supplierTop = {}
      var supplierAfter = {}
      var defaultSupplier = {}
      this.oneFlag = false
      for(let obj of vo.backupTwoList)
      {
        switch (obj.saveType) {
            // 供应商
          case 11:
            this.oneFlag = true
            supplierTop = JSON.parse(JSON.stringify(supplier))
            supplierTop.nameTop = "供应商资料改变前"
            supplierAfter = JSON.parse(JSON.stringify(obj))
            supplierAfter.nameAfter = "供应商资料改变后"
            break;
          case 13:
            defaultSupplier = JSON.parse(JSON.stringify(obj))
            break;
        }
      }
      supplier.isDefault = defaultSupplier.isDefault
      vo.supplierTop = supplierTop
      vo.supplierAfter = supplierAfter
    }
  }
}
</script>