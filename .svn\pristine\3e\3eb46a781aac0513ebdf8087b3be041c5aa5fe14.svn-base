<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">招标广场</h3>
    </div>
    <div class="card">
      <div class="card-body p-3">
        <div v-if="biddingList && biddingList.length > 0">
          <template v-for="item in biddingList">
            <div class="border-bottom pt-3 pb-3" :key="item.recordId">
              <div class="d-flex align-items-center pb-1">
                <div class="image-container-common"><img :src="item.customer.logoStr"></div>
                <div class="ml-3">
                  <div class="font-size-h5 font-weight-bolder">{{item.customer ? item.customerName : null}}</div>
                  <div class="mt-1">{{item.startTimeStr}}&nbsp;到&nbsp;{{item.endTimeStr}}</div>
                </div>
              </div>
              <div class="d-flex align-items-center">
                <div>
                  <span class="badge badge-success" v-if="item.startFlag == 1">进行中</span>
                  <span class="badge badge-info" v-if="item.startFlag == 2">等待中</span>
                  <span class="badge badge-dark" v-if="item.startFlag == 3">已结束</span>
                </div>
                <div class="ml-3" v-if="item.startFlag == 3">
                  <span class="badge badge-primary" v-if="item.status == 1">等待中</span>
                  <span class="badge badge-success" v-if="item.status == 2">已中标</span>
                  <span class="badge badge-danger" v-if="item.status == 3">未中标</span>
                  <span class="badge badge-success" v-if="item.status == 4">已采购</span>
                  <span class="badge badge-warning" v-if="item.status == 5">已弃标</span>
                </div>
                <div class="ml-3 font-weight-bolder">
                  {{item.title}}
                </div>
              </div>
              <div class="d-flex justify-content-between align-items-center pt-1">
                <div>我的企业：{{item.supplierName}}</div>
                <div>竞标数量：{{item.materialNum}}</div>
              </div>
              <div style="word-break: break-all;">{{item.materialName}}&emsp;{{item.materialCraft}}&emsp;交期：{{item.deliveryDateStr}}&emsp;{{item.remark}}</div>
              <div class="d-flex justify-content-end">
                <button class="btn btn-sm btn-outline-primary" v-if="item.startFlag == '1'" v-on:click="biddingDetail(item)">报价<span v-if="item.price">：{{item.price}}</span></button>
                <button class="btn btn-sm btn-outline-dark" v-if="item.startFlag == '3'" v-on:click="biddingDetail(item)">历史报价<span v-if="item.price">：{{item.price}}</span></button>
              </div>
            </div>
          </template>
        </div>
        <div class="text-muted" v-else>暂无场次</div>
      </div>
    </div>
    <b-modal ref="biddingDetail" title="参与竞标" hide-footer>
      <div>
        <div>
          <div class="font-size-lg">招标方：{{bidding.customerName}}</div><div class="font-size-lg pt-1">竞标方：{{bidding.supplierName}}</div>
          <div class="d-flex justify-content-between align-items-center pt-1">
            <div>{{bidding.materialName}}&nbsp;{{bidding.materialCraft}}</div>
            <div>招标数量：{{bidding.materialNum}}</div>
          </div>
          <div class="pt-1">到货日期：{{bidding.deliveryDateStr}}</div>
          <div class="pt-1">招标时间：{{bidding.startTimeStr}}&nbsp;到&nbsp;{{bidding.endTimeStr}}</div>
        </div>
        <div>
          <div class="row pt-2">
            <div class="col-6">单价：<input class="form-control w-75" v-model="bidding.price" :disabled="bidding.startFlag !== 1"></div>
            <div class="col-6">运费：<input class="form-control w-75" v-model="bidding.freight" :disabled="bidding.startFlag !== 1"></div>
          </div>
          <div class="row pt-2">
            <div class="col-6">
              结款方式：
              <select class="form-control w-75" v-model="bidding.payWay" :disabled="bidding.startFlag !== 1">
                <option v-for="item in payWayList" :key="item.recordId" :value="item.recordId">
                  {{item.value}}
                </option>
              </select>
            </div>
            <div class="col-6">
              含税说明：
              <select class="form-control w-75" v-model="bidding.taxDescript" :disabled="bidding.startFlag !== 1">
                <option v-for="item in taxDescriptList" :key="item.recordId" :value="item.recordId">
                  {{item.value}}
                </option>
              </select>
            </div>
          </div>
          <div class="row pt-2">
            <div class="col-6">
              货币类型：
              <select class="form-control w-75" v-model="bidding.currencyType" :disabled="bidding.startFlag !== 1">
                <option v-for="item in currencyTypeList" :key="item.recordId" :value="item.recordId">
                  {{item.value}}
                </option>
              </select>
            </div>
            <div class="col-6">
              付款方式：
              <select class="form-control w-75" v-model="bidding.paycause" :disabled="bidding.startFlag !== 1">
                <option v-for="item in paycauseList" :key="item.recordId" :value="item.recordId">
                  {{item.value}}
                </option>
              </select>
            </div>
          </div>
          <div class="d-flex justify-content-end pt-3" v-if="bidding.startFlag == 1">
            <button class="btn btn-primary btn-sm" v-on:click="addDelivery">增加交货</button>
          </div>
          <template v-for="(item,index) in deliveryList">
            <div class="row align-items-center border-bottom pt-1 pb-2" :key="index">
              <div class="col">数量：<input class="form-control w-75" v-model="item.quantity" :disabled="bidding.startFlag !== 1"></div>
              <div class="col">
                交期：
                <select class="form-control w-75" v-model="item.deliveryDay" :disabled="bidding.startFlag !== 1">
                  <option v-for="item in deliveryDayList" :key="item.recordId" :value="item.recordId">
                    {{item.value}}
                  </option>
                </select>
              </div>
              <div class="col-2 text-right" v-if="bidding.startFlag == 1">
                <button class="btn btn-danger px-3 py-1" v-on:click="delDelivery(index)">-</button>
              </div>
            </div>
          </template>
          <div class="pt-2">备注：<textarea class="form-control" v-model="bidding.remark" :disabled="bidding.startFlag !== 1"/></div>
          <div class="d-flex justify-content-end pt-7">
            <button type="button" class="btn btn-outline-primary" v-on:click="saveBidding" v-if="bidding.startFlag == 1">保存</button>
          </div>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
export default {
name: "startInquiry",
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId) {
      this.userMsg = sysUser;
      this.loadData();
    } else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return {
      userMsg: {},
      payWayList: [],
      taxDescriptList: [],
      currencyTypeList: [],
      paycauseList: [],
      biddingList: [],
      runBiddingList: [],
      waitBiddingList: [],
      bidding: {},
      deliveryList: [],
      deliveryDayList: [],
      imgUrl: "",
    }
  },
  methods: {
    loadData() {
      this.biddingList = [];
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPostString("f/wechat/business/getBiddingList", this.userMsg.recordId).then(result => {
        this.biddingList = result.data;
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    biddingDetail(item) {
      this.deliveryList = item.list;
      this.bidding = item;
      this.getDictValue();
    },
    getDictValue() {
      this.payWayList = [];
      this.taxDescriptList = [];
      this.currencyTypeList = [];
      this.paycauseList = [];
      this.deliveryDayList = [];
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/loadBiddingData").then(result => {
        this.payWayList = result.data.payWayList;
        this.taxDescriptList = result.data.taxDescriptList;
        this.currencyTypeList = result.data.currencyTypeList;
        this.paycauseList = result.data.paycauseList;
        this.deliveryDayList = result.data.deliveryDayList;
        this.$refs['biddingDetail'].show();
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    addDelivery(){
      const delivery = {};
      this.deliveryList.push(delivery);
    },
    delDelivery(index) {
      this.deliveryList.splice(index, 1);
    },
    saveBidding() {
      if (!this.bidding.price || isNaN(this.bidding.price)){
        alert("单价请录入数字");
        return;
      }
      if (isNaN(this.bidding.freight)){
        alert("运费请录入数字");
        return;
      }
      if (!this.bidding.payWay){
        alert("请选择结算方式");
        return;
      }
      if (!this.bidding.taxDescript){
        alert("请选择税率");
        return;
      }
      if (!this.bidding.currencyType){
        alert("请选择货币类型");
        return;
      }
      if (!this.bidding.paycause){
        alert("请选择付款方式");
        return;
      }
      const entity = {};
      entity.recordId = this.bidding.recordId;
      entity.price = this.bidding.price;
      entity.freight = this.bidding.freight;
      entity.payWay = this.bidding.payWay;
      entity.taxDescript = this.bidding.taxDescript;
      entity.currencyType = this.bidding.currencyType;
      entity.paycause = this.bidding.paycause;
      entity.remark = this.bidding.remark;
      entity.startTime = this.bidding.startTime;
      entity.endTime = this.bidding.endTime;
      entity.userId = this.userMsg.recordId;
      entity.list = [];
      for (let i=0;i<this.deliveryList.length;i++){
        if (this.deliveryList[i].quantity){
          const delivery = {};
          delivery.quantity = this.deliveryList[i].quantity;
          delivery.deliveryDay = this.deliveryList[i].deliveryDay;
          entity.list.push(delivery);
        }
      }
      if (entity.list == null || entity.list.length == 0){
        alert("请录入交货时间");
        return;
      }
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/saveBidding", entity).then(result => {
        alert(result.data);
        this.$refs['biddingDetail'].hide();
        this.getBiddingList();
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    }
  }
}
</script>