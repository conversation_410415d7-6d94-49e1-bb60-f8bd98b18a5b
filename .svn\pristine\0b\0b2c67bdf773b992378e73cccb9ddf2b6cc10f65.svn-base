<template>
  <div>
    <template v-for="(item, index) in auditList">
      <div class="p-1 border-bottom" :key="item.recordId" v-if="index < 100">
        <div class="d-flex justify-content-between align-items-center">
          <div class="text-truncate" style="width: 20rem;">
            <span :class="['badge', 'alert-' + item.typeColor, 'px-1 py-1']"><i :class="['fa', item.typeIconClass, 'text-' + item.typeColor, 'font-size-sm']"></i></span>
            <span class="font-weight-bolder">{{item.typeName}}</span>
            <span class="pr-1 pl-1">{{ item.typeCode }}-{{item.no}}</span>
          </div>
          <button class="btn btn-sm btn-outline-primary px-2 py-1" v-on:click="showAuditDetails(item)">
            进入
          </button>
        </div>
        <div class="pt-1 text-truncate text-primary" v-if="item.applicationsType == 2 && item.positionName">
          [待{{item.positionName}}审批]<span v-if="item.approveUserName">-{{item.approveUserName}}</span>
        </div>
        <div class="pt-1 text-truncate text-warning" v-else-if="item.applicationsType == 1">
          [草稿]
        </div>
        <div class="pt-1 text-truncate text-danger" v-else-if="item.applicationsType == 3 && item.applicationsResult=='reject'">
          [已驳回]
        </div>
        <div class="pt-1 text-truncate text-success" v-else-if="item.applicationsType == 3 && item.applicationsResult=='assent'">
          [通过]
        </div>
        <div class="pt-1 d-flex justify-content-between align-items-center">
          <div>提交人：{{item.name}}</div>
          <div>提交时间：{{item.createdDate}}</div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: "shareAudit",
  props: {
    auditList: Array
  },
  methods: {
    showAuditDetails (item) {
      this.$router.push({path: '/audit/auditDetails', query: {auditId: item.recordId, inFlag: '2'}});
    },
  }
}
</script>