<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">历史反馈</h3>
    </div>
    <div class="card mb-3">
      <div class="card-body p-3">
        <div class="input-group input-group-solid">
          <span class="pl-3 font-weight-bolder">筛选：</span>
          <input class="form-control" v-model="searchContent" v-on:change="loadData">
        </div>
      </div>
    </div>
    <template v-for="(row, index) in historyBackList">
      <div class="bg-white p-3 mb-3" :key="index">
        <div class="d-flex justify-content-between align-items-center border-bottom pb-2" style="font-size: 1.3rem;">
          <div class="text-primary">FK00{{ row.recordId }}</div>
          <div>
            <button class="btn btn-outline-danger px-3 py-1" v-if="row.status === '1001'" v-on:click="finishBack(row)">结案</button>
            <button class="btn btn-outline-primary px-3 py-1" v-else>已结案</button>
          </div>
        </div>
        <div class="font-size-lg pt-2">
          <div class="text-muted">反馈内容</div>
          {{row.content}}
        </div>
        <div class="pt-3 pb-3 d-flex justify-content-between align-items-center">
          <div><span class="text-muted">联系号码：</span>{{row.phone}}</div>
          <div><span class="text-muted">反馈时间：</span>{{row.createdDate}}</div>
        </div>
        <div class="p-1 alert-danger font-weight-bolder" v-if="row.status === '1001'">
          我们将在24小时内尽快与您联系，如急需解决，请到&nbsp;<span class="badge badge-primary">我的</span>&nbsp;再点击&nbsp;
          <span class="badge badge-primary">联系客服</span>&nbsp;拨打客服电话
        </div>
      </div>
    </template>
    <div style="height: 100px;"></div>
  </div>
</template>

<script>
export default {
name: "historyBack",
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      //加载历史反馈
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data()
  {
    return{
      userMsg:{},
      historyBackList:[],
      searchContent:"",
    }
  },
  methods: {
    loadData() {
      const query = {};
      query.recordId = this.userMsg.recordId;
      query.searchContent = this.searchContent;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/getHistoryBackList", query).then(result => {
        if(result.data){
          this.historyBackList = result.data;
        }else {
          alert("请刷新重试");
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    finishBack(item) {
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/finishBack", item).then(result => {
        if(result.data && result.data == "success"){
          alert("结案成功！")
        }else {
          alert("请刷新重试");
        }
        this.loadData();
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    }
  }
}
</script>
