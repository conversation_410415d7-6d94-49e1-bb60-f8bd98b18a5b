<template>
  <transition name="fade">
    <div v-if="isPageLoading" class="global-page-loader">
      <div class="loader-content">
        <div class="loader-spinner">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
        </div>
        <div class="loader-message" v-if="loadingMessage">
          {{ loadingMessage }}
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "GlobalPageLoader",
  computed: {
    ...mapGetters(["isPageLoading", "loadingMessage"])
  }
};
</script>

<style scoped>
.global-page-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #ffffff;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.loader-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loader-spinner {
  margin-bottom: 20px;
}

.loader-spinner .spinner-border {
  width: 3rem;
  height: 3rem;
  border-width: 0.3em;
}

.loader-message {
  font-size: 16px;
  color: #6c757d;
  text-align: center;
  margin-top: 10px;
}

/* 淡入淡出动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

/* 防止页面滚动 */
.global-page-loader {
  overflow: hidden;
}

/* 当加载器显示时，禁止body滚动 */
body.page-loading-active {
  overflow: hidden;
}
</style>
