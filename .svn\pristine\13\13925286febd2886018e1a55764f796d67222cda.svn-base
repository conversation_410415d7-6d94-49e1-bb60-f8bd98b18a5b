<template>
<div>
<div v-if="showAuditData && showAuditData.voList && showAuditData.voList.length > 0">
  <div class="pt-2">
    <div class="font-weight-bolder">销售方：{{showAuditData.voList[0].company.name}}</div>
    <div class="font-weight-bolder">供方：{{showAuditData.voList[0].supplier.name}}</div>
    <div class="text-left pt-1">
      <div class="d-flex justify-content-between align-items-center pb-1 border-bottom">
        <div class="font-weight-bolder">采购单详情</div>
        <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlag(1)">{{ flag && flag == '1' ? '收起' : '展开' }}</button>
      </div>
    </div>
    <div v-for="(item,index) in showAuditData.voList" :key="index">
      <div v-if="flag == '1' && item && item.materialCheckList && item.materialCheckList.length > 0">
        <div class="pt-2"  v-for="(materialCheck,index) in item.materialCheckList" :key="index">
          <div class="d-flex text-danger">
            <div>采购单{{index+1}}</div>
          </div>
          <div class="d-flex justify-content-between">
            <div>采购单编号：{{materialCheck.orderNo}}</div>
            <div>数量：{{materialCheck.quantity}}</div>
            <div>金额：{{materialCheck.amount}}</div>
          </div>
          <div class="pt-2">
            <div class="d-flex justify-content-between align-items-center pb-1 border-bottom">
              <div class="font-weight-bolder">付款对账单明细</div>
              <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlagTwo(materialCheck)">
                <span v-if="!item.flag">展开</span>
                <span v-else>收起</span>
              </button>
            </div>
            <div v-if="materialCheck.detailList && materialCheck.detailList.length > 0 && materialCheck.flag">
              <div v-for="(detail,index) in materialCheck.detailList" :key="index">
                <div class="d-flex justify-content-between">
                  <div>供应商编号：{{detail.supplier ? detail.supplier.no : null}}</div>
                  <div>单据编号：{{detail.billno}}</div>
                </div>
                <div class="d-flex justify-content-between">
                  <div>供应商名称：{{detail.supplier ? detail.supplier.name : null}}</div>
                  <div>对账月份：{{detail.period}}</div>
                </div>
                <div class="d-flex justify-content-between">
                  <div>物料编号/外发单号：{{detail.material ? detail.material.no : null}}</div>
                  <div>物料名称/生产编号：{{detail.material ? detail.material.name : null}}</div>
                </div>
                <div class="d-flex justify-content-between">
                  <div>出入库时间：{{detail.receivedDate}}</div>
                  <div>订单数量：{{detail.purchasingQuantity}}</div>
                </div>
                <div class="d-flex justify-content-between">
                  <div>出入库数量：{{detail.quantity}}</div>
                  <div>金额：{{detail.displayAmount}}</div>
                </div>
                <div class="d-flex">
                  <div>单价：{{detail.price}}</div>
                </div>
                <div class="pt-2"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="text-left pt-1">
      <div class="d-flex justify-content-between align-items-center pb-1 border-bottom">
        <div class="font-weight-bolder">对账单明细调整</div>
        <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlag(2)">
          {{ flagT && flagT == '1' ? '收起' : '展开' }}
        </button>
      </div>
      <div class="pt-2" v-if="showAuditData.adjustPayableList && showAuditData.adjustPayableList.length > 0">
        <div v-for="(item,index) in showAuditData.adjustPayableList" :key="index">
          <div v-if="flagT == '1' && item.adjustPayableList && item.adjustPayableList.length > 0">
            <div v-for="(adjustPayable, index) in item.adjustPayableList" :key="index">
              <div class="d-flex justify-content-between">
                <div>供应商编号:{{adjustPayable.supplier ? adjustPayable.supplier.no : null}}</div>
                <div>据编号:{{adjustPayable.materialCheck.billNo}}</div>
              </div>
              <div class="d-flex justify-content-between">
                <div>供应商名称:{{adjustPayable.supplier ? adjustPayable.supplier.name : null}}</div>
                <div>对账月份:{{adjustPayable.period}}</div>
              </div>
              <div class="d-flex justify-content-between">
                <div>物料编号/外发单号:{{adjustPayable.material ? adjustPayable.material.no : null}}</div>
                <div>物料名称/生产编号:{{adjustPayable.material ? adjustPayable.material.name : null}}</div>
              </div>
              <div class="d-flex justify-content-between">
                <div>出入库时间:{{adjustPayable.materialCheck.receivedDate}}</div>
                <div>订单数量:{{adjustPayable.materialCheck.purchasingQuantity}}</div>
              </div>
              <div class="d-flex justify-content-between">
                <div>出入库数量:{{adjustPayable.materialCheck.quantity}}</div>
                <div>金额:{{adjustPayable.materialCheck.originalAmount}}</div>
              </div>
              <div class="d-flex justify-content-between">
                <div>单价:{{adjustPayable.materialCheck.price}}</div>
                <div>对账单明细调整金额:{{adjustPayable.amount}}</div>
              </div>
              <div style="word-break: break-all;">
                <div>调整原因:{{adjustPayable.adjustReason}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pt-2" v-if="showAuditData.adjustDetailList && showAuditData.adjustDetailList.length > 0">
        <div v-for="(item,index) in showAuditData.adjustDetailList" :key="index">
          <div v-if="flagT == '1' && item.adjustDetailList && item.adjustDetailList.length > 0">
            <div v-for="(adjustBalance, index) in item.adjustDetailList" :key="index">
              <div class="d-flex justify-content-between">
                <div>对账调整金额: {{ adjustBalance.amount }}</div>
                <div>调整原因: {{ adjustBalance.adjustReason }}</div>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>
</div>
</div>
</template>

<script>
export default {
name: "paymentStateConfirm",
  props: {
    showAuditData: [Object, Array],
  },
  data() {
    return{
      flag: "2",
      flagT: "2",
    }
  },
  methods: {
    changeFlag(num) {
      if (num == 1)
      {
        if (this.flag == "1") {
          this.flag = "2";
        } else {
          this.flag = "1";
        }
      }else{
        if (this.flagT == "1") {
          this.flagT = "2";
        } else {
          this.flagT = "1";
        }
      }
    },
    changeFlagTwo(item) {
      this.$set(item, 'flag', !item.flag)
    },
  }
}
</script>