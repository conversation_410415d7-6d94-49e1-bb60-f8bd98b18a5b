<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">
        设备管理
      </h3>
    </div>
    <div class="card card-custom gutter-b">
      <div class="card-body d-flex flex-column p-3">
        <div class="row border-bottom pb-1 align-items-center">
          <div class="col-12">
            <label class="text-muted">客户:</label>
            <select class="form-control input-icon-sm" v-model="applyCompanyId" v-on:change="getEquipmentManageList(1)">
              <option v-for="item in applyCompanyList" :key="item.recordId" :value="item.recordId">
                {{item.name}}
              </option>
            </select>
          </div>
          <div class="col-12 pb-2">
            <label class="text-muted">设备:</label>
            <input type="text" class="form-control"  placeholder="请输入要搜索的设备信息" v-model="searchInfo" v-on:blur="getEquipmentManageList(1)"/>
          </div>
        </div>
        <div class="row pt-1">
          <template v-for="item in equipmentManageList">
            <div class="col-6" :key="item.recordId">
              <div class="card card-custom gutter-b card-stretch bg-dark text-light-secondary">
                <div class="card-body p-1">
                  <div class="d-flex justify-content-between align-items-center" v-on:click="showMaterialMsg(item)">
                    <span class="text-primary font-weight-bolder mr-2">{{item.materialType}}</span>
                    <span class="label label-danger ml-2" v-if="item.repairStatus === '1001' || item.repairStatus === '1002'"></span>
                    <span class="label label-success ml-2" v-else></span>
                  </div>
                  <div>{{item.materialName}}<span v-if="item.specification">-{{item.specification}}</span></div>
                  <div>{{item.applyCompany ? item.applyCompany.name : ""}}</div>
                  <div v-if="item.repairStatus === '1001'" class="text-danger">未受理</div>
                  <div v-if="item.repairStatus === '1002'" class="text-success">维修中</div>
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="font-weight-bolder mr-2">维修记录:</span>
                    <span>{{item.count? item.count : 0}}</span>
                  </div>
                  <div class="d-flex justify-content-between align-items-center" v-if="item.repairName">
                    <span class="font-weight-bolder mr-2">最近维保:</span>
                    <span class="font-weight-bold">{{item.repairName}}</span>
                  </div>
                  <div v-if="item.remark">
                    <div>最多原因:</div>
                    <span>{{item.remark}}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
        <div class="row">
          <div class="col-xl-12">
            <div style="height: 6rem;"></div>
          </div>
        </div>
      </div>
    </div>
    <b-modal ref="showRecordModal" title="维保记录" hide-footer>
      <div>
        <div class="modal-body">
          <div class="row font-weight-bold border-bottom pt-3 pb-3">
            <div class="col-12">
              <label>
                设备:&nbsp;{{maintenanceRecord.materialName}}&nbsp;规格:&nbsp;{{maintenanceRecord.specification}}
              </label>
            </div>
          </div>
          <div class="row pt-3" v-if="!(maintenanceRecordList && maintenanceRecordList.length > 0)">
            <div class="col-xl-12 text-success">
              <h3>暂无维保记录</h3>
            </div>
          </div>
          <template v-for="item in maintenanceRecordList">
            <div class="row border-bottom" :key="item.recordId">
              <div class="col-xl-12 pb-3 pt-3">
                <div class="row">
                  <div class="col">
                    申报:&nbsp;<span>{{item.applyCompany ? item.applyCompany.name : ""}}-{{item.applyName}}</span>
                  </div>
                </div>
                <div class="row">
                  <div class="col">
                    维保:&nbsp;<span>{{item.repairCompany ? item.repairCompany.name : ""}}<span v-if="item.repairName">-{{item.repairName}}</span></span>
                  </div>
                </div>
                <div class="row" v-if="item.startDate || item.endDate">
                  <div class="col">
                    时间:&nbsp;{{item.startDate}}<span v-if="item.endDate">&nbsp;到&nbsp;{{item.endDate}}</span>
                  </div>
                </div>
                <div class="row" v-if="item.problem">
                  <div class="col">
                    描述:&nbsp;{{item.problem}}
                  </div>
                </div>
                <div class="row" v-if="item.remark">
                  <div class="col">
                    原因:&nbsp;{{item.remark}}
                  </div>
                </div>
                <div class="row" v-if="item.repairStatus == 1003">
                  <div class="col-xl-12" v-if="item.remarkT">
                    <div class="row">
                      <div class="col">
                        <div class="checkbox-inline">
                          效率:&nbsp;
                          <div class="star-rating">
                            <template v-for="(itemT, index) in efficiencyList">
                              <i :class="{'fas fa-star text-danger': index + 1 <= item.checkedStarsOne, 'far fa-star': index + 1 > item.checkedStarsOne}" :key="itemT.value"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            </template>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col">
                        <div class="checkbox-inline">
                          质量:&nbsp;
                          <div class="star-rating">
                            <template v-for="(itemT, index) in massScoreList">
                              <i :class="{'fas fa-star text-danger': index + 1 <= item.checkedStarsTwo, 'far fa-star': index + 1 > item.checkedStarsTwo}" :key="itemT.value"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            </template>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col">
                        <div class="checkbox-inline">
                          态度:&nbsp;
                          <div class="star-rating">
                            <template v-for="(itemT, index) in attitudeScoreList">
                              <i :class="{'fas fa-star text-danger': index + 1 <= item.checkedStarsThree, 'far fa-star': index + 1 > item.checkedStarsThree}" :key="itemT.value"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            </template>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col">
                        评价:&nbsp;{{item.remarkT}}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
export default {
name: "equipmentManage",
  data(){
    return {
      equipmentManageList:[],
      searchInfo:'',
      message:'',
      applyCompanyId:null,
      maintenanceRecord:{},
      maintenanceRecordList:[],
      efficiencyList:[
        {"recordId" : "1", "value": 1},
        {"recordId" : "2", "value": 2},
        {"recordId" : "3", "value": 3},
        {"recordId" : "4", "value": 4},
        {"recordId" : "5", "value": 5}
      ],
      massScoreList:[
        {"recordId" : "1", "value": 1},
        {"recordId" : "2", "value": 2},
        {"recordId" : "3", "value": 3},
        {"recordId" : "4", "value": 4},
        {"recordId" : "5", "value": 5}
      ],
      attitudeScoreList:[
        {"recordId" : "1", "value": 1},
        {"recordId" : "2", "value": 2},
        {"recordId" : "3", "value": 3},
        {"recordId" : "4", "value": 4},
        {"recordId" : "5", "value": 5}
      ],
      checkedStarsOne: 0,
      checkedStarsTwo:0,
      checkedStarsThree:0,
      userMsg:{},
      applyCompanyList:[],
    }
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.getEquipmentManageList(2);
    }else {
      alert("请重新进入公众号");
    }
  },
  methods: {
    getEquipmentManageList: function (number) {
      const query = {};
      query.searchInfo = this.searchInfo;
      query.userId = this.userMsg.recordId;
      query.phone = this.userMsg.phone;
      query.applyCompanyId = this.applyCompanyId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/repair/getEquipmentManageList", query).then(result => {
        if (result.data) {
            this.equipmentManageList = result.data.materialEquimentList;
          if (number === 2)
          {
            this.applyCompanyList = result.data.applyCompanyList;
          }
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
    showMaterialMsg: function (item) {
      this.maintenanceRecord = item;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/repair/getMaintenanceRecordList", item).then(result => {
        if (result.data) {
          this.maintenanceRecordList = result.data;
          for (let scoreT of this.maintenanceRecordList) {
            if (scoreT.efficiency) {
              scoreT.checkedStarsOne = scoreT.efficiency;
            }
            if (scoreT.massScore) {
              scoreT.checkedStarsTwo = scoreT.massScore;
            }
            if (scoreT.attitudeScore) {
              scoreT.checkedStarsThree = scoreT.attitudeScore;
            }
          }
        }
        this.$refs['showRecordModal'].show();
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
    closeWindow()
    {
      this.$refs['showRecordModal'].hide();
    },
  }
}
</script>
