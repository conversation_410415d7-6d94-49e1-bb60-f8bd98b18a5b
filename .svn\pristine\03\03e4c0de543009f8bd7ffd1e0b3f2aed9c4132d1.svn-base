<template>
  <div>
    <template v-for="(item, index) in dayThingList">
      <div class="p-1 border-bottom" :key="item.recordId" v-if="index < 100">
        <div class="d-flex justify-content-between align-items-center">
          <div class="text-truncate text-primary" style="width: 20rem;">
            {{item.title}}
          </div>
          <button class="btn btn-sm btn-outline-primary px-2 py-1" v-on:click="showDaythingDetails(item)">进入</button>
        </div>
        <div class="pt-1 text-truncate" v-if="item.content">{{item.content}}</div>
        <div class="pt-1 text-truncate">地点：{{item.address}}</div>
        <div class="pt-1 text-truncate">时间：{{item.startTime}}&nbsp;到&nbsp;{{item.endTime}}</div>
        <div class="pt-1 text-truncate font-weight-bolder">参与: {{item.shareNames}}</div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: "shareDayThing",
  props: {
    dayThingList: Array
  },
  methods: {
    showDaythingDetails: function (item) {
      this.$router.push({path: '/dayThing/dayThingDetails', query: {dayThingId : item.recordId, inFlag: '2'}});
    },
  }
}
</script>