<template>
  <div class="wechat-pagination bg-white">
    <div class="pagination-controls pl-7 pr-7">
      <button
          :disabled="currentPage === 1"
          @click="goToPage(1)"
          class="btn btn-sm btn-outline-primary px-1 py-1"
      >
        首页
      </button>

      <button
          :disabled="currentPage === 1"
          @click="prevPage"
          class="btn btn-sm btn-outline-primary px-1 py-1"
      >
        <i class="fa fa-angle-left"></i>
      </button>

      <div class="current-page-info">
        {{ currentPage }}/{{ totalPages }}
      </div>

      <button
          :disabled="currentPage === totalPages"
          @click="nextPage"
          class="btn btn-sm btn-primary px-1 py-1"
      >
        <i class="fa fa-angle-right"></i>
      </button>

      <button
          :disabled="currentPage === totalPages"
          @click="goToPage(totalPages)"
          class="btn btn-sm btn-primary px-1 py-1"
      >
        尾页
      </button>
    </div>

    <!-- 移动端快捷跳转 -->
    <div class="page-jump">
      <span>前往</span>
      <input
          type="number"
          v-model.number="jumpPage"
          :min="1"
          :max="totalPages"
          @keyup.enter="jumpToPage"
          class="page-input"
      >
      <span>页</span>&nbsp;
      <button @click="jumpToPage" class="btn btn-sm btn-primary px-1 py-1">确定</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WechatPagination',
  props: {
    total: {
      type: Number,
      required: true
    },
    currentPage: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      jumpPage: this.currentPage
    };
  },
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.pageSize);
    }
  },
  watch: {
    currentPage(newVal) {
      this.jumpPage = newVal;
    }
  },
  methods: {
    goToPage(page) {
      if (page < 1 || page > this.totalPages) return;
      this.$emit('update:currentPage', page);
      this.$emit('page-change', page);
    },

    prevPage() {
      if (this.currentPage > 1) {
        this.goToPage(this.currentPage - 1);
      }
    },

    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.goToPage(this.currentPage + 1);
      }
    },

    jumpToPage() {
      const page = parseInt(this.jumpPage);
      if (page && page >= 1 && page <= this.totalPages) {
        this.goToPage(page);
      } else {
        // 输入无效时恢复当前页
        this.jumpPage = this.currentPage;
      }
    }
  }
}
</script>

<style scoped>
.wechat-pagination {
  padding: 8px;
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 14px;
}

.pagination-info {
  text-align: center;
  color: #666;
}

.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-btn {
  padding: 6px 12px;
  background-color: #07C160;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:disabled {
  background-color: #ddd;
  cursor: not-allowed;
}

.current-page-info {
  font-weight: bold;
  color: #333;
}

.page-jump {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.page-input {
  width: 50px;
  height: 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
}

.jump-btn {
  padding: 6px 12px;
  background-color: #07C160;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}
</style>
