<template>
  <div class="d-flex flex-column flex-root">
    <LoadingModal v-if="loadingFlag"></LoadingModal>
    <Loader v-if="loaderEnabled" v-bind:logo="loaderLogo"></Loader>
    <div class="d-flex flex-row flex-column-fluid page">
      <div class="d-flex flex-column flex-row-fluid wrapper">
        <div class="content d-flex flex-column flex-column-fluid"
             :style="{ backgroundImage: `url(${backgroundImage})`,
              backgroundSize: '100% 100px',backgroundPosition: 'top'}">
          <div class="d-flex flex-column-fluid">
            <div class="container-fluid">
              <transition name="fade-in-up">
                <router-view />
              </transition>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <nav class="fixed-bottom bottom-0 left-0 right-0 bg-white shadow-[0_-2px_10px_rgba(0,0,0,0.05)] z-50">
        <div class="container mx-auto">
          <div class="nav justify-content-around" role="tablist" style="height: 5rem;">
            <template v-for="item in tabList">
              <div :key="item.idKey" v-on:click="changeTab(item)">
                <MENU :tabClass="item.tabClass" :name="item.name" :iconClass="item.iconClass"
                              :currentTabId="currentTabId" :idKey="item.idKey"></MENU>
              </div>
            </template>
          </div>
        </div>
      </nav>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Loader from "@/view/content/Loader.vue";
import LoadingModal from "@/view/content/LoadingModal.vue";
import MENU from "@/view/layout/utils/Menu";
import {
  ADD_BODY_CLASSNAME,
  REMOVE_BODY_CLASSNAME
} from "@/core/services/store/htmlclass.module.js";

export default {
  name: "Layout",
  components: {
    Loader,
    LoadingModal,
    MENU
  },
  beforeMount() {
    // show page loading
    this.$store.dispatch(ADD_BODY_CLASSNAME, "page-loading");
    // Simulate the delay page loading
    setTimeout(() => {
      // Remove page loader after some time
      this.$store.dispatch(REMOVE_BODY_CLASSNAME, "page-loading");
    }, 2000);
  },
  beforeDestroy() {
    // 组件销毁前清除定时器，避免触发导航
    if (this.navigationTimer) {
      clearTimeout(this.navigationTimer)
    }
  },
  mounted() {
    // 空:小易助手,1:大生态圈,2:MES工具,3:物流系统,4:账户中心,5:简介
    let wxEntry = window.localStorage.getItem("wxEntry");
    this.getTabList(wxEntry);
    const name = this.$route.name;
    if (name == "business" || name == "work"){
      this.currentTabId = "1";
    }else if (name == "quotation" || name == "submit"){
      this.currentTabId = "2";
    }else if (name == "PublishDemand"){
      this.currentTabId = "3";
    }else if (name == "orderList" || name == "contract"){
      this.currentTabId = "4";
    }else if (name == "bus_person" || name == "my"){
      this.currentTabId = "5";
    }else {
      this.currentTabId = "1";
    }
  },
  data() {
    return{
      loadingFlag: false,
      currentTabId: "1",
      navigationTimer: null,
      tabList: []
    }
  },
  methods: {
    enableLoadFlag(flag) {
      this.loadingFlag = flag;
    },
    changeTab(item) {
      // 空:小易助手,1:大生态圈,2:MES工具,3:物流系统,4:账户中心,5:简介
      let wxEntry = window.localStorage.getItem("wxEntry");
      if (!(item && item.idKey)){
        this.router_page((wxEntry && wxEntry == "1") ?'/business':'/work');
      }
      this.currentTabId = item.idKey;
      if (item.idKey == "1"){
        this.router_page((wxEntry && wxEntry == "1") ?'/business':'/work');
      }else if (item.idKey == "2"){
        this.router_page((wxEntry && wxEntry == "1") ?'/quotation':'/submit');
      }else if (item.idKey == "3"){
        this.router_page((wxEntry && wxEntry == "1") ?'/PublishDemand':'/work');
      }else if (item.idKey == "4"){
        this.router_page((wxEntry && wxEntry == "1") ?'/orderList':'/contract');
      }else if (item.idKey == "5"){
        this.router_page((wxEntry && wxEntry == "1") ?'/bus_person':'/my');
      }
    },
    getTabList(wxEntry) {
      this.tabList = [];
      if (wxEntry){
        if (wxEntry == "1"){
          this.tabList = [
            {idKey: "1", tabClass: "active", name: "首页", iconClass: "fa fa-home text-xl"},
            {idKey: "2", tabClass: "", name: "询价下单", iconClass: "fa fa-shopping-cart text-xl"},
            {idKey: "3", tabClass: "", name: "发布", iconClass: "fa fa-plus-circle text-xl"},
            {idKey: "4", tabClass: "", name: "交易中心", iconClass: "fa fa-list-ul text-xl"},
            {idKey: "5", tabClass: "", name: "我的", iconClass: "fa fa-user text-xl"}
          ];
          return;
        }else if (wxEntry == "6"){
          this.tabList = [
            {idKey: "1", tabClass: "active", name: "工作台", iconClass: "fa fa-home text-xl"},
            {idKey: "2", tabClass: "", name: "提交", iconClass: "fa fa-plus-circle text-xl"},
            {idKey: "4", tabClass: "", name: "联系人", iconClass: "fa fa-users text-xl"},
            {idKey: "5", tabClass: "", name: "我的", iconClass: "fa fa-user text-xl"}
          ];
          return;
        }
      }
    },
    router_page(name) {
      // 模拟异步操作
      this.navigationTimer = setTimeout(() => {
        this.$router.push(name)
      }, 500)
    }
  },
  computed: {
    ...mapGetters([
      "layoutConfig"
    ]),

    /**
     * Check if the page loader is enabled
     * @returns {boolean}
     */
    loaderEnabled() {
      return !/false/.test(this.layoutConfig("loader.type"));
    },

    /**
     * Page loader logo image using require() function
     * @returns {string}
     */
    loaderLogo() {
      return process.env.BASE_URL + "media/logos/kyb.jpg";
    },
    backgroundImage() {
      return process.env.BASE_URL + "media/bg/bg-10.jpg";
    }
  }
};
</script>
