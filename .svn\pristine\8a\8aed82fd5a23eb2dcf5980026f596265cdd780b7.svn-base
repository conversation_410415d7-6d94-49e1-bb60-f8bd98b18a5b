<template>
  <div>
    <div v-if="showAuditData && showAuditData.length > 0">
      <div>
        <div class="d-flex justify-content-between align-items-center pb-1 border-bottom">
          <div class="font-weight-bolder">业务费率</div>
          <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlag">{{ flag && flag == '1' ? '收起' : '展开' }}</button>
        </div>
        <template v-for="(item,index) in showAuditData">
          <div v-if="flag == '1'" :key="index">
            <div class="d-flex justify-content-between pt-1">
              <div>客户：{{item.customer.no}}-{{item.customer.shortName}}</div>
              <div>费率：{{item.rates}}%</div>
            </div>
            <div class="d-flex pt-1">
              备注:{{item.remark}}
            </div>
          </div>
        </template>
      </div>

    </div>
  </div>
</template>

<script>
export default {
name: "businessRate",
  props: {
    showAuditData: [Object, Array],
  },
  data() {
    return{
      flag: "2"
    }
  },
  methods: {
    changeFlag() {
      if (this.flag == "1"){
        this.flag = "2";
      }else {
        this.flag = "1";
      }
    }
  }
}
</script>