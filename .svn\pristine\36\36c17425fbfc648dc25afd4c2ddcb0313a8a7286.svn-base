<template>
  <div class="bg-white rounded-sm p-3" v-if="produceDetail && produceDetail.recordId">
    <div class="row">
      <div class="col-12 text-center font-weight-bolder" style="font-size: 1.3rem;">
        <span v-if="!produceDetail.complaintId">{{produceDetail.no}}</span> /
        A{{produceDetail.produceBatch ? produceDetail.produceBatch.recordId : null}}
        B{{produceDetail.produceBatchDetail ? produceDetail.produceBatchDetail.recordId : null}}
        ({{produceDetail.deailNo}}/{{produceDetail.cardCount}})
      </div>
    </div>
    <div class="row pt-7 pl-2 pr-2 align-items-center">
      <div class="col-8 font-weight-bolder" style="font-size: 1.2rem;">
        <span class="text-danger" v-if="produceDetail.process && produceDetail.process.category">{{produceDetail.process.category}}</span>
        <span class="text-danger font-weight-bolder">
          (<span v-if="produceDetail.handOverFlag == 1">待接板</span>
          <span v-if="produceDetail.handOverFlag == 2">待交板</span>
          <span v-if="produceDetail.handOverFlag == 3">已交板</span>
          <span v-if="produceDetail.handOverFlag == 4">已结束</span>)
        </span>
      </div>
      <div class="col-4 text-right" v-if="!produceDetail.complaintId">
        <button class="btn btn-sm btn-primary px-1 py-1" v-on:click="inInformation(produceDetail)">看资料</button>
      </div>
    </div>
    <div class="row pt-7 pb-3 border-bottom font-weight-bolder">
      <div class="col-12">
        <span class="text-muted" v-if="!produceDetail.complaintId">通知单号:</span>
        <span class="text-muted" v-else>客诉单号:</span>&nbsp;&nbsp;&nbsp;{{produceDetail.noticeNos}}&nbsp;&nbsp;&nbsp;
        <span class="text-muted">生产编号:</span>&nbsp;&nbsp;&nbsp;{{produceDetail.craftNo}}
      </div>
    </div>
    <div class="row pt-3 pb-3 border-bottom font-weight-bolder" v-if="produceDetail.handOverFlag != 1 && produceDetail.mrb">
      <div class="col-12">
        <span class="text-muted">报废PCS&nbsp;:&nbsp;</span>{{produceDetail.mrb}}
      </div>
    </div>
    <div class="row pt-3 pb-3 border-bottom font-weight-bolder" v-if="!produceDetail.complaintId">
      <div class="col-4">
        <span class="text-muted">接板PNL&nbsp;:&nbsp;</span>{{(produceDetail.takeOverQtyA?Number(produceDetail.takeOverQtyA):0)
        + (produceDetail.takeOverQtyB?Number(produceDetail.takeOverQtyB):0)}}
      </div>
      <div class="col-4">
        <span class="text-muted">A板&nbsp;:&nbsp;</span>{{produceDetail.takeOverQtyA}}
      </div>
      <div class="col-4">
        <span class="text-muted">B板&nbsp;:&nbsp;</span>{{produceDetail.takeOverQtyB}}
      </div>
    </div>
    <div class="row pt-3 pb-3 border-bottom font-weight-bolder" v-if="!produceDetail.complaintId">
      <div class="col-4">
        <span class="text-muted">接板SET&nbsp;:&nbsp;</span>{{(produceDetail.takeOverQtySetA?Number(produceDetail.takeOverQtySetA):0)
      + (produceDetail.takeOverQtySetB?Number(produceDetail.takeOverQtySetB):0)}}
      </div>
      <div class="col-4">
        <span class="text-muted">A板&nbsp;:&nbsp;</span>{{produceDetail.takeOverQtySetA}}
      </div>
      <div class="col-4">
        <span class="text-muted">B板&nbsp;:&nbsp;</span>{{produceDetail.takeOverQtySetB}}
      </div>
    </div>
    <div class="row pt-3 pb-3 border-bottom font-weight-bolder">
      <div class="col-4">
        <span class="text-muted"><span v-if="produceDetail.complaintId">修理PCS</span>
          <span v-else>接板PCS</span>&nbsp;:&nbsp;</span>{{produceDetail.takeOverQtyPcsT}}
      </div>
      <div class="col-4" v-if="!produceDetail.complaintId">
        <span class="text-muted">A板&nbsp;:&nbsp;</span>{{produceDetail.takeOverQtyPcsA}}
      </div>
      <div class="col-4" v-if="!produceDetail.complaintId">
        <span class="text-muted">B板&nbsp;:&nbsp;</span>{{produceDetail.takeOverQtyPcsB}}
      </div>
    </div>
    <div class="row pt-3 pb-3 border-bottom font-weight-bolder" v-if="!produceDetail.complaintId && produceDetail.handOverFlag == 2">
      <div class="col-4">
        <span class="text-muted">已交PCS&nbsp;:&nbsp;</span>{{produceDetail.handOverQtyPcsT?produceDetail.handOverQtyPcsT:0}}
      </div>
      <div class="col-4">
        <span class="text-muted">A板&nbsp;:&nbsp;</span>{{produceDetail.handOverQtyPcsA}}
      </div>
      <div class="col-4">
        <span class="text-muted">B板&nbsp;:&nbsp;</span>{{produceDetail.handOverQtyPcsB}}
      </div>
    </div>
    <div class="row pt-3 pb-3 border-bottom font-weight-bolder align-items-center">
      <div class="col-3 text-muted">过数人</div>
      <div class="col-8">
        <select class="form-control form-control-sm" v-model="userId">
          <template v-for="item in produceUsers">
            <option :key="item.recordId" :value="item.recordId">
              {{item.userName}}
            </option>
          </template>
        </select>
      </div>
    </div>
    <div class="row pt-3 pb-3 border-bottom font-weight-bolder align-items-center" v-if="!produceDetail.complaintId">
      <div class="col-12" v-if="produceDetail.handOverFlag == '1'">
        <label class="text-muted">接板检数(PCS)</label>
        <div class="row">
          <div class="col-6">
            A板
            <input class="form-control" :disabled="!produceDetail.takeOverQtyPcsA || produceDetail.takeOverQtyPcsA == 0"
                   v-model="takePcsA" v-on:blur="checkTakeNum(1)">
          </div>
          <div class="col-6">
            B板
            <input class="form-control" :disabled="!produceDetail.takeOverQtyPcsB || produceDetail.takeOverQtyPcsB == 0"
                   v-model="takePcsB" v-on:blur="checkTakeNum(2)">
          </div>
        </div>
      </div>
      <div class="col-12" v-if="produceDetail.handOverFlag == '2'">
        <label class="text-muted">交板检数(PCS)</label>
        <div class="row">
          <div class="col-6">
            A板
            <input class="form-control" :disabled="!produceDetail.takeOverQtyPcsA || produceDetail.takeOverQtyPcsA == 0"
                   v-model="handPcsA" v-on:blur="checkHandNum(1)">
          </div>
          <div class="col-6">
            B板
            <input class="form-control" :disabled="!produceDetail.takeOverQtyPcsB || produceDetail.takeOverQtyPcsB == 0"
                   v-model="handPcsB" v-on:blur="checkHandNum(2)">
          </div>
        </div>
      </div>
    </div>
    <div class="fixed-bottom d-flex p-5">
      <button class="btn btn-primary flex-grow-1 p-3" v-on:click="take" v-if="produceDetail && produceDetail.handOverFlag == '1' && produceDetail.countOverFlag != '1'">接板</button>
      <button class="btn btn-primary flex-grow-1 p-3" v-on:click="hand" v-if="produceDetail && produceDetail.handOverFlag == '2' && produceDetail.countOverFlag != '1'">交板</button>
    </div>
  </div>
</template>

<script>
import {getCompany, getErpList} from "@/assets/js/utils/userAuth";
export default {
name: "produce",
  data(){
    return{
      userId: "",
      processId: "",
      takePcsA: null,
      takePcsB: null,
      handPcsA: null,
      handPcsB: null,
      clickFlag:false,
      multipleCount:"",
      batchId: "",
      batchDetailId: "",
      produceDetail: {},
      produceUsers: []
    }
  },
  mounted() {
    this.userMsg = {};
    this.erpList = [];
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.erpList = getErpList(sysUser);
      this.company = getCompany(sysUser);
      this.userId = this.company.userId;
      this.batchId = this.$route.query.batchId;
      this.batchDetailId = this.$route.query.batchDetailId;
      this.initData();
    }else {
      alert("请重新进入公众号");
    }
  },
  methods:{
    inInformation(item)
    {
      if (!(item && item.cardA && item.cardA.recordId)){
        alert("信息不全，请刷新重试！");
        return;
      }
      this.$router.push({path: '/information',
        query: {
          cardId: item.cardA.recordId,
          craftNo: item.craftNo
        }
      });
    },
    initData() {
      if (!this.company || !this.company.recordId || !this.company.userId){
        alert("无公司信息");
        this.$router.push('/scanWork');
        return;
      }
      if (!this.batchId || !this.batchDetailId){
        alert("扫卡信息有误，请重新扫卡!");
        this.$router.push('/scanWork');
        return;
      }
      let produce = {};
      produce.produceBatch = {};
      produce.produceBatchDetail = {};
      produce.produceBatch.recordId = this.batchId;
      produce.produceBatchDetail.recordId = this.batchDetailId;
      produce.company = this.company;
      produce.userId = this.company.userId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost('f/wechat/produce/getProduceDeail', produce).then((result) => {
        if(result && result.data)
        {
          if (result.data.pbdStatus){
            let message = "";
            switch (result.data.pbdStatus) {
              case "900301":
                message = "系统还未进行投料出库，请先进行仓库出库处理";
                break;
              case "900302":
                message = "本卡被设置暂停了";
                break;
              case "900303":
                message = "本卡被设置缺料了";
                break;
              case "900304":
                message = "本卡已经作废了";
                break;
              case "900305":
                message = "本卡已经过数完成";
                break;
              case "900306":
                message = "本卡已经生产入库了";
                break;
            }
            if (message){
              alert(message);
              this.$router.push('/scanWork');
              return;
            }
          }else if (result.data.errorMsg){
            alert(result.data.errorMsg);
            this.$router.push('/scanWork');
            return;
          }
          if (!(result.data.record && result.data.record.recordId)){
            let message = "系统还未进行投料出库，请先进行仓库出库处理。或者本卡已经生产入库了";
            alert(message);
            this.$router.push('/scanWork');
            return;
          }
          this.produceDetail = result.data.record;
          this.produceDetail.userId = this.company.userId;
          this.setProduceData();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)})
    },
    setProduceData() {
      if(this.produceDetail && this.produceDetail.recordId){
        if(this.produceDetail && this.produceDetail.handOverFlag == '1'){
          // 待接板数量
          this.takePcsA = this.produceDetail.takeOverQtyPcsA ? this.produceDetail.takeOverQtyPcsA : 0;
          this.takePcsB = this.produceDetail.takeOverQtyPcsB ? this.produceDetail.takeOverQtyPcsB : 0;
        }
        if(this.produceDetail && this.produceDetail.handOverFlag == '2'){
          // 接板数量
          let handPcsAall = this.produceDetail.takeCheckNumA ? this.produceDetail.takeCheckNumA : 0;
          let handPcsBall = this.produceDetail.takeCheckNumB ? this.produceDetail.takeCheckNumB : 0;
          // 已交板数量
          let handPcsAread = this.produceDetail.handOverQtyPcsA?this.produceDetail.handOverQtyPcsA:0;
          let handPcsBread = this.produceDetail.handOverQtyPcsB?this.produceDetail.handOverQtyPcsB:0;
          // 待交板数量
          this.handPcsA = Number(handPcsAall) - Number(handPcsAread);
          this.handPcsB = Number(handPcsBall) - Number(handPcsBread);
        }
        // 加载操作人
        this.getProduceUsers();
      }
    },
    getProduceUsers() {
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost('f/wechat/produce/getProduceUsers', this.produceDetail).then((result) => {
        if (result && result.data) {
          this.produceUsers = result.data
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)})
    },
    take:function(){
      // 接板
      if(this.clickFlag) {
        alert("请勿多次点击！");
        return;
      }
      this.produceDeail.takePcsA=this.takePcsA;
      this.produceDeail.takePcsB=this.takePcsB;
      this.produceDeail.handPcsA=this.handPcsA;
      this.produceDeail.handPcsB=this.handPcsB;
      this.produceDeail.createdBy={};
      this.produceDeail.createdBy.recordId = this.userId;
      this.produceDeail.operType=0;
      this.produceDeail.operator=this.userId;
      this.clickFlag = true;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost('f/wechat/produce/add', this.produceDetail).then((result) => {
        if (result && result.data) {
          if(result.data.resultThree == "fail"){
            alert(result.data.messageThree);
          }
          if(result.data.result) {
            if (result.data.messageTwo){
              alert(result.data.message + result.data.messageTwo);
            }else{
              alert(result.data.message);
            }
            this.$router.push('/scanWork');
          } else {
            alert(result.data.message);
          }
        }
        this.clickFlag = false;
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)})
    },
    hand:function(){
      if(this.clickFlag)
      {
        alert("请勿多次点击！");
        return;
      }
      this.produceDeail.takePcsA=this.takePcsA;
      this.produceDeail.takePcsB=this.takePcsB;
      this.produceDeail.handPcsA=this.handPcsA;
      this.produceDeail.handPcsB=this.handPcsB;
      let handPcsA = this.produceDeail.handPcsA?this.produceDeail.handPcsA:0;
      let handPcsB = this.produceDeail.handPcsB?this.produceDeail.handPcsB:0;
      let handPcsT = Number(handPcsA) + Number(handPcsB);
      if (handPcsA < 0){
        alert("A交板数必须大于等于0");
        return;
      }
      if (handPcsB < 0){
        alert("B交板数必须大于等于0");
        return;
      }
      if (handPcsT <= 0 && !this.produceDeail.complaintId){
        alert("交板总数必须大于0才能交板");
        return;
      }
      if(this.produceDeail.complaintId){
        this.produceDeail.handOverQtyPcsA = this.produceDeail.takeOverQtyPcsA;
        this.produceDeail.handOverQtyPcsB = this.produceDeail.takeOverQtyPcsB;
        this.produceDeail.handOverQtyPcsT = this.produceDeail.takeOverQtyPcsT;
      }
      this.produceDeail.lastUpdBy={}
      this.produceDeail.lastUpdBy.recordId = this.userId
      this.produceDeail.operType = 1;
      this.clickFlag = true;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost('f/wechat/produce/add', this.produceDetail).then((result) => {
        if (result && result.data) {
          if(result.data.result) {
            alert(result.data.message);
            this.$router.push('/scanWork');
          } else {
            alert(result.data.message);
          }
        }
        this.clickFlag = false;
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)})
    },
    checkHandNum:function(num){
      if(num == 1)
      {
        if(!this.handPcsA) {
          this.handPcsA = null;
          alert("交板A板数不能为空");
          return;
        }
        if(isNaN(this.handPcsA)) {
          this.handPcsA = null;
          alert("交板A板数必须为数字");
          return;
        }
        if(this.handPcsA < 0) {
          this.handPcsA = null;
          alert("交板A板数必须大于等于0");
          return;
        }
        if(this.handPcsA%1 !== 0 || this.handPcsA.indexOf(".")!=-1) {
          this.handPcsA = null;
          alert("交板A板数必须是整数");
          return;
        }
      }
      // 接板B板检数
      else if(num == 2)
      {
        if(!this.handPcsB) {
          this.handPcsB = null;
          alert("交板B板数不能为空");
          return;
        }
        if(isNaN(this.handPcsB)) {
          this.handPcsB = null;
          alert("交板B板数必须为数字");
          return;
        }
        if(this.handPcsB < 0) {
          this.handPcsB = null;
          alert("交板B板数必须大于等于0");
          return;
        }
        if(this.handPcsB%1 !== 0 || this.handPcsB.indexOf(".")!=-1) {
          this.handPcsB = null;
          alert("交板B板数必须是整数");
          return;
        }
      }
    },
    checkTakeNum:function(num){
      if(num == 1)
      {
        if(!this.takePcsA) {
          this.takePcsA = null;
          alert("接板A板检数不能为空");
          return;
        }
        if(isNaN(this.takePcsA)) {
          this.takePcsA = null;
          alert("接板A板检数必须为数字");
          return;
        }
        if(this.takePcsA < 0) {
          this.takePcsA = null;
          alert("接板A板检数必须大于等于0");
          return;
        }
        if(this.takePcsA%1 !== 0 || this.takePcsA.indexOf(".")!=-1) {
          this.takePcsA = null;
          alert("接板A板检数必须是整数");
          return;
        }
      }
      // 接板B板检数
      else if(num == 2)
      {
        if(!this.takePcsB) {
          this.takePcsB = null;
          alert("接板B板检数不能为空");
          return;
        }
        if(isNaN(this.takePcsB)) {
          this.takePcsB = null;
          alert("接板B板检数必须为数字");
          return;
        }
        if(this.takePcsB < 0) {
          this.takePcsB = null;
          alert("接板B板检数必须大于等于0");
          return;
        }
        if(this.takePcsB%1 !== 0 || this.takePcsB.indexOf(".")!=-1) {
          this.takePcsB = null;
          alert("接板B板检数必须是整数");
          return;
        }
      }
    },
  }
}
</script>
