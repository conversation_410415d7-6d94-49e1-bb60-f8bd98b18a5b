<template>
  <div class="bg-white py-5 px-4 rounded-xl shadow-sm">
    <div class="text-center mb-5">
      <h2 class="font-weight-bold text-gray-800 mb-2" style="font-size: clamp(1.25rem, 3vw, 1.75rem);">
        用心铸就&nbsp;品质好板
      </h2>
      <p class="text-gray-600 mx-auto" style="font-size: clamp(0.9rem, 2vw, 1rem); max-width: 800px;">
        我们秉承工匠精神，不忘初心，坚持
        <span class="text-primary font-weight-bold">做好产品做好人</span>
        的理念，认真做好每一块版
      </p>
    </div>

    <div class="row">
      <div v-for="(item, index) in heroList" :key="index" class="col-6 mb-3 px-2"> <!-- 从mb-4改为mb-3 -->
        <section class="position-relative h-[100px] d-flex overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300"> <!-- 图片高度减小20px -->
          <!-- Background Image -->
          <div class="position-absolute top-0 left-0 w-100 h-full z-0">
            <img
                :src="item.src"
                :alt="item.title"
                class="w-100 h-full object-cover transition-transform duration-500"
                :class="{ 'scale-105': hoverStates[index], 'scale-100': !hoverStates[index] }"
            >
            <div class="position-absolute top-0 left-0 w-100 h-100 bg-dark opacity-50"></div>
          </div>

          <!-- Content -->
          <div class="position-absolute inset-0 z-10 flex flex-col justify-end p-3"> <!-- 内边距从p-4改为p-3 -->
            <div class="font-weight-bold text-white text-shadow mb-1 text-left" style="font-size: clamp(1rem, 2vw, 1.25rem);">
              {{ item.title }}
            </div>
            <div class="text-white text-shadow text-sm text-left" style="font-size: clamp(0.75rem, 1.5vw, 0.875rem); line-height: 1.2;">
              {{ item.subtitle }}
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HeroSection',
  data() {
    return {
      heroList: [
        {
          src: process.env.BASE_URL + 'media/apply/1.png',
          title: '高标准',
          subtitle: 'UL认证、欧盟ROHS、IPC及MIL标准体系'
        },
        {
          src: process.env.BASE_URL + 'media/apply/2.png',
          title: '好板材',
          subtitle: '铝基板、铜基板、单双面多层线路板等产品'
        },
        {
          src: process.env.BASE_URL + 'media/apply/3.png',
          title: '工艺精',
          subtitle: '国际生产标准的自动化生产设备，全套高阶的检测设备'
        },
        {
          src: process.env.BASE_URL + 'media/apply/4.png',
          title: '供应全',
          subtitle: '专属供应链体系，一站式制板服务'
        }
      ],
      hoverStates: [false, false, false, false]
    }
  },
  methods: {
    handleImageHover(index, isHovering) {
      this.hoverStates[index] = isHovering;
    }
  }
}
</script>

<style scoped>
.h-\[100px\] {
  height: 100px; /* 高度从200px减小到180px */
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.font-weight-bold {
  font-weight: 700 !important;
}

.scale-100 {
  transform: scale(1);
}

.scale-105 {
  transform: scale(1.05);
}
</style>
