<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">线路板工厂报价</h3>
    </div>
    <div class="card">
      <div class="card-body p-3">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <div class="text-muted">材料分类</div>
            <select class="form-control form-control-sm" style="width: 12rem;" v-model="quotation.materialTypeId" v-on:change="loadQuotationArrange">
              <option v-for="item in materialTypeList" :key="item.recordId" :value="item.recordId">
                {{ item.materialName }}
              </option>
            </select>
          </div>
          <div>
            <div class="text-muted">工艺分类</div>
            <select class="form-control form-control-sm" style="width: 12rem;" v-model="quotation.craftTypeId" v-on:change="loadQuotationArrange">
              <option v-for="item in craftTypeList" :key="item.recordId" :value="item.recordId">
                {{ item.craftName }}
              </option>
            </select>
          </div>
        </div>
        <div class="text-muted pt-1 pb-1">点击下面蓝色价格可进行修改</div>
        <div class="p-3">
          <div class="row">
            <template v-for="(item, index) in quotationArrangeList">
              <div :class="[index%2===0? 'bg-light-primary':'bg-light-warning', 'col pt-1 pb-1 pl-0 pr-0 text-center']" :key="item.recordId">
                <span class="font-weight-bolder font-size-sm">{{item.arrange}}㎡</span>
              </div>
            </template>
          </div>
          <template v-for="item in interCraftList">
            <div class="row" :key="item.recordId">
              <div class="col-12">
                <div class="row border">
                  <div class="col-12 pt-1 pb-1 font-weight-bolder font-size-base">
                    {{ item.interCraftName }}
                  </div>
                </div>
                <div class="row border">
                  <template v-for="(row, sortIndex) in item.interCraftDetailList">
                    <div :class="[sortIndex%2===0? 'bg-light-primary':'bg-light-warning', 'col pt-1 pb-1']" :key="row.recordId">
                      <span class="text-primary font-size-lg font-weight-bolder" v-on:click="openQuotation(row,item)">{{ isNaN(row.price) ? 0 : row.price}}</span>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div style="height: 150px;"></div>
    <b-modal ref="openQuotation" title="工艺信息" hide-footer>
      <div>
        工艺信息：<span>{{interCraftName}}</span>
      </div>
      <div class="d-flex justify-content-between align-items-center pt-3">
        <div v-if="interArrange">
          区间：<span>{{interArrange}}㎡</span>
        </div>
        <div class="d-flex align-items-center">
          <div>价格：</div>
          <input type="text" class="form-control form-control-sm" style="width: 5rem;" v-model="quotation.price"
                 oninput="this.value = this.value.replace(/^0*(\d+(\.\d*)?)$|^(\.(\d*))?$|^[^\d.].*$|(\d+\.\d*\.|\d*\.0*)/g, '$1').replace(/[^\d.]/g, '');"/>
          <div class="pl-3">元</div>
        </div>
      </div>
      <div class="row pt-7">
        <div class="col-xl-12 text-right">
          <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal" @click="close">取消</button>&nbsp;
          <button type="button" class="btn btn-sm btn-primary" v-on:click="updateQuotationPrice">保存</button>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
export default {
name: "productQuotation",
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return{
      materialTypeList:[],
      craftTypeList:[],
      quotationArrangeList:[],
      interCraftList:[],
      quotation:{
        price:"",
        materialTypeId:"",
        craftTypeId:"",
      },
      processPriceId:"",
      interCraftName:"",
      interArrange: "",
      userMsg:{},
    }
  },
  methods:{
    //换算汇率
    loadData(){
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("is/offer/getQuotationSelectDate").then(result => {
        if (result && result.data) {
          this.materialTypeList = result.data.materialTypeList;
          this.craftTypeList = result.data.craftTypeList;
          if (this.materialTypeList && this.materialTypeList.length > 0)
          {
            this.quotation.materialTypeId = this.materialTypeList[0].recordId;
          }
          if (this.craftTypeList && this.craftTypeList.length > 0)
          {
            this.quotation.craftTypeId = this.craftTypeList[0].recordId;
          }
          this.loadQuotationArrange();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    loadQuotationArrange(){
      const query = {};
      query.craftId = this.quotation.craftTypeId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("is/offer/getLoadQuotationArrange", query).then(result => {
        if (result && result.data) {
          this.quotationArrangeList = result.data;
          this.getInterCraftList();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    getInterCraftList()
    {
      const query = {};
      query.materialTypeId = this.quotation.materialTypeId;
      query.craftIdTypeId = this.quotation.craftTypeId;
      query.number = "1";
      query.userId = this.userMsg.recordId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("is/offer/getInterCraftList", query).then(result => {
        if (result && result.data) {
          this.interCraftList = result.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    openQuotation(row,item){
      this.quotation.price = "";
      this.processPriceId = row.recordId;
      this.quotation.price = row.price;
      this.interArrange = row.arrange;
      this.interCraftName =  item.interCraftName;
      this.quotation.recordId = row.processCustomerId;
      this.$refs['openQuotation'].show();
    },
    updateQuotationPrice()
    {
      const query = {};
      query.processQuotationId = this.processPriceId;
      query.price = this.quotation.price;
      query.quoteCustomerId = this.userMsg.recordId;
      query.recordId = this.quotation.recordId;
      this.$parent.enableLoadFlag(true);
      const _this = this;
      this.$axios.fetchPost("is/offer/updateQuotationPrice", query).then(result => {
        if (result.data === "success")
        {
          alert("报价成功!");
          _this.getInterCraftList();
          _this.$refs['openQuotation'].hide();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    close(){
      this.$refs['openQuotation'].hide();
    },
  }
}
</script>