<template>
  <div>
    <div class="bg-white rounded-sm p-2" v-if="commentEntity.recordId">
      <div class="d-flex justify-content-between align-items-center pb-2 border-bottom">
        <div class="font-weight-bolder">更新动态</div>
        <button class="btn btn-primary px-2 py-1" v-on:click="comment()">发布</button>
      </div>
      <template v-for="item in commentEntity.commentList">
        <div class="pt-2 pb-2 border-bottom" :key="item.recordId" v-if="!item.commentId && !item.commentFaId && item.user && item.user.downloadUrl">
          <div class="d-flex align-items-center text-primary">
            <div class="image-container-common mr-1 mb-0" style="width: 2rem;height: 2rem;"><img :src="item.user.downloadUrl"></div>
            <div>{{item.name}}</div>
          </div>
          <div class="pt-1" style="word-break: break-all;">
            {{item.content}}
          </div>
          <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">{{item.createdDateStr}}</div>
            <button class="btn btn-sm btn-outline-primary px-2 py-1" v-on:click="replyComment(item)">反馈</button>
          </div>
          <template v-for="row in commentEntity.commentList">
            <div class="alert-secondary pt-2 pb-2 mt-2" :key="row.recordId" v-if="row.commentFaId == item.recordId">
              <div class="d-flex align-items-center text-primary">
                <div class="image-container-common mr-1 mb-0" style="width: 2rem;height: 2rem;"><img :src="row.user.downloadUrl"></div>
                <div>{{row.name}}-{{row.replyName ? row.replyName : item.name}}</div>
              </div>
              <div class="pt-1" style="word-break: break-all;">
                {{row.content}}
              </div>
              <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">{{row.createdDateStr}}</div>
                <button class="btn btn-sm btn-outline-primary px-2 py-1" v-on:click="replyCom(item,row)">反馈</button>
              </div>
            </div>
          </template>
        </div>
      </template>
    </div>
    <b-modal ref="comment" hide-footer hide-header>
      <div>
        <div class="font-size-h5 pb-3">内容</div>
        <textarea class="form-control" rows="5" v-model="comments.content"></textarea>
        <div class="row pt-7">
          <div class="col-12 text-right">
            <button type="button" class="btn btn-secondary" v-on:click="hideComment">取消</button>&nbsp;
            <button type="button" class="btn btn-primary" v-on:click="commit">发布</button>
          </div>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
export default {
  name: "comment",
  props: {
    commentEntity: Object,
    entityFlag: String
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.employeeId){
        return;
      }
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return{
      userMsg: {},
      comments: {},
    }
  },
  methods:{
    comment() {
      this.$refs['comment'].show();
    },
    replyComment(item) {
      if(item && item.recordId){
        this.comments.commentFaId = item.recordId;
      }
      this.$refs['comment'].show();
    },
    replyCom(item, row) {
      if(item && item.recordId){
        this.comments.commentFaId = item.recordId;
      }
      if(row && row.recordId){
        this.comments.commentId = row.recordId;
      }
      this.$refs['comment'].show();
    },
    hideComment() {
      this.$refs['comment'].hide();
    },
    commit: function(){
      if(!this.comments || !this.comments.content){
        alert("请填写评论内容");
      }
      if (this.entityFlag == "task"){
        this.comments.taskId = this.commentEntity.recordId;
      }else if (this.entityFlag == "report"){
        this.comments.dailyId = this.commentEntity.recordId;
      }else if (this.entityFlag == "audit"){
        this.comments.auditId = this.commentEntity.recordId;
      }else if (this.entityFlag == "dayThing"){
        this.comments.dayThingId = this.commentEntity.recordId;
      }else {
        alert("找不到业务模型，请刷新重试");
        return;
      }
      this.comments.createdBy = this.userMsg.employeeId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/addAuditComment",this.comments).then(result => {
        if(result.data == "success"){
          alert('发布成功！');
          this.comments = {};
          this.$parent.loadData();
        }else{
          alert('发布失败！');
        }
        this.$refs['comment'].hide();
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
  }
}
</script>
