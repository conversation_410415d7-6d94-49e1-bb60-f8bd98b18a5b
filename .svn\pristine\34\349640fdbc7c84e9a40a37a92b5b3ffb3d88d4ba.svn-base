<template>
  <div class="d-flex flex-column flex-root">
    <LoadingModal v-if="loadingFlag"></LoadingModal>
    <Loader v-if="loaderEnabled" v-bind:logo="loaderLogo"></Loader>
    <div class="d-flex flex-row flex-column-fluid page">
      <div class="d-flex flex-column flex-row-fluid wrapper">
        <div class="content d-flex flex-column flex-column-fluid"
             :style="{ backgroundImage: `url(${backgroundImage})`,
              backgroundSize: '100% 100px',backgroundPosition: 'top'}">
          <div class="d-flex flex-column-fluid">
            <div class="container-fluid">
              <transition name="fade-in-up">
                <router-view />
              </transition>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Loader from "@/view/content/Loader.vue";
import LoadingModal from "@/view/content/LoadingModal.vue";
import {
  ADD_BODY_CLASSNAME,
  REMOVE_BODY_CLASSNAME
} from "@/core/services/store/htmlclass.module.js";

export default {
  name: "Layout",
  components: {
    Loader,
    LoadingModal
  },
  beforeMount() {
    // show page loading
    this.$store.dispatch(ADD_BODY_CLASSNAME, "page-loading");
    // Simulate the delay page loading
    setTimeout(() => {
      // Remove page loader after some time
      this.$store.dispatch(REMOVE_BODY_CLASSNAME, "page-loading");
    }, 2000);
  },
  data() {
    return{
      loadingFlag: false
    }
  },
  methods: {
    enableLoadFlag(flag) {
      this.loadingFlag = flag;
    }
  },
  computed: {
    ...mapGetters([
      "layoutConfig"
    ]),

    /**
     * Check if the page loader is enabled
     * @returns {boolean}
     */
    loaderEnabled() {
      return !/false/.test(this.layoutConfig("loader.type"));
    },

    /**
     * Page loader logo image using require() function
     * @returns {string}
     */
    loaderLogo() {
      return process.env.BASE_URL + "media/logos/kyb.jpg";
    },
    backgroundImage() {
      return process.env.BASE_URL + "media/bg/bg-10.jpg";
    }
  }
};
</script>
