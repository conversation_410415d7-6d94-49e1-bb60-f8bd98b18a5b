<template>
  <div>
    <div class="d-flex justify-content-between">
      <div>
        <div class="font-size-h5 font-weight-bolder">{{userMsg.departmentName}}-{{userMsg.empName}}</div>
        <div class="text-muted">{{getCurrentDate()}}</div>
      </div>
      <div><button class="btn btn-sm btn-outline-white px-3 py-1" v-on:click="routerFilter()">小助手</button></div>
    </div>
    <NoticeBar></NoticeBar>
    <div class="m-1">
      <div class="row pl-1 pr-1">
        <template v-for="item in functionNavs">
          <div class="col pt-2 text-center" :key="item.title" v-on:click="showList(item.id)">
            <div v-if="item.id == 1"><SQUARE :name="item.title" :num="auditList ? auditList.length > 100 ? '99+' : auditList.length + '' : '0'"></SQUARE></div>
            <div v-if="item.id == 2"><SQUARE :name="item.title" :num="dayThingList ? dayThingList.length > 100 ? '99+' : dayThingList.length + '' : '0'"></SQUARE></div>
            <div v-if="item.id == 3"><SQUARE :name="item.title" :num="reportList ? reportList.length > 100 ? '99+' : reportList.length + '' : '0'"></SQUARE></div>
            <div v-if="item.id == 4"><SQUARE :name="item.title" :num="taskList ? taskList.length > 100 ? '99+' : taskList.length + '' : '0'"></SQUARE></div>
            <div v-if="item.id == 5"><SQUARE :name="item.title" :num="performanceList ? performanceList.length > 100 ? '99+' : performanceList.length + '' : '0'"></SQUARE></div>
            <div v-if="item.id == 6"><SQUARE :name="item.title" num="0"></SQUARE></div>
          </div>
        </template>
      </div>
    </div>
    <div class="m-1 mt-3" v-if="roleItem && roleItem.length > 0 && jscList && jscList.length > 0">
      <div class="d-flex justify-content-between align-items-center pt-1 pb-1">
        <div>
          <button class="btn btn-sm px-1 py-1 mr-2 btn-outline-white" v-on:click="openFilter">筛选</button>
          <template v-for="item in reportDateList">
            <button :key="item.id" :class="['btn btn-sm px-1 py-1 mr-2', reportDateFlag && reportDateFlag == item.id ? 'btn-white' : 'btn-outline-white']" v-on:click="getReportDate(item.id)">{{ item.title }}</button>
          </template>
        </div>
        <div class="d-flex">
          <span v-if="showDate">{{ showDate }}</span>
        </div>
      </div>
      <div class="jsc_card_pox3 p-1 mt-1" style="background-color: rgba(4,51,77,0.6)">
        <template v-for="(item, index) in jscList">
          <div class="pt-1" :key="index" v-if="showItem(item.item)">
            <div class="d-flex align-items-center" v-if="item.list && item.list.length > 0">
              <div class="p-1 font-weight-bolder">{{ item.name }}</div>
              <template v-for="(row, id) in item.list">
                <div class="p-1" :key="id">
                  <span>{{row.name}}:&nbsp;</span>
                  <span class="text-warning" v-if="row.name.includes('率')">{{row.value}}</span>
                  <span class="text-warning" v-else>{{bigNumberTransform(row.value)}}</span>
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="m-1" v-if="orgMap">
      <div v-if="showItem('接单')">
        <PAGERANK name="接单" :showDate="showDate" :normalRanking="orgMap.custRankingA" :custRankingM="orgMap.custRankingM" :custRankingP="orgMap.custRankingP"
                  :departRankingA="orgMap.departRankingA" :departRankingM="orgMap.departRankingM" :departRankingP="orgMap.departRankingP" :saleRankingA="orgMap.saleRankingA"
                  :saleRankingM="orgMap.saleRankingM" :saleRankingP="orgMap.saleRankingP" :userRankingA="orgMap.userRankingA" :userRankingM="orgMap.userRankingM"
                  :userRankingP="orgMap.userRankingP" :showList="['1','2','3','4']" showFlag="1">
        </PAGERANK>
      </div>
      <div v-if="showItem('出货')">
        <PAGERANK name="销售" :showDate="showDate" :normalRanking="orgMap.saleCustRankingA" :custRankingM="orgMap.saleCustRankingM" :custRankingP="orgMap.saleCustRankingP"
                  :departRankingA="orgMap.saleDepartRankingA" :departRankingM="orgMap.saleDepartRankingM" :departRankingP="orgMap.saleDepartRankingP"
                  :saleRankingA="orgMap.saleSaleRankingA" :saleRankingM="orgMap.saleSaleRankingM" :saleRankingP="orgMap.saleSaleRankingP"
                  :userRankingA="orgMap.saleUserRankingA" :userRankingM="orgMap.saleUserRankingM" :userRankingP="orgMap.saleUserRankingP" :showList="['1','2','3','4']" showFlag="1">
        </PAGERANK>
      </div>
      <div v-if="showItem('回款')">
        <PAGERANK name="应收" :showDate="showDate" :normalRanking="orgMap.financeCustRankingW" :departRankingA="orgMap.financeDepartRankingW" :companyRanking="orgMap.financeCompanyRankingW"
                  :periodRanking="orgMap.financePeriodRankingW" :showList="['5','6','7','8']" showFlag="5">
        </PAGERANK>
        <PAGERANK name="应付" :showDate="showDate" :normalRanking="orgMap.financeSupplierRankingW" :companyRanking="orgMap.financeCompanySupRankingW" :periodRanking="orgMap.financePeriodSupRankingW"
                  :showList="['9','7','8']" showFlag="9">
        </PAGERANK>
      </div>
      <div v-if="showItem('品质')">
        <PAGERANK name="客诉" :showDate="showDate" :normalRanking="orgMap.custAllQuatityRanking" showFlag="5"></PAGERANK>
        <PAGERANK name="报废" :showDate="showDate" :normalRanking="orgMap.selfAllQuatityRanking" showFlag="5"></PAGERANK>
      </div>
      <div v-if="showItem('采购')">
        <PAGERANK name="成品" :showDate="showDate" :normalRanking="orgMap.productAllRanking" :companyRanking="orgMap.productAllComRanking" :showList="['9','7']" showFlag="9"></PAGERANK>
        <PAGERANK name="原料" :showDate="showDate" :normalRanking="orgMap.purchAllRanking" :companyRanking="orgMap.purchAllComRanking" :showList="['9','7']" showFlag="9"></PAGERANK>
      </div>
      <div style="height: 100px;"></div>
    </div>
    <b-modal ref="showList" hide-footer hide-header>
      <div>
        <div class="d-flex justify-content-between align-items-center font-weight-bolder font-size-lg border-bottom pb-3">
          <span v-if="currId == '1'">待办审批</span>
          <span v-if="currId == '2'">{{flag == '1' ? "当日" : "本周"}}日程</span>
          <span v-if="currId == '3'">未读报告</span>
          <span v-if="currId == '4'">我的任务</span>
          <span v-if="currId == '5'">绩效评估</span>
          <span v-if="currId == '6'">{{flag == '1' ? "当日" : "本周"}}生产</span>
          <button class="btn btn-sm btn-outline-danger px-2 py-1" v-on:click="closeShowList">关闭</button>
        </div>
        <div class="pt-3" v-if="currId == 1"><SHAREAUDIT :auditList="auditList"></SHAREAUDIT></div>
        <div class="pt-3" v-if="currId == 2"><SHAREDAYTHING :dayThingList="dayThingList"></SHAREDAYTHING></div>
        <div class="pt-3" v-if="currId == 3"><SHAREREPORT :reportList="reportList"></SHAREREPORT></div>
        <div class="pt-3" v-if="currId == 4"><SHARETASK :taskList="taskList"></SHARETASK></div>
        <div class="pt-3" v-if="currId == 5"><SHAREPERFORMANCE :performanceList="performanceList" :empId="userMsg.employeeId"></SHAREPERFORMANCE></div>
      </div>
    </b-modal>
    <b-modal ref="routerList" hide-footer hide-header>
      <div>
        <div class="font-weight-bolder border-bottom pb-3">系统管理</div>
        <template v-for="(item, index) in functionTitles">
          <div class="d-flex justify-content-between align-items-center p-3 font-size-lg bg-white mt-3 border mb-3" :key="index">
            <div class="d-flex align-items-center">
              <div :class="['d-flex justify-content-center align-items-center alert-' + item.iconColor]" style="width: 3rem; height: 3rem;">
                <i class="fa" :class="[item.icon, 'text-' + item.iconColor]" aria-hidden="true"></i>
              </div>
              <div class="ml-3">
                <div style="font-size: 1.2rem;">{{ item.title }}</div>
              </div>
            </div>
            <button class="btn btn-sm btn-outline-primary" @click="router_page(item.routerName)">前往<i class="fa fa-arrow-alt-circle-right ml-3"></i></button>
          </div>
        </template>
      </div>
    </b-modal>
    <b-modal ref="filter" hide-footer hide-header>
      <div>
        <div class="font-weight-bolder border-bottom pb-3">报表筛选</div>
        <div class="pt-3">
          <span class="text-muted">统计模式</span>
          <div class="pt-1">
            <button :class="['btn btn-sm', mergeQuery == '1' ? 'btn-primary' : 'btn-outline-primary']" style="width: 60px;" v-on:click="setMergeQuery(1)">合并</button>
            <button :class="['btn btn-sm ml-3', mergeQuery == '2' ? 'btn-primary' : 'btn-outline-primary']" style="width: 60px;" v-on:click="setMergeQuery(2)">不合并</button>
          </div>
        </div>
        <div class="pt-3 pb-3">
          <span class="text-muted">时间范围</span>
          <div class="pt-1">
            <button :class="['btn btn-sm', dateQuery == '1' ? 'btn-primary' : 'btn-outline-primary']" style="width: 60px;" v-on:click="setDateQuery(1)">本期</button>
            <button :class="['btn btn-sm ml-3', dateQuery == '2' ? 'btn-primary' : 'btn-outline-primary']" style="width: 60px;" v-on:click="setDateQuery(2)">上期</button>
          </div>
        </div>
        <div class="d-flex justify-content-end pt-6 border-top">
          <button class="btn btn-outline-secondary" v-on:click="cancelFilter">清空</button>
          <button class="btn btn-outline-primary ml-3" v-on:click="applyFilter">应用</button>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
import SQUARE from "@/view/pages/wx/report/utils/square/square";
import NoticeBar from '@/view/pages/wx/business/mainUtils/NoticeBar';
import SHAREAUDIT from '@/view/pages/wx/kybsoft/work/shareTable/shareAudit';
import SHAREDAYTHING from '@/view/pages/wx/kybsoft/work/shareTable/shareDayThing';
import SHAREPERFORMANCE from '@/view/pages/wx/kybsoft/work/shareTable/sharePerformance';
import SHAREREPORT from '@/view/pages/wx/kybsoft/work/shareTable/shareReport';
import SHARETASK from '@/view/pages/wx/kybsoft/work/shareTable/shareTask';
import PAGERANK from "@/view/pages/wx/report/utils/rank/pageRank";
import "@/view/pages/wx/report/utils/square/square.css";
export default {
  name: "worktable",
  components: {
    SQUARE,
    NoticeBar,
    SHAREAUDIT,
    SHAREDAYTHING,
    SHAREPERFORMANCE,
    SHAREREPORT,
    SHARETASK,
    PAGERANK
  },
  props: {
    functionTitles: {
      type: Array,
      default: () => [
        {title: "OA", icon: "fa-users", iconColor: "primary", routerName: "/work"},
        {title: "MES工具", icon: "fa-cogs", iconColor: "warning", routerName: "/scanWork"},
        {title: "物流系统", icon: "fa-truck", iconColor: "success", routerName: "/logistics"},
        {title: "制度管理", icon: "fa-file", iconColor: "danger", routerName: "/mechanism"},
        // {title: "档案管理", icon: "fa-file", iconColor: "danger", routerName: "/"},
        // {title: "报价管理", icon: "fa-file", iconColor: "danger", routerName: "/"},
        // {title: "系统库存", icon: "fa-file", iconColor: "danger", routerName: "/"},
        // {title: "WIP", icon: "fa-file", iconColor: "danger", routerName: "/"},
        // {title: "更多报表", icon: "fa-fighter-jet", iconColor: "warning", routerName: "/"},
      ]
    },
    functionNavs: {
      type: Array,
      default: () => [
        {id: 1, title: "审批", iconClass: "fa-check-circle", iconBg: "#346dd5", color: 'primary', title1: '待处理', title2: '高优先级', font_color: '#0f4bd3'},
        {id: 2, title: "日程", iconClass: "fa-calendar", iconBg: "#0eab2a", color: 'success', title1: '今日', title2: '中优先级', font_color: '#13be1d'},
        {id: 3, title: "报告", iconClass: "fa-file-alt", iconBg: "#774ef3", color: 'info', title1: '待查看', title2: '中优先级', font_color: '#7b18f3'},
        {id: 4, title: "任务", iconClass: "fa-list", iconBg: "#e39b6f", color: 'warning', title1: '进行中', title2: '高优先级', font_color: '#c7620e'},
        {id: 5, title: "绩效", iconClass: "fa-star", iconBg: "#bd444d", color: 'danger', title1: '待评估', title2: '高优先级', font_color: '#940b0b'},
        // {id: 6, title: "生产", iconClass: "fa-cogs", iconBg: "#e8641d", color: 'warning', title1: '今日', title2: '高优先级', font_color: '#7a470e'},
      ],
    },
    reportDateList: {
      type: Array,
      default: () => [
        {id: '1', title: "日"}, {id: '2', title: "周"}, {id: '3', title: "月"}, {id: '4', title: "季"}, {id: '5', title: "年"},
      ],
    }
  },
  data() {
    return {
      auditList: [],
      dayThingList: [],
      reportList: [],
      taskList: [],
      performanceList: [],
      userMsg: {},
      flag: "1",
      currId: "",
      reportDateFlag: "3",
      roleItem: [],
      report: {},
      showDate: "",
      orgMap: {},
      jscList: [],
      mergeQuery: '1',
      dateQuery: '1',
    }
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.employeeId){
        return;
      }
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  methods: {
    setDateQuery(val) {
      this.dateQuery = val;
    },
    setMergeQuery(val) {
      this.mergeQuery = val;
    },
    cancelFilter() {
      this.dateQuery = '1';
      this.mergeQuery = '1';
      this.$refs['filter'].hide();
      this.getReportData();
    },
    applyFilter() {
      this.$refs['filter'].hide();
      this.getReportData();
    },
    openFilter() {
      this.$refs['filter'].show();
    },
    showItem(name) {
      if (this.roleItem && this.roleItem.length > 0){
        for (let i=0;i<this.roleItem.length;i++){
          if (this.roleItem[i].name == name){
            return true;
          }
        }
      }
      return false;
    },
    getReportDate(flag) {
      this.reportDateFlag = flag;
      this.getReportData();
    },
    getReportData() {
      this.orgMap = {};
      this.jscList = [];
      // 加载报表权限
      this.roleItem = this.userMsg.itemList;
      if (!(this.roleItem && this.roleItem.length > 0)){
        return;
      }
      // 加载报表
      const query = {};
      query.dateType = this.reportDateFlag;
      query.userId = this.userMsg.recordId;
      query.mergeQuery = this.mergeQuery;
      query.dateQuery = this.dateQuery;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybReport/getHomePageReport", query).then(result => {
        if (result && result.data) {
          if (result.data.message && result.data.message == "success"){
            this.orgMap = result.data;
            this.showDate = result.data.showDate;
            this.initReport(result.data);
          }else {
            alert(result.data.message);
          }
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    initReport(item) {
      // 接单
      // 本期计划、年度计划、金额/面积
      // 接单金额、净利润、净利润率、计划达成
      // 接单面积、丝印曝光统计、计划达成
      // 销售金额、面积、利润、利润率
      // 客诉笔数、客诉率、交期达成率
      // 成交客户数量、成交率
      let order = {};
      order.name = "接单";
      order.item = "接单";
      order.list = [];
      order.list.push(this.setReportValue("面积", item.orderAllArea ? item.orderAllArea : '0'));
      order.list.push(this.setReportValue("金额", item.orderAllMoney ? item.orderAllMoney : '0'));
      order.list.push(this.setReportValue("利润", item.orderAllProfit ? item.orderAllProfit : '0'));
      order.list.push(this.setReportValue("利润率", this.countRadio(item.orderAllProfit, item.orderAllMoney)));
      this.jscList.push(order);
      // 销售
      let sale = {};
      sale.name = "销售";
      sale.item = "出货";
      sale.list = [];
      sale.list.push(this.setReportValue("面积", item.saleAllArea ? item.saleAllArea : '0'));
      sale.list.push(this.setReportValue("金额", item.saleAllMoney ? item.saleAllMoney : '0'));
      sale.list.push(this.setReportValue("利润", item.saleAllProfit ? item.saleAllProfit : '0'));
      sale.list.push(this.setReportValue("利润率", this.countRadio(item.saleAllProfit, item.saleAllMoney)));
      this.jscList.push(sale);
      // 财务
      let finance = {};
      finance.name = "财务";
      finance.item = "回款";
      finance.list = [];
      finance.list.push(this.setReportValue("应收", item.financeAllIn ? item.financeAllIn : '0'));
      finance.list.push(this.setReportValue("应付", item.financeAllOut ? item.financeAllOut : '0'));
      this.jscList.push(finance);
      // 品质
      let quatity = {};
      quatity.name = "品质";
      quatity.item = "品质";
      quatity.list = [];
      // quatity.list.push(this.setReportValue("客诉", item.custAllQuatity ? item.custAllQuatity : '0'));
      quatity.list.push(this.setReportValue("客诉率", this.countRadio(item.custAllNum, item.saleAllNum)));
      quatity.list.push(this.setReportValue("报废", item.selfAllQuatity ? item.selfAllQuatity : '0'));
      quatity.list.push(this.setReportValue("补料", item.addAllArea ? item.addAllArea : '0'));
      this.jscList.push(quatity);
      // 采购
      let purch = {};
      purch.name = "采购";
      purch.item = "采购";
      purch.list = [];
      purch.list.push(this.setReportValue("成品", item.productAllMoney ? item.productAllMoney : '0'));
      // purch.list.push(this.setReportValue("成品款数", item.productAllNum ? item.productAllNum : '0'));
      purch.list.push(this.setReportValue("原料", item.purchAllMoney ? item.purchAllMoney : '0'));
      // purch.list.push(this.setReportValue("原料款数", item.purchAllNum ? item.purchAllNum : '0'));
      this.jscList.push(purch);

      // 生产
      let production = {};
      production.name = "生产";
      production.item = "生产";
      production.list = [];
      production.list.push(this.setReportValue("投料出库", item.feedingOutAllArea ? item.feedingOutAllArea : '0'));
      production.list.push(this.setReportValue("生产入库", item.productionStorageAllArea ? item.productionStorageAllArea : '0'));
      production.list.push(this.setReportValue("结存", item.balanceAllArea ? item.balanceAllArea : '0'));
      this.jscList.push(production);
    },
    setReportValue(name, value) {
      let param = {};
      param.name = name;
      param.value = value;
      return param;
    },
    showList(id) {
      this.currId = id;
      this.$refs['showList'].show();
    },
    closeShowList() {
      this.currId = '';
      this.$refs['showList'].hide();
    },
    getCurrentDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
      const weekday = weekdays[now.getDay()];
      let extraInfo = '';
      const isSpecialDay = this.checkSpecialDay(month, day);
      if (isSpecialDay) {
        extraInfo = ` - ${isSpecialDay}`;
      }
      return `今天是${year}年${month}月${day}日，星期${weekday}${extraInfo}`;
    },
    checkSpecialDay(month, day) {
      if (month === 1 && day === 1) return '元旦';
      if (month === 5 && day === 1) return '劳动节';
      if (month === 10 && day === 1) return '国庆节';
      return '';
    },
    router_page(name) {
      this.$router.push(name);
    },
    loadData() {
      // 加载待审批清单
      this.loadAuditData();
      // 加载当日/本周日程清单
      this.loadDayThingData();
      // 加载待读报告
      this.loadReportData();
      // 加载未完成任务
      this.loadTaskData();
      //加载绩效考核
      this.loadPerformanceData();
      // 加载报表
      this.getReportData();
    },
    loadAuditData() {
      this.auditList = [];
      let query = {};
      query.empId = this.userMsg.employeeId;
      query.flag = "1";
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/getAuditList", query).then(result => {
        if (result && result.data && result.data.length > 0) {
          this.auditList = result.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    loadDayThingData() {
      this.dayThingList = [];
      let query = {};
      query.empId = this.userMsg.employeeId;
      query.flag = this.flag;
      query.status = "2";
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybDayThing/getDayThingList", query).then(result => {
        if (result && result.data && result.data.length > 0) {
          this.dayThingList = result.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    loadReportData() {
      this.reportList = [];
      let query = {};
      query.empId = this.userMsg.employeeId;
      query.readType = "1";
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost('f/wechat/kybsoftOA/getReportList', query).then((result) => {
        if (result && result.data && result.data.length > 0) {
          this.reportList = result.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)})
    },
    loadTaskData() {
      let query = {};
      query.empId = this.userMsg.employeeId;
      query.principals = this.userMsg.employeeId;
      query.finsh = "5"; //表示未结束
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/getTaskList", query).then(result => {
        if (result && result.data && result.data.length > 0) {
          this.taskList = result.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    routerFilter() {
      this.$refs['routerList'].show();
    },
    loadPerformanceData() {
      let query = {};
      query.recordId = this.userMsg.employeeId;
      query.status = '1001';
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/getPerformanceData", query).then(result => {
        if (result && result.data && result.data.length > 0) {
          this.performanceList = result.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    countRadio(num, allNum){
      num = Number(num);
      allNum = Number(allNum);
      if (num && num > 0){
        if (allNum && allNum > 0 && num < allNum) {
          return (num / allNum * 100).toFixed(2) + "%";
        }else {
          return "100%";
        }
      }else {
        return "0%"
      }
    },
    bigNumberTransform(value) {
      value = Number(value);
      const newValue = ['', '', '']
      let fr = 1000
      let num = 3
      let text1 = ''
      let fm = 1
      while (value / fr >= 1) {
        fr *= 10
        num += 1
      }
      if (num <= 4) {
        newValue[0] = value.toFixed(2);
      } else if (num <= 8) { // 万
        text1 = parseInt(num - 4) / 3 > 1 ? '千万' : '万'
        fm = text1 === '万' ? 10000 : 10000000
        if (value % fm === 0) {
          newValue[0] = parseInt(value / fm) + ''
        } else {
          newValue[0] = parseFloat(value / fm).toFixed(2) + ''
        }
        newValue[1] = text1
      } else if (num <= 16) { // 亿
        text1 = (num - 8) / 3 > 1 ? '千亿' : '亿'
        text1 = (num - 8) / 4 > 1 ? '万亿' : text1
        text1 = (num - 8) / 7 > 1 ? '千万亿' : text1
        fm = 1
        if (text1 === '亿') {
          fm = 100000000
        } else if (text1 === '千亿') {
          fm = 100000000000
        } else if (text1 === '万亿') {
          fm = 1000000000000
        } else if (text1 === '千万亿') {
          fm = 1000000000000000
        }
        if (value % fm === 0) {
          newValue[0] = parseInt(value / fm) + ''
        } else {
          newValue[0] = parseFloat(value / fm).toFixed(2) + ''
        }
        newValue[1] = text1
      }
      if (value < 1000) {
        newValue[0] = value + ''
        newValue[1] = ''
      }
      return newValue.join('')
    }
  },
}
</script>