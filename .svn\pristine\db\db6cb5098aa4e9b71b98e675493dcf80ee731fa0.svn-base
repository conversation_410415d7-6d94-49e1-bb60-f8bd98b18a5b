<template>
  <div>
    <template v-for="item in workList">
      <section class="mb-6" :key="item.recordId">
        <div class="text-dark font-weight-bolder font-size-lg pb-3">
          <i :class="['fa', item.iconClass, 'text-primary', 'pr-3']"></i>{{ item.name }}
        </div>
        <div class="sub_work-cards" v-if="item.itemList && item.itemList.length > 0">
          <template v-for="row in item.itemList">
            <div :key="row.recordId" v-on:click="router_page(item, row)" v-if="item.id != 3 || (row.attachments && row.attachments == '2' && row.status == '1')">
              <div :class="['sub_work-card', 'sub_card', 'border-top', 'border-' + row.color]">
                <div :class="['sub_icon', 'alert-' + row.color]">
                  <i :class="['fa', row.iconClass, 'text-' + row.color]"></i>
                </div>
                <span :class="['text-' + row.color,'ellipsis', 'text-truncate']" style="width: 6rem;">{{ row.name }}</span>
              </div>
            </div>
          </template>
        </div>
      </section>
    </template>
  </div>
</template>

<script>
export default {
  name: "submitSquare",
  props: {
    workList: Array,
    organizationId: String,
    organizationName: String
  },
  methods: {
    router_page(item, row) {
      if (!this.organizationId){
        alert("请选择组织架构");
        return;
      }
      if (item.id == '3'){
        // 审批
        this.$router.push({path: "/audit/auditDetails",
          query: {organizationId: this.organizationId, organizationName: this.organizationName, auditTypeId: row.recordId}
        });
      }else if (item.id == '2'){
        // 报告
        this.$router.push({path: row.routerName,
          query: {organizationId: this.organizationId, organizationName: this.organizationName, reportType: row.reportType}
        });
      }else if (item.id == '1'){
        // 任务、日程
        this.$router.push({path: row.routerName,
          query: {organizationId: this.organizationId}
        });
      }
    }
  }
}
</script>

<style scoped>
/* 卡片样式 */
.sub_card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0rem 0rem;
}

/* 工作区域卡片 */
.sub_work-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

.sub_work-card {
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  min-height: 80px;
}

.sub_work-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
}

.sub_work-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.sub_work-card .sub_icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.5rem auto;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.ellipsis {
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 溢出隐藏 */
  text-overflow: ellipsis; /* 溢出时显示省略号 */
  max-width: 200px; /* 需要指定最大宽度 */
}
</style>
