<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">消息通知</h3>
    </div>
    <div class="card">
      <div class="card-body p-3">
        <div>
          <input type="text" class="form-control form-control-sm" placeholder="搜索通知内容" v-model="condition" v-on:blur="getNoticeList"/>
        </div>
        <template v-for="row in noticeList">
          <div class="pt-7 pb-3 pl-3 pr-3 border-bottom" :key="row.recordId">
            <div>
              <span>发布人:{{row.name}}</span><span class="pl-3">组织架构:{{row.path}}</span>
            </div>
            <div class="pt-3" v-html="row.content"></div>
            <div class="d-flex justify-content-end pt-3 text-muted">
              发布时间:{{row.createdDate}}
            </div>
          </div>
        </template>
      </div>
    </div>
    <div style="height: 100px;"></div>
  </div>
</template>
<script>
export default {
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.getNoticeList();
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return{
      noticeList: [],
      condition: "",
      userMsg:{},
    }
  },
  methods:{
    getNoticeList(){
      const notice ={};
      notice.groupId = this.userMsg.departId;
      notice.departId = this.userMsg.groupId;
      notice.condition = this.condition;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost('kybOa/getNoticeList', notice).then((data) => {
        if (data && data.data) {
          this.noticeList = data.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)})
    }
  }
}
</script>
