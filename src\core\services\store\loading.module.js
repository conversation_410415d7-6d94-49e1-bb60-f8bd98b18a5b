// action types
export const SHOW_PAGE_LOADING = "showPageLoading";
export const HIDE_PAGE_LOADING = "hidePageLoading";
export const SET_LOADING_MESSAGE = "setLoadingMessage";

// mutation types
export const SET_PAGE_LOADING = "setPageLoading";
export const SET_LOADING_TEXT = "setLoadingText";

const state = {
  isPageLoading: false,
  loadingMessage: "加载中..."
};

const getters = {
  isPageLoading(state) {
    return state.isPageLoading;
  },
  loadingMessage(state) {
    return state.loadingMessage;
  }
};

const actions = {
  [SHOW_PAGE_LOADING]({ commit }, message = "加载中...") {
    commit(SET_LOADING_TEXT, message);
    commit(SET_PAGE_LOADING, true);
  },
  [HIDE_PAGE_LOADING]({ commit }) {
    commit(SET_PAGE_LOADING, false);
  },
  [SET_LOADING_MESSAGE]({ commit }, message) {
    commit(SET_LOADING_TEXT, message);
  }
};

const mutations = {
  [SET_PAGE_LOADING](state, isLoading) {
    state.isPageLoading = isLoading;
  },
  [SET_LOADING_TEXT](state, message) {
    state.loadingMessage = message;
  }
};

export default {
  state,
  actions,
  mutations,
  getters
};
