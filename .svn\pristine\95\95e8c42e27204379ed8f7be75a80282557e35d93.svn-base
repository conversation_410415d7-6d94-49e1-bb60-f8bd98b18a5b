<template>
  <div class="bg-white rounded-sm p-3">
    <label class="font-weight-bolder font-size-h4">{{craftNo}}的生产资料</label>
    <div class="pt-3">
      <label class="badge badge-secondary font-weight-bolder">
        大板
      </label>
    </div>
    <div class="row pt-3 pb-7 border-bottom">
      <div class="col-12" v-if="produceFile.dFileUrl != null && produceFile.dFileUrl != ''">
        <img :src="produceFile.dFileUrl" class="w-100">
      </div>
      <div class="col-12 text-danger font-weight-bolder" v-else>
        <span class="pl-3">系统未上传大板资料</span>
      </div>
    </div>
    <div class="pt-3">
      <label class="badge badge-secondary font-weight-bolder">
        A板
      </label>
    </div>
    <div class="row pt-3 pb-7 border-bottom">
      <div class="col-12" v-if="produceFile.aFileUrl != null && produceFile.aFileUrl != ''">
        <img :src="produceFile.aFileUrl" class="w-100">
      </div>
      <div class="col-12 text-danger font-weight-bolder" v-else>
        <span class="pl-3">系统未上传A板资料</span>
      </div>
    </div>
    <div class="pt-3">
      <label class="badge badge-secondary font-weight-bolder">
        B板
      </label>
    </div>
    <div class="row pt-3 pb-7 border-bottom">
      <div class="col-12" v-if="produceFile.bFileUrl != null && produceFile.bFileUrl != ''">
        <img :src="produceFile.bFileUrl" class="w-100">
      </div>
      <div class="col-12 text-danger font-weight-bolder" v-else>
        <span class="pl-3">系统未上传B板资料</span>
      </div>
    </div>
    <div class="pt-3">
      <label class="badge badge-secondary font-weight-bolder">
        附件
      </label>
    </div>
    <div class="row pt-3 pb-7 border-bottom">
      <div class="col-12" v-if="produceFile.fileList != null && produceFile.fileList.length > 0">
        <div class="row">
          <template v-for="(item, index) in produceFile.fileList">
            <div class="col-4" :key="index">
              <button class="btn btn-outline-primary" v-on:click="getFile(item)">附件{{index + 1}}</button>
            </div>
          </template>
        </div>
      </div>
      <div class="col-12 text-danger font-weight-bolder" v-else>
        <span class="pl-3">系统未上传附件</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
name: "information",
  mounted() {
    this.cardId = this.$route.query.cardId;
    this.craftNo = this.$route.query.craftNo;
    if (!this.cardId){
      alert("无工程卡信息，即将自动跳转主页");
      this.$router.push('/scanWork');
      return;
    }
    this.setFileUrl();
  },
  data(){
    return{
      produceFile: {},
      cardId: "",
      craftNo: ""
    }
},
  methods:{
    setFileUrl () {
      this.produceFile = {};
      const query = {};
      query.recordId = this.cardId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost('f/wechat/produce/getFileUrl', query).then((result) => {
        if (result && result.data) {
          this.produceFile = result.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)})
    },
    getFile(fileUrl) {
      window.open(fileUrl, '_blank');
    }
  }
}
</script>
