<template>
<!--材料最新价格、周期审批-->
  <div>
    <div>
      <div v-if="showAuditData && showAuditData.length > 0">
        <template v-for="(material,index) in showAuditData">
          <div class="pt-1" :key="index">
            <div class="d-flex justify-content-between">
              <div>编号：{{ material.no }}</div>
              <div>名称：{{ material.name }}</div>
            </div>
            <div class="d-flex text-left" style="word-break: break-all;">
              <div>规格：{{ material.specification }}</div>
            </div>
            <div class="d-flex justify-content-between pt-2">
              <div class="p-1 text-danger">
                <div class="font-weight-bolder">当前信息</div>
                <div class="pt-1">价格：{{ material.initPrice ?  material.initPrice : 0}}</div>
                <div class="pt-1">提前期：{{ material.initLeadTime ?  material.initLeadTime : 0}}</div>
              </div>
              <div class="p-1 text-success">
                <div class="font-weight-bolder">修改信息</div>
                <div class="pt-1">价格：{{ material.erpRawmaterialPrice ? material.erpRawmaterialPrice : 0}}</div>
                <div class="pt-1">提前期：{{ material.erpRawmaterialLeadTime ? material.erpRawmaterialLeadTime : 0}}</div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
name: "materialPriceLatest",
  props: {
    showAuditData: [Object, Array],
  }
}
</script>
