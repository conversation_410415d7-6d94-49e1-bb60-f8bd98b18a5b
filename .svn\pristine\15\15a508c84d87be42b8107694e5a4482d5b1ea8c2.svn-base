<template>
  <div>
    <div class="bg-white rounded-sm p-3 mb-3 font-size-lg">
      <div>
        <span class="text-danger">*</span>任务标题
        <input class="form-control" v-model="title"/>
      </div>
      <div class="pt-3">
        <span class="text-danger">*</span>任务内容
        <textarea class="form-control" v-model="content"></textarea>
      </div>
      <div class="row pt-3">
        <div class="col-6">
          <span class="text-danger">*</span>负责人
          <div v-on:click="openFilter('1')">
            <label class="form-control">
              <template v-for="item in empList">
                <span :key="item.recordId" class="mr-1" v-if="isIdInString(item.recordId, '1')">{{item.name}}&nbsp;</span>
              </template>
            </label>
          </div>
        </div>
        <div class="col-6">
          <span class="text-danger">*</span>紧急程度
          <select class="form-control" v-model="emergencyLevel">
            <option value="1">低</option>
            <option value="2">中</option>
            <option value="3">高</option>
          </select>
        </div>
      </div>
      <div class="pt-3">
        参与人员
        <div class="pt-1" v-on:click="openFilter('2')">
          <label class="form-control">
            <template v-for="item in empList">
              <span :key="item.recordId" class="mr-1" v-if="isIdInString(item.recordId, '2')">{{item.name}}&nbsp;</span>
            </template>
          </label>
        </div>
      </div>
      <div class="pt-3">
        管理人员
        <div class="pt-1" v-on:click="openFilter('3')">
          <label class="form-control">
            <template v-for="item in empList">
              <span :key="item.recordId" class="mr-1" v-if="isIdInString(item.recordId, '3')">{{item.name}}</span>
            </template>
          </label>
        </div>
      </div>
      <div class="row pt-3">
        <div class="col-6">
          <span class="text-danger">*</span>截止日期
          <input id="completeTime" class="form-control">
        </div>
      </div>
    </div>
    <b-modal ref="filter" hide-footer hide-header>
      <div>
        <div class="pb-3 border-bottom">
          <div class="text-muted">负责人筛选</div>
          <input class="form-control form-control-sm" style="width: 10rem;" v-model="empFilter"/>
        </div>
        <div class="row pt-3" style="max-height: 30vh;overflow-y: auto;">
          <template v-for="item in empList">
            <div class="col-4" :key="item.recordId" v-if="item.name.indexOf(empFilter) !== -1">
              <button :class="['btn text-left', (isIdInString(item.recordId, empFlag) ? 'btn-primary' : 'btn-outline-secondary'), 'w-100 mb-2']" v-on:click="setEmpVal(item, empFlag)">{{item.name}}</button>
            </div>
          </template>
        </div>
        <div class="row pt-10">
          <div class="col-12 text-right">
            <button class="btn btn-outline-secondary" v-on:click="cancelFilter(empFlag)">清空</button>
            <button class="btn btn-outline-primary ml-3" v-on:click="applyFilter(empFlag)">应用</button>
          </div>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
import $ from "jquery";
export default {
  name: "addTask",
  props: {
    task: Object
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.employeeId){
        return;
      }
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return {
      userMsg: {},
      completeTime:"",
      empList:[],
      shareIds:"",
      participateIds:"",
      principalIds: "",
      empFlag: "",
      empFilter: "",
      title: "",
      content: "",
      emergencyLevel: ""

    }
  },
  methods:{
    loadData() {
      this.getEmpList();
    },
    getEmpList () {
      const query = {};
      query.departId = this.userMsg.departId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/getEmpList",query).then(result => {
        this.empList = result.data;
        if (this.task && this.task.recordId)
        {
          this.title = this.task.title;
          this.content = this.task.content;
          this.completeTime = this.task.completeTime;
          this.emergencyLevel = this.task.emergencyLevel;
          this.principalIds = this.task.principals.recordId; //负责人
          this.shareIds = this.task.shareIds;
          this.participateIds = this.task.participateIds;
          this.initQueryDate('completeTime', this.completeTime);
        }else{
          this.getDataT();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    getDataT: function (){
      let deadlineDate = new Date();
      deadlineDate.setMonth(deadlineDate.getMonth());
      let deadlineYear = deadlineDate.getFullYear();
      let deadlineMonth = deadlineDate.getMonth() + 1;
      deadlineMonth = (deadlineMonth < 10 ? '0' + deadlineMonth : deadlineMonth);
      let deadlineDay = deadlineDate.getDate() >= 10 ? deadlineDate.getDate() : '0' + deadlineDate.getDate();
      let completeTime = deadlineYear + '-' + deadlineMonth + '-' + deadlineDay;

      this.completeTime = completeTime;
      this.initQueryDate('completeTime', completeTime);
    },
    initQueryDate: function (id, startDates) {
      if ($('#' + id + '').is(':visible')) {
        const _this = this;
        $('#' + id + '').daterangepicker({
          'singleDatePicker': true,
          'showDropdowns': true,
          'timePicker': false,
          'timePicker24Hour': false,
          'startDate': startDates, // 设置开始日期
          'opens': 'center',
          'drops': 'down',
          'locale': {
            'format': 'YYYY-MM-DD',
            'separator': ' - ',
            'applyLabel': '确定',
            'cancelLabel': '取消',
            'fromLabel': 'From',
            'toLabel': '到',
            'customRangeLabel': 'Custom',
            'weekLabel': 'W',
            'daysOfWeek': [
              '日',
              '一',
              '二',
              '三',
              '四',
              '五',
              '六'
            ],
            'monthNames': [
              '一月',
              '二月',
              '三月',
              '四月',
              '五月',
              '六月',
              '七月',
              '八月',
              '九月',
              '十月',
              '十一月',
              '十二月'
            ],
            'firstDay': 1
          }
        }, function (start) {
          if (id === 'completeTime') {
            _this.completeTime = start.format('YYYY-MM-DD');
          }
        })
      } else {
        if (this.temp > 50) {
          this.temp = 0;
        }
        this.temp++;
        // 递归 等待dom渲染完毕
        const _this = this;
        setTimeout(function () { _this.initQueryDate(id, startDates); }, 500)
      }
    },
    openFilter(flag) {
      this.empFlag = flag;
      this.$refs['filter'].show();
    },
    cancelFilter(flag) {
      if (flag == '1') {
        this.principalIds = "";
      }else if (flag == '2') {
        this.shareIds = "";
      }else if (flag == '3') {
        this.participateIds = "";
      }
      this.$refs['filter'].hide();
    },
    applyFilter() {
      this.$refs['filter'].hide();
    },
    setEmpVal(item, flag) {
      if (!flag) {
        return false;
      }
      if (flag == '1') {
        this.principalIds = item.recordId;
      }else {
        this.clickEmp(item.recordId, flag);
      }
    },
    isIdInString(targetId, flag) {
      if (!flag) {
        return false;
      }
      let idString = "";
      if (flag == '1') {
        idString = this.principalIds;
      }else if (flag == '2') {
        idString = this.participateIds;
      }else if (flag == '3') {
        idString = this.shareIds;
      }
      if (!idString) {
        return false;
      }
      const idArray = idString.split(',').map(id => id.trim());
      return idArray.includes(targetId);
    },
    clickEmp(id, flag) {
      if (!flag) {
        return "";
      }
      let idString = "";
      if (flag == '2') {
        idString = this.participateIds;
      }else if (flag == '3') {
        idString = this.shareIds;
      }
      let resIds = "";
      if (this.isIdInString(id, flag)){
        const parts = idString.split(',');
        for (const part of parts) {
          if (part.trim() !== id) {
            resIds = resIds ? resIds + "," + part.trim() : part.trim();
          }
        }
      }else {
        resIds = idString ? idString + "," + id : id;
      }
      if (flag == '2') {
        this.participateIds = resIds;
      }else if (flag == '3') {
        this.shareIds = resIds;
      }
    }
  }
}
</script>
