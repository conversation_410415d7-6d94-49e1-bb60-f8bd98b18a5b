<template>
<div>
  <template v-for="item in showAuditData">
    <div class="pt-1 pb-1 alert-secondary" :key="item.recordId">
      <div class="text-truncate font-weight-bolder">生产编号：{{item.craftNo}}</div>
      <div class="text-truncate" style="word-break: break-all">客户型号：{{item.customerModel}}</div>
      <div style="word-break: break-all;">
        {{item.referenceType}}
        <span v-if="item.boardLevel" class="pl-1">{{item.boardLevel}}</span>
        <span v-if="item.materialType" class="pl-1">{{item.materialType}}</span>
        <span v-if="item.boardThickness" class="pl-1">{{item.boardThickness}}</span>
        <span v-if="item.copperCladThickness" class="pl-1">{{item.copperCladThickness}}</span>
        <span v-if="item.surfaceProcess" class="pl-1">{{item.surfaceProcess}}</span>
        <span v-if="item.solderMaskType" class="pl-1">{{item.solderMaskType}}</span>
        <span v-if="item.characterType" class="pl-1">{{item.characterType}}</span>
        <span v-if="item.shapingWay" class="pl-1">{{item.shapingWay}}</span>
        <span v-if="item.testMethod" class="pl-1">{{item.testMethod}}</span>
        <span v-if="item.smallAperture" class="pl-1">{{item.smallAperture}}</span>
        <span v-if="item.buryBlindHole" class="pl-1">{{item.buryBlindHole}}</span>
        <span v-if="item.deliveryUrgent" class="pl-1">{{item.deliveryUrgent}}</span>
        <span v-if="item.daore" class="pl-1">{{item.daore}}</span>
        <span v-if="item.naiya" class="pl-1">{{item.naiya}}</span>
        <span v-if="item.lingeSpacing" class="pl-1">{{item.lingeSpacing}}</span>
        <span v-if="item.halAhole" class="pl-1">{{item.halAhole}}</span>
        <span v-if="item.resistance" class="pl-1">{{item.resistance}}</span>
        <span v-if="item.pliesnumber" class="pl-1">{{item.pliesnumber}}</span>
        <span v-if="item.specialCraftVal" class="pl-1">{{item.specialCraftVal}}</span>
        <span v-if="item.processValue" class="pl-1">{{item.processValue == '1' ? "曝光" : "丝印"}}</span>
        <span v-if="item.packingRequirement" class="pl-1">{{item.packingRequirement}}</span>
        <span v-if="item.throughHole" class="pl-1">通孔：{{item.throughHole}}</span>
        <span v-if="item.countersinkHole" class="pl-1">沉头孔：{{item.countersinkHole}}</span>
      </div>
      <div v-if="item.useMaterialNo" style="word-break: break-all;">使用板材：{{item.useMaterialNo}}&nbsp;{{item.useMaterialName}}{{item.useMaterialSpec}}</div>
      <div class="d-flex justify-content-between">
        <span class="font-weight-bolder text-primary">面积：{{item.deailArea}}</span>
        <span class="font-weight-bolder text-primary" v-if="isShowFeeFlag">平米单价：{{(item.price * item.quantity / item.deailArea).toFixed(2)}}</span>
      </div>
      <div v-if="isShowFeeFlag">
        <div>
          <span>数量：{{item.quantity}}</span>
          <span class="pl-2">单价：{{item.price}}</span>
          <span class="pl-2">金额：{{item.totalAmt}}</span>
          <span class="pl-2">工程费：{{item.engineeringFee ? item.engineeringFee : 0}}</span>
          <span class="pl-2">样本费：{{item.sampleFee ? item.sampleFee : 0}}</span>
          <span class="pl-2">测试架费：{{item.testShelfFee ? item.testShelfFee : 0}}</span>
          <span class="pl-2">模具费：{{item.mouldFee ? item.mouldFee : 0}}</span>
          <span class="pl-2">其他费：{{item.othersFee ? item.othersFee : 0}}</span>
        </div>
        <div class="d-flex justify-content-between">
          <div>PCS：{{item.unitLength}} * {{item.unitWidth}}</div>
          <div>PNL：{{item.pnlLength}} * {{item.pnlWidth}} / {{item.pnlDivisor}}</div>
        </div>
        <div class="d-flex justify-content-between">
          <div>总部经济单价：{{item.lnPrice.toFixed(4)}}</div>
          <div>工厂单价：{{item.jxPrice.toFixed(4)}}</div>
        </div>
        <div class="d-flex justify-content-between text-primary">
          <div>加工成本：{{item.processFee}}(￥{{(item.processFee / item.deailArea).toFixed(2)}})</div>
          <div>材料成本：{{item.materialFee}}(￥{{(item.materialFee / item.deailArea).toFixed(2)}})</div>
        </div>
        <div class="d-flex justify-content-between text-primary">
          <div>营运费：{{item.saleFee + item.manageFee}}</div>
          <div>总成本：{{item.netCostFee}}</div>
        </div>
        <div class="text-truncate font-weight-bolder text-primary">{{item.productGrade}}</div>
        <div class="d-flex text-primary justify-content-between font-weight-bolder">
          <div>客户等级：{{item.customerLevel}}</div>
          <div v-if="item.productProfitMargin">要求利润：{{item.productProfitMargin}}%</div>
          <div :class="item.netSalesProfit && item.netSalesProfit > 0 ? 'text-success' : 'text-danger'">利润率：{{item.netProfitRate}}%</div>
        </div>
      </div>
      <div class="d-flex justify-content-between">
        <div v-if="item.estimateStartDate">开始评估日期：{{item.estimateStartDate.split(" ")[0]}}</div>
        <div class="text-primary font-weight-bolder">
          交货日期：
          <span v-if="item.estimateDate">{{item.estimateDate.split(" ")[0]}}</span>
          <span v-else>{{item.deliveryDate}}</span>
        </div>
      </div>
      <div class="text-muted" v-if="item.estimateMethod">{{item.estimateMethod}}</div>
      <div class="font-weight-bolder">生产商：{{item.madeSupplierName}}</div>
      <div>备注：{{item.remark}}</div>
      <div>生产：{{item.prdDetailRemark}}</div>
    </div>
  </template>
</div>
</template>

<script>
export default {
name: "orderDetailUtil",
  props: {
    showAuditData: [Object, Array],
    isShowFeeFlag: Boolean,
  },
}
</script>