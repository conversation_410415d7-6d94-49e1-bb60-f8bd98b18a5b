<template>
  <!--原料采购单反审-->
  <div>
    <div v-if="showAuditData && showAuditData.length > 0 && showAuditData[0].contract">
      <div class="pt-2">
        <div class="d-flex align-items-center pb-3">
          <div class="font-weight-bolder">订单</div>
        </div>
        <div>
          <div>需方：{{showAuditData[0].contract.customerName}}</div>
          <div>供方：{{showAuditData[0].contract.supplierName}}</div>
          <div class="d-flex">
            <div>编号：{{showAuditData[0].contract.no}}</div>
          </div>
          <div class="d-flex">
            <div>下单日期：{{showAuditData[0].contract.orderDate}}</div>
          </div>
          <div class="d-flex">
            <div>采购员：{{showAuditData[0].contract.purchaserName}}</div>
          </div>
          <div style="word-break: break-all;">
            <div>送货地址：{{showAuditData[0].contract.deliveryPlace}}</div>
          </div>
          <div class="d-flex">
            客户要求：
            <div>{{showAuditData[0].contract.currencyType}}</div>
            <div class="pl-2">{{showAuditData[0].contract.freightWay}}</div>
            <div class="pl-2">{{showAuditData[0].contract.deliveryWay}}</div>
            <div class="pl-2">{{showAuditData[0].contract.taxDescript}}</div>
            <div class="pl-2">{{showAuditData[0].contract.payWay}}</div>
            <div class="pl-2" v-if="showAuditData[0].contract.assureDays != null && showAuditData[0].contract.assureDays !=''">
              保质期:{{showAuditData[0].contract.assureDays}}
            </div>
          </div>
        </div>
      </div>
      <div class="pt-2">
        <div class="d-flex justify-content-between align-items-center pb-1 border-bottom">
          <div class="font-weight-bolder">订单明细</div>
          <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlag">{{ flag && flag == '1' ? '收起' : '展开' }}</button>
        </div>
        <div v-if="flag == '1'">
          <template v-for="item in showAuditData">
            <div class="pt-1 pb-1 border-bottom alert-secondary" :key="item.recordId">
              <div class="d-flex justify-content-between">
                <div>物料编号：{{item.materialNo}}</div>
                <div>物料名称：{{item.materialName}}</div>
              </div>
              <div class="d-flex">
                <span>单位：{{item.unit}}</span>
                <span class="pl-2">数量：{{item.quantity}}</span>
                <span class="pl-2">单价：{{item.price}}</span>
                <span class="pl-2">总金额：{{item.amount}}</span>
              </div>
              <div class="d-flex justify-content-between">
                <span>材料：{{item.materialName}}</span>
                <span class="pl-2" v-if="item.supplierModel != null && item.supplierModel != ''">供应商型号：{{item.supplierModel}}</span>
              </div>
              <div style="word-break: break-all" v-if="item.craftDescript != null && item.craftDescript != ''">规格要求：{{item.craftDescript}}</div>
              <div v-if="item.deliverysList" class="pt-1">
                <span class="d-flex text-dark">分批交货</span>
                <div class="pt-2 border-top border-bottom alert-secondary" v-for="itemTwo in item.deliverysList" :key="itemTwo.recordId">
                  <div class="d-flex justify-content-between">
                    <span v-if="itemTwo.deliveryDateStr">
                      交货时间:{{itemTwo.deliveryDateStr}}
                    </span>
                    <span v-if="itemTwo.quantity">
                      交货数量:{{itemTwo.quantity}}
                    </span>
                  </div>
                </div>
              </div>
              <div class="d-flex pt-1">
                <div>采购备注:{{item.purchasingRemarks}}</div>
              </div>
              <div class="d-flex pt-1">
                <a href="javascript:void(0)" v-if="item.purchRaw != null && item.purchRaw.purchApplyId != ''" v-on:click="purchRawDate(item.purchRaw)">
                  原料申请单编号:{{item.purchRaw.purchApplyId}}
                </a>
              </div>
              <div>
                <div class="d-flex justify-content-between align-items-center pb-1 border-bottom">
                  <div class="text-dark" > {{item.purchRaw.purchaseWayStr}}列表</div>
                  <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlagTwo(item)">
                    <span v-if="!item.flag">展开</span>
                    <span v-else>收起</span>
                  </button>
                </div>
                <div v-if="item.flag && item.materialList && item.materialList.length > 0">
                  <div v-if="item.purchRaw.purchaseWay == 1">
                    <div class="d-flex justify-content-between">
                      <div>编号：{{item.materialList[0].no}}</div>
                      <div>标题：{{item.materialList[0].title}}</div>
                    </div>
                    <div class="d-flex justify-content-between">
                      <div>招标目标：{{item.materialList[0].materialName}}</div>
                      <div>招标数量：{{item.materialList[0].materialNum}}</div>
                    </div>
                    <div class="d-flex justify-content-between">
                      <div>备注说明：{{item.materialList[0].content}}</div>
                      <div>招标时间：{{item.materialList[0].startTimeStr}}-{{item.materialList[0].endTimeStr}}</div>
                    </div>
                    <div class="d-flex justify-content-between">
                      <div>招标地点：{{item.materialList[0].address}}</div>
                      <div>原因：{{item.materialList[0].remark}}</div>
                    </div>
                    <div class="pt-1">
                      <template v-for="row in item.materialList">
                        <div :key="row.recordId" v-if="row.price && row.price > 0">
                          <div class="d-flex justify-content-between">
                            <div>询价方：{{item.materialList[0].address}}</div>
                            <div>
                               <span class="badge badge-danger" v-if="!row.price">未报价</span>
                               <span v-else><span class="badge badge-success" v-if="(row.biddingStatus == '3' && (row.status == '2' || row.status == '4')) || row.status == '60001'">已中标</span>
                               <span class="badge badge-warning" v-else>未中标</span></span>
                            </div>
                          </div>
                          <div class="d-flex">
                            <div>
                              <span>等级：<span class="font-weight-bold">{{row.erpSupplier ? row.erpSupplier.supplierLevelValue : null}}</span>&nbsp;</span>
                              <span>行业：<span class="font-weight-bold">{{row.erpSupplier ? row.erpSupplier.industry : null}}</span>&nbsp;</span>
                              <span>产品：<span class="font-weight-bold">{{row.erpSupplier ? row.erpSupplier.supplierTypeValue : null}}</span>&nbsp;</span>
                              <span>分类：<span class="font-weight-bold">{{item.erpSupplier ? item.erpSupplier.classificationValue : null}}</span>&nbsp;</span>
                            </div>
                          </div>
                          <div class="d-flex">
                            <div>
                              <span v-if="item.price && item.price > 0">单价：<span class="font-weight-bold">{{row.price}}</span>&nbsp;</span>
                              <span v-if="item.price && item.price > 0">运费：<span class="font-weight-bold">{{row.freight}}</span>&nbsp;</span>
                              <span v-for="del in row.list" :key="del.recordId">交期：<span class="font-weight-bold">{{del.deliveryDayVal}}</span>&nbsp;数量：<span class="font-weight-bold">{{del.quantity}}</span>&nbsp;</span>
                            </div>
                          </div>
                          <div class="d-flex">
                            <div>
                              备注：{{item.remark}}&nbsp;
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                  <div v-if="item.purchRaw.purchaseWay == 2 || item.purchRaw.purchaseWay == 3">
                    <div v-for="row in item.materialList" :key="row.recordId">
                      <div class="d-flex justify-content-between">
                        <div>品牌：{{row.manufacturer}}</div>
                        <div>
                          <span v-if="row.price != null && row.price != ''">
                          价格：
                          <span v-if="row.useFlag == 1 || row.updatePriceDateFlag == 1" class="font-weight-bold">
                            {{row.price}}
                          </span>
                          <span class="text-muted" v-else>
                            {{row.price}}(过期)
                          </span>
                        </span>
                        </div>
                      </div>
                      <div class="d-flex justify-content-between">
                        <div>
                           <span v-if="row.areaPrice != null && row.areaPrice != ''">
                              平米单价：
                              <span v-if="row.useFlag == 1 || row.updatePriceDateFlag == 1" class="font-weight-bold">
                                {{row.areaPrice}}
                              </span>
                              <span v-else>
                                {{row.areaPrice}}
                              </span>
                            </span>
                        </div>
                        <div>
                           <span>
                             <span class="badge badge-success" v-if="row.useFlag == '1'">使用中</span>
                             <span class="badge badge-warning" v-if="row.useFlag !== '1'">待使用</span>
                           </span>
                        </div>
                      </div>
                      <div class="d-flex">
                        <div>维护时间：{{row.updatePriceDate}}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
<!--              <div>-->
<!--                <div class="row pt-2 align-items-center" v-if="detailFold && audit.flag == 5 && item.purchRaw && item.purchRaw.purchaseWayStr">-->
<!--                  <div class="col">-->
<!--                    {{item.purchRaw.purchaseWayStr}}列表-->
<!--                  </div>-->
<!--                  <div class="col text-right">-->
<!--                    <button class="btn btn-sm btn-primary" v-on:click="clickPurchRaw(item)">-->
<!--                      <span v-if="!item.purchRawFold">展开</span>-->
<!--                      <span v-else>收起</span>-->
<!--                    </button>-->
<!--                  </div>-->
<!--                </div>-->

<!--                <div v-if="item.purchRawFold && item.showDataList && item.showDataList.length > 0">-->
<!--                  <div v-if="purchaseWay == 1">-->
<!--                    <div class="row alert-light align-items-center">-->
<!--                      <div class="col-6">-->
<!--                        <span v-if="item.showDataList[0].no != null && item.showDataList[0].no != ''">-->
<!--                          编号：<span class="font-weight-bold">{{item.showDataList[0].no}}</span>&nbsp;-->
<!--                        </span>-->
<!--                      </div>-->
<!--                      <div class="col-6">-->
<!--                          <span v-if="item.showDataList[0].title != null && item.showDataList[0].title != ''">-->
<!--                            标题：<span class="font-weight-bold">{{item.showDataList[0].title}}</span>&nbsp;-->
<!--                          </span>-->
<!--                      </div>-->
<!--                    </div>-->
<!--                    <div class="row alert-light align-items-center">-->
<!--                      <div class="col-6">-->
<!--                          <span v-if="item.showDataList[0].materialName != null && item.showDataList[0].materialName != ''">-->
<!--                            招标目标：<span class="font-weight-bold">{{item.showDataList[0].materialName}}</span>&nbsp;-->
<!--                          </span>-->
<!--                      </div>-->
<!--                      <div class="col-6">-->
<!--                          <span v-if="item.showDataList[0].materialNum != null && item.showDataList[0].materialNum != ''">-->
<!--                            招标数量：<span class="font-weight-bold">{{item.showDataList[0].materialNum}}</span>&nbsp;-->
<!--                          </span>-->
<!--                      </div>-->
<!--                    </div>-->
<!--                    <div class="row alert-light align-items-center">-->
<!--                      <div class="col-6">-->
<!--                          <span v-if="item.showDataList[0].content != null && item.showDataList[0].content != ''">-->
<!--                            备注说明：<span class="font-weight-bold">{{item.showDataList[0].content}}</span>&nbsp;-->
<!--                          </span>-->
<!--                      </div>-->
<!--                      <div class="col-6">-->
<!--                          <span v-if="item.showDataList[0].startTimeStr != null && item.showDataList[0].startTimeStr != '' && item.showDataList[0].endTimeStr != null && item.showDataList[0].endTimeStr != ''">-->
<!--                            招标时间：<span class="font-weight-bold">{{item.showDataList[0].startTimeStr}}-{{item.showDataList[0].endTimeStr}}</span>&nbsp;-->
<!--                          </span>-->
<!--                      </div>-->
<!--                    </div>-->
<!--                    <div class="row alert-light align-items-center">-->
<!--                      <div class="col-6">-->
<!--                          <span v-if="item.showDataList[0].address != null && item.showDataList[0].address != ''">-->
<!--                            招标地点：<span class="font-weight-bold">{{item.showDataList[0].address}}</span>&nbsp;-->
<!--                          </span>-->
<!--                      </div>-->
<!--                      <div class="col-6">-->
<!--                          <span v-if="item.showDataList[0].remark != null && item.showDataList[0].remark != ''">-->
<!--                            原因：<span class="font-weight-bold">{{item.showDataList[0].remark}}</span>&nbsp;-->
<!--                          </span>-->
<!--                      </div>-->
<!--                    </div>-->
<!--                    <div  class="row pt-1 align-items-center border-bottom pt-2 pb-2 border-dark" v-for="row in item.showDataList" :key="row.recordId" v-if="row.price && row.price > 0">-->
<!--                      <div class="col-10" >-->
<!--                          <span>-->
<!--                            询价方：<span class="font-weight-bold">{{row.supplierShortName}}</span>&nbsp;-->
<!--                          </span>-->
<!--                      </div>-->
<!--                      <div class="col-2 text-right">-->
<!--                          <span class="badge badge-danger" v-if="!row.price">-->
<!--                            未报价-->
<!--                          </span>-->
<!--                        <span v-else>-->
<!--                            <span class="badge badge-success" v-if="(row.biddingStatus == '3' && (row.status == '2' || row.status == '4')) || row.status == '60001'">-->
<!--                              已中标-->
<!--                            </span>-->
<!--                            <span class="badge badge-warning" v-else>-->
<!--                              未中标-->
<!--                            </span>-->
<!--				                </span>-->
<!--                      </div>-->
<!--                      <div class="col-12">-->
<!--                          <span>-->
<!--                            等级：<span class="font-weight-bold">{{row.erpSupplier ? row.erpSupplier.supplierLevelValue : null}}</span>&nbsp;-->
<!--                          </span>-->
<!--                                          <span>-->
<!--                            行业：<span class="font-weight-bold">{{row.erpSupplier ? row.erpSupplier.industry : null}}</span>&nbsp;-->
<!--                          </span>-->
<!--                                          <span>-->
<!--                            产品：<span class="font-weight-bold">{{row.erpSupplier ? row.erpSupplier.supplierTypeValue : null}}</span>&nbsp;-->
<!--                          </span>-->
<!--                                          <span>-->
<!--                            分类：<span class="font-weight-bold">{{item.erpSupplier ? item.erpSupplier.classificationValue : null}}</span>&nbsp;-->
<!--                          </span>-->
<!--                      </div>-->
<!--                      <div class="col-12">-->
<!--                        <span v-if="item.price && item.price > 0">-->
<!--                          单价：<span class="font-weight-bold">{{row.price}}</span>&nbsp;-->
<!--                        </span>-->
<!--                        <span v-if="item.price && item.price > 0">-->
<!--					              运费：<span class="font-weight-bold">{{row.freight}}</span>&nbsp;-->
<!--				                </span>-->
<!--                        <span v-for="del in row.list" :key="del.recordId">-->
<!--                          交期：<span class="font-weight-bold">{{del.deliveryDayVal}}</span>&nbsp;-->
<!--                          数量：<span class="font-weight-bold">{{del.quantity}}</span>&nbsp;-->
<!--				                </span>-->
<!--                      </div>-->
<!--                      <div class="col-12" v-if="row.remark">-->
<!--                          <span>-->
<!--                            备注：<span class="font-weight-bold">{{item.remark}}</span>&nbsp;-->
<!--                          </span>-->
<!--                      </div>-->
<!--                    </div>-->
<!--                  </div>-->
<!--                  <div v-if="purchaseWay == 2 || purchaseWay == 3">-->
<!--                    <div  class="pt-1 align-items-center border-bottom pt-2 pb-2 border-dark" v-for="row in item.showDataList" :key="row.recordId">-->
<!--                      <div class="row alert-light align-items-center">-->
<!--                        <div class="col-6">-->
<!--                          <span v-if="row.manufacturer != null && row.manufacturer != ''">-->
<!--                            品牌：<span class="font-weight-bold">{{row.manufacturer}}</span>&nbsp;-->
<!--                          </span>-->
<!--                        </div>-->
<!--                        <div class="col-6">-->
<!--                          <span v-if="row.price != null && row.price != ''">-->
<!--                          价格：-->
<!--                          <span v-if="row.useFlag == 1 || row.updatePriceDateFlag == 1" class="font-weight-bold">-->
<!--                            {{row.price}}-->
<!--                          </span>-->
<!--                          <span class="text-muted" v-else>-->
<!--                            {{row.price}}(过期)-->
<!--                          </span>-->
<!--                        </span>-->
<!--                        </div>-->
<!--                      </div>-->
<!--                      <div class="row alert-light align-items-center">-->
<!--                        <div class="col-6">-->
<!--                            <span v-if="row.areaPrice != null && row.areaPrice != ''">-->
<!--                              平米单价：-->
<!--                              <span v-if="row.useFlag == 1 || row.updatePriceDateFlag == 1" class="font-weight-bold">-->
<!--                                {{row.areaPrice}}-->
<!--                              </span>-->
<!--                              <span v-else>-->
<!--                                {{row.areaPrice}}-->
<!--                              </span>-->
<!--                            </span>-->
<!--                        </div>-->
<!--                        <div class="col-6">-->
<!--                          <span>-->
<!--                            <span class="badge badge-success" v-if="row.useFlag == '1'">-->
<!--                              使用中-->
<!--                            </span>-->
<!--                            <span class="badge badge-warning" v-if="row.useFlag !== '1'">-->
<!--                              待使用-->
<!--                            </span>-->
<!--                          </span>-->
<!--                        </div>-->
<!--                      </div>-->
<!--                      <div class="row alert-light align-items-center">-->
<!--                        <div class="col-6">-->
<!--                          <span v-if="row.updatePriceDate != null && row.updatePriceDate != ''">-->
<!--                            维护时间：-->
<!--                            <span>-->
<!--                              {{row.updatePriceDate}}-->
<!--                            </span>-->
<!--                          </span>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->




            </div>
          </template>
        </div>
      </div>
    </div>
    <b-modal :title="'原料申购单详情'" ref="purchRawReverseData" hide-footer>
      <div class="row alert-light align-items-center">
        <span class="col">
          原料申购单号: {{ purchRawReverseShow.purchApplyId }}
        </span>
        <span class="col">
          申请状态: {{ purchRawReverseShow.typeStr }}
        </span>
      </div>
      <div class="row alert-light align-items-center">
        <span class="col">
          采购方式: {{ purchRawReverseShow.purchaseWayStr }}
        </span>
        <span class="col">
          申购数量: {{ purchRawReverseShow.quantity }}
        </span>
      </div>
      <div class="row alert-light align-items-center">
        <span class="col">
          申购人: {{ purchRawReverseShow.filingApplicant }}
        </span>
        <span class="col">
          申购部门: {{ purchRawReverseShow.reportingDept }}
        </span>
      </div>
    </b-modal>
  </div>
</template>

<script>
export default {
name: "rawMaterialReverse",
  props: {
    showAuditData: [Object, Array],
  },
  data() {
    return{
      flag: "2",
      purchRawReverseShow: "",
    }
  },
  methods: {
    changeFlag() {
      if (this.flag == "1"){
        this.flag = "2";
      }else {
        this.flag = "1";
      }
    },
    changeFlagTwo(item){
      this.$set(item,'flag',!item.flag)
    },
    purchRawDate(item)
    {
      if(!item)
      {
        return;
      }
      this.purchRawReverseShow = item;
      this.$refs['purchRawReverseData'].show();
    }
  }
}
</script>