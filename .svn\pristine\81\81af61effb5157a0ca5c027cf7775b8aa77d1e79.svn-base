<template>
  <div class="wx-flash-sale-card" @click="router_page">
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="shine-effect"></div>
      <div class="header-content">
        <div class="title-wrapper">
          <div class="flame-icon"></div>
          <span>{{ title }}</span>
        </div>
        <span class="badge">{{ tag }}</span>
      </div>
    </div>

    <!-- 卡片主体 -->
    <div class="card-body">
      <h4 class="product-name">{{ productName }}</h4>

      <div class="price-area">
        <span class="current-price">已购</span>
        <span class="original-price">100块</span>
      </div>

      <div class="info-row">
        <span class="stock-info">需求{{ remainingStock }}块</span>
        <span class="time-limit">
          <i class="fa fa-clock-o mr-0.5"></i> {{ timeLimitText }}
        </span>
      </div>

      <div class="stock-bar">
        <div class="stock-progress" :style="{ width: progress + '%' }"></div>
      </div>

      <button class="buy-button" @click.stop="handleBuy">
        <i class="fa fa-calculator text-success mr-1"></i>{{ buyButtonText }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WxFlashSaleCard',
  props: {
    title: { type: String, default: '今日钜惠' },
    tag: { type: String, default: '限量' },
    productName: { type: String, required: true },
    originalPrice: { type: Number, required: true },
    discountedPrice: { type: Number, required: true },
    remainingStock: { type: Number, required: true },
    totalStock: { type: Number, default: 100 },
    timeLimitText: { type: String, default: '限时' },
    buyButtonText: { type: String, default: '立即抢购' },
  },
  computed: {
    progress() {
      return Math.round((this.remainingStock / this.totalStock) * 100);
    }
  },
  methods: {
    router_page() { this.$emit('click'); },
    handleBuy(event) {
      this.$emit('buy');
      event.stopPropagation();
    }
  }
}
</script>

<style scoped>
/* 移除:root变量，直接使用颜色值 */
.wx-flash-sale-card {
  position: relative;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.wx-flash-sale-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* 卡片头部 */
.card-header {
  position: relative;
  padding: 5px 6px;
  background: linear-gradient(135deg, #4c6039, #137c3d);
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-wrapper {
  display: flex;
  align-items: center;
}

.badge {
  font-size: 10px;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 999px;
}

/* 闪光效果 */
.shine-effect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.3), rgba(255,255,255,0));
  transform: skewX(-20deg);
  animation: shine 3s infinite linear;
}

/* 卡片主体 */
.card-body {
  padding: 6px;
}

.product-name {
  font-weight: bold;
  color: #333;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.price-area {
  display: flex;
  align-items: baseline;
  margin-top: 3px;
}

.current-price {
  font-size: 18px;
  font-weight: bold;
  color: #FF4D4F;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
  margin-left: 6px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 3px;
}

.stock-info {
  font-size: 12px;
  color: #666;
}

.time-limit {
  font-size: 12px;
  color: #FF4D4F;
  background: rgba(255, 77, 79, 0.1);
  padding: 1px 6px;
  border-radius: 4px;
}

.stock-bar {
  height: 6px;
  background: #f1f1f1;
  border-radius: 3px;
  margin-top: 8px;
  overflow: hidden;
}

.stock-progress {
  height: 100%;
  background: linear-gradient(90deg, #477220, #218a4b);
  border-radius: 3px;
  transition: width 0.5s ease;
}

.buy-button {
  display: block;
  width: 100%;
  margin-top: 10px;
  padding: 6px 0;
  background: linear-gradient(135deg, #586583, #134a5b);
  color: white;
  text-align: center;
  font-weight: bold;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(22, 93, 255, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.buy-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(22, 93, 255, 0.4);
}

/* 火焰装饰 */
.flame-icon {
  position: relative;
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.flame-icon::before, .flame-icon::after {
  content: '';
  position: absolute;
  background: linear-gradient(to top, #3e6c20, #127246);
  border-radius: 50% 50% 0 50%;
  transform: rotate(45deg);
  animation: flame 1.5s infinite;
}

.flame-icon::before {
  width: 100%;
  height: 100%;
  opacity: 0.8;
}

.flame-icon::after {
  width: 70%;
  height: 70%;
  top: 30%;
  left: 30%;
  opacity: 0.6;
  animation-delay: -0.5s;
}

/* 动画定义 */
@keyframes flame {
  0%, 100% { transform: scaleY(1) rotate(0deg); }
  25% { transform: scaleY(1.1) rotate(-2deg); }
  50% { transform: scaleY(0.9) rotate(2deg); }
  75% { transform: scaleY(1.05) rotate(-1deg); }
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}
</style>
