<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">物流系统</h3>
    </div>
    <div class="bg-white rounded-sm p-3 mb-3">
      <div>
        业务公司
        <select class="form-control form-control-sm" v-model="company" v-on:change="loadData">
          <template v-for="item in erpList">
            <option :key="item.recordId" :value="item" v-if="item.recordId">
              {{item.name}}
            </option>
          </template>
        </select>
      </div>
      <div class="pt-2">
        搜索
        <input class="form-control form-control-sm" v-model="name" v-on:change="loadData">
      </div>
    </div>
    <div class="d-flex justify-content-between align-items-center bg-white rounded-sm p-3 mb-3">
      <div>操作人：{{userMsg.empName}}</div>
      <div>处理张数：{{scanNum}}</div>
      <div>待处理：<span v-if="deliveryList">{{deliveryList.length}}</span></div>
    </div>
    <template v-for="item in deliveryList">
      <div class="p-3 bg-white rounded-sm mb-2" :key="item.recordId">
        <div class="d-flex justify-content-between align-items-center">
          <div style="width: 20rem;" class="text-truncate text-primary font-weight-bolder">送货单号：{{item.no}}</div>
          <button class="btn btn-sm btn-outline-primary px-1 py-1" v-on:click="scanDelivery(item)">扫卡</button>
        </div>
        <div style="word-break: break-all">{{item.custNo}}&nbsp;{{item.saleName}}&nbsp;{{item.linkMan}}&nbsp;{{item.linkPhone}}</div>
        <div style="word-break: break-all">{{item.address}}</div>
      </div>
    </template>
    <b-modal ref="static" hide-footer title="物流信息绑定" no-close-on-backdrop>
      <p>送货单号：{{messageNo}}</p>
      <p>快递公司：{{messageCom}}</p>
      <p>快递单号：{{messageNumber}}</p>
    </b-modal>
    <div style="height: 100px;"></div>
  </div>
</template>

<script>
import wx from 'weixin-js-sdk';
import { getWxMsg } from '@/assets/js/utils/wechatScanUtils';
import { getErpList, getCompany } from '@/assets/js/utils/userAuth';
export default {
  name: "logistics",
  data(){
    return{
      company: {},
      deliveryList: [],
      messageNo: "",
      messageCom: "",
      messageNumber: "",
      scanNum: 0,
      name: "",
      erpList: [],
      userMsg: {}
    }
  },
  created:function(){
    if (process.env.NODE_ENV === "production"){
      getWxMsg(window.location.href.split('#')[0], wx);
    }
  },
  mounted() {
    this.userMsg = {};
    this.erpList = [];
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.erpList = getErpList(sysUser);
      this.company = getCompany(sysUser);
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  methods:{
    loadData:function()
    {
      if (!this.company || !this.company.recordId){
        alert("无公司信息");
        return;
      }
      this.scanNum = 0;
      window.localStorage.setItem('company', JSON.stringify(this.company));
      const com = {};
      const comId = eval('(' + this.company.recordId + ')');
      com.recordId = comId;
      com.erpUserId = this.company.userId
      com.name = this.name
      // 加载绑定物料
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/produce/getDeliveryList", com).then(result => {
        if(result.data){
          this.deliveryList = result.data.list;
          this.scanNum = result.data.scanNum == null ? 0:result.data.scanNum;
          this.$parent.enableLoadFlag(false);
        }else {
          alert("获取信息失败");
        }
      }).catch(err => {console.log(err);});
    },
    scanDelivery: function(item) {
      wx.scanQRCode({
        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
        scanType: ['qrCode'], // 可以指定扫二维码qrCode还是一维码barCode，默认二者都有
        success: function (res) {
          const result = res.resultStr // 当needResult 为 1 时，扫码返回的结果
          // 进入明细
          if(result){
            this.messageNo = "";
            this.messageCom = "";
            this.messageNumber = "";
            let message = "";
            let flag = 0;
            // 拆分result进行验证快递公司
            if(result.indexOf("deppon") != -1){
              // 德邦快递
              item.courierCompanyId = 70662;
              message = "德邦快递";
            }else if(result.indexOf("ky-express") != -1){
              // 跨越速运
              item.courierCompanyId = 70663;
              message = "跨越速运";
            }else if(result.indexOf("sf-express") != -1){
              // 顺丰速运
              item.courierCompanyId = 70664;
              message = "顺丰速运";
            }else if(result.indexOf("padtf") != -1){
              // 平安达
              item.courierCompanyId = 70949;
              message = "平安达";
            }else if(result.indexOf("uc56") != -1){
              // 优速快递
              message = "优速快递请扫条码";
              this.messageCom = message;
              this.$refs['static'].show();
              return;
            }else{
              item.courierCompanyId = 70953;
              message = "优速快递";
              flag = 1;
            }
            if(flag == 0){
              // 绑定快递单号
              item.courierNumber = result.substring(result.indexOf("=") + 1);
              if(result.indexOf("ky-express") != -1 && item.courierNumber.length > 4){
                // 跨越速运截取后四位不要
                item.courierNumber = item.courierNumber.substring(0,item.courierNumber.length-4);
              }
            }else{
              if(result.indexOf(",") != -1){
                const splitRes = result.split(",");
                if(splitRes.length > 1){
                  item.courierNumber = splitRes[1];
                }
              }
            }
            item.scanUserId = this.company.userId;
            this.$axios.fetchPost("f/wechat/produce/updateCourierNumber", item).then(result => {
              if(result.data){
                this.loadData();
                this.messageNo = item.no;
                this.messageCom = message;
                this.messageNumber = item.courierNumber;
                this.$refs['static'].show();
                this.$parent.enableLoadFlag(false);
              }
            }).catch(err => {console.log(err);});
          }
        },
        error: function () {
          console.log('系统错误');
        }
      })
    },
  }
}
</script>
