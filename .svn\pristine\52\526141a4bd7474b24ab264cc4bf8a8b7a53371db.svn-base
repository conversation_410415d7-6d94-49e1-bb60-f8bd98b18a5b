<template>
  <div class="ld-contact-card">
    <div class="ld-card-container">
      <!-- 装饰元素 -->
      <div class="ld-decor-circle ld-decor-circle-top-right"></div>
      <div class="ld-decor-circle ld-decor-circle-bottom-left"></div>

      <!-- 内容区域 -->
      <div class="ld-content-wrapper">
        <!-- 文字区域 -->
        <div class="ld-text-section">
          <h3 class="ld-title">全球服务热线</h3>
          <p class="ld-phone-number">13714723568</p>
          <div class="ld-info-items">
            <div class="ld-info-item">
              <i class="fa fa-envelope"></i>
              <a :href="'mailto:' + email" class="ld-link">{{ email }}</a>
            </div>
            <div class="ld-info-item">
              <i class="fa fa-clock"></i>
              <span>24/7 全天候服务</span>
            </div>
          </div>
        </div>

        <!-- 图片区域 -->
        <div class="ld-qrcode-section">
          <div class="ld-qrcode-wrapper">
            <img :src="qrcodeUrl" alt="领德人微信公众号" class="ld-qrcode-image">
          </div>
          <p class="ld-qrcode-text">扫码关注领德人公众号</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ContactCard',
  props: {
    email: {
      type: String,
      default: '<EMAIL>'
    },
    qrcodeUrl: {
      type: String,
      default: process.env.BASE_URL + 'media/apply/code.jpg',
    }
  }
}
</script>

<style scoped>
/* 防止与Bootstrap冲突的命名空间前缀: ld- */

.ld-contact-card {
  margin-bottom: 1.5rem;
}

.ld-card-container {
  background: linear-gradient(90deg, #6c6e72, #839ce1);
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 1.25rem;
  overflow: hidden;
  position: relative;
}

.ld-decor-circle {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
}

.ld-decor-circle-top-right {
  top: 0;
  right: 0;
  width: 10rem;
  height: 10rem;
  margin-top: -2.5rem;
  margin-right: -5rem;
}

.ld-decor-circle-bottom-left {
  bottom: 0;
  left: 0;
  width: 8rem;
  height: 8rem;
  margin-bottom: -2rem;
  margin-left: -4rem;
}

.ld-content-wrapper {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 1rem;
  position: relative;
  z-index: 10;
}

.ld-text-section {
  flex: 1;
  min-width: 0;
}

.ld-title {
  color: white;
  font-size: clamp(1.15rem, 3vw, 1.5rem);
  font-weight: bold;
  margin-bottom: 0.25rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ld-phone-number {
  color: rgba(255, 255, 255, 0.9);
  font-size: clamp(0.95rem, 2vw, 1.15rem);
  font-weight: medium;
  margin-bottom: 0.5rem;
}

.ld-info-items {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.ld-info-item {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
}

.ld-info-item i {
  margin-right: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.ld-link {
  color: white;
  text-decoration: none;
  white-space: nowrap;
}

.ld-link:hover {
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s;
}

.ld-qrcode-section {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ld-qrcode-wrapper {
  background-color: white;
  padding: 0.375rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: transform 0.3s;
}

.ld-qrcode-wrapper:hover {
  transform: scale(1.05);
}

.ld-qrcode-image {
  width: 7rem;
  height: 7rem;
  object-fit: cover;
  display: block;
}

.ld-qrcode-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.7rem;
  font-weight: medium;
  text-align: center;
  margin-top: 0.375rem;
}

/* 响应式调整 */
@media (min-width: 768px) {
  .ld-card-container {
    padding: 1.5rem;
  }

  .ld-content-wrapper {
    gap: 1.5rem;
  }

  .ld-qrcode-image {
    width: 8rem;
    height: 8rem;
  }

  .ld-qrcode-text {
    font-size: 0.875rem;
  }
}
</style>
