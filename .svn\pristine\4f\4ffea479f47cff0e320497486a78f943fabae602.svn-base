<template>
  <div>
    <div v-if="showAuditData && showAuditData.length > 0 && showAuditData[0].customerType">
      <div>
        <div class="d-flex justify-content-between align-items-center pb-1 border-bottom">
          <div class="font-weight-bolder" v-if="showAuditData[0].customerType == 24">客诉品质信息</div>
          <div class="font-weight-bolder" v-if="showAuditData[0].customerType == 25 || showAuditData[0].customerType == 27">客诉品质信息</div>
          <div class="font-weight-bolder" v-if="showAuditData[0].customerType == 26">品质信息</div>
          <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlag">{{ flag && flag == '1' ? '收起' : '展开' }}</button>
        </div>
        <div v-if="flag == 1">
          <div class="alert-light pt-2 align-items-center" v-if="showAuditData[0].company">
            <div class="d-flex justify-content-between">
              <div>
                提交公司:{{showAuditData[0].company.name}}
              </div>
            </div>
          </div>
          <div v-if="(showAuditData[0].customerType == 24 || showAuditData[0].customerType == 25 || showAuditData[0].customerType == 27) && showAuditData[0].reject">
            <div class="d-flex justify-content-between">
              <div>
                客诉单编号:{{showAuditData[0].reject.no}}
              </div>
            </div>
            <div class="rd-flex justify-content-between">
              <div>
                客户编号:{{showAuditData[0].reject.customer.no}}
              </div>
              <div>
                客诉简称:{{showAuditData[0].reject.customer.shortName}}
              </div>
            </div>
            <div class="d-flex justify-content-between">
             <div>
                申请人:{{showAuditData[0].reject.createdBy ? showAuditData[0].reject.createdBy.userName : null}}
              </div>
              <div>
				    	  申请时间:{{showAuditData[0].reject.businessHappenTimeStr}}
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>
                送货日期:{{showAuditData[0].reject.deliveryDateStr}}
              </div>
              <div>
                合同号:{{showAuditData[0].reject.contractNo}}
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>
                补发单号:{{showAuditData[0].reject.fedNo}}
              </div>
              <div>
                生产编号:{{showAuditData[0].reject.craftNos}}
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>
                退货数量:{{showAuditData[0].reject.quantity}}
              </div>
              <div>
                退货金额:{{showAuditData[0].reject.normalAmount}}
              </div>
            </div>
            <div class="d-flex justify-content-between">
             <div>
                补发数量:{{showAuditData[0].reject.fedNum}}
              </div>
              <div>
                赔偿总额:{{showAuditData[0].reject.satisfactionAmount}}
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>
                其中生产承担金额:{{showAuditData[0].reject.groupAmount}}
              </div>
              <div>
                总金额:{{showAuditData[0].reject.amount}}
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>
                原因及要求:{{showAuditData[0].reject.rejectCause}}
              </div>
              <div>
                处理意见:{{showAuditData[0].reject.treatOpinion}}
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>
                备注:{{showAuditData[0].reject.remark}}
              </div>
            </div>
          </div>
          <div v-if="(showAuditData[0].customerType == 25 || showAuditData[0].customerType == 26 || showAuditData[0].customerType == 27) && showAuditData[0].inspect">
            <div class="d-flex justify-content-between">
              <div>
                投料单编号:{{showAuditData[0].inspect.feedNo}}
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>
                通知单编号:{{showAuditData[0].inspect.notificationNo}}
              </div>
              <div>
                生产编号:{{showAuditData[0].inspect.craftNo}}
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>
                检测时间:{{showAuditData[0].inspect.checkDateStr}}
              </div>
              <div>
                检测人:{{showAuditData[0].inspect.checker ? showAuditData[0].inspect.checker.userName : null}}
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>
                操作人:{{showAuditData[0].inspect.operator ? showAuditData[0].inspect.operator.userName : null}}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="d-flex justify-content-between align-items-center pb-1 border-bottom" v-if="(showAuditData[0].customerType == 25 || showAuditData[0].customerType == 26 || showAuditData[0].customerType == 27) && showAuditData[0].inspect && showAuditData[0].inspect.reworkList && showAuditData[0].inspect.reworkList.length > 0">
          <div class="font-weight-bolder">返工</div>
          <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlagTwo">{{ flagTwo && flagTwo == '1' ? '收起' : '展开' }}</button>
        </div>
        <div v-if="flagTwo == '1'">
          <template v-for="item in showAuditData[0].inspect.reworkList">
            <div class="pt-1 pb-1 border-bottom alert-secondary" :key="item.recordId">
              <div class="d-flex justify-content-between">
                <div v-if="item.dutyProcess">
                  责任工序:{{item.dutyProcess ? item.dutyProcess.category : null}}
                </div>
                <div v-if="item.dutyProcessMoreName">
                  责任工序:{{item.dutyProcessMoreName}}
                </div>
                <div>
                  返工数量:{{item.reworkQty}}
                </div>
              </div>
              <div class="d-flex justify-content-between">
                <div>
                  返工原因:{{item.reworkCause}}
                </div>
                <div>
                  备注:{{item.reworkResult}}
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div>
        <div class="d-flex justify-content-between align-items-center pb-1 border-bottom" v-if="(showAuditData[0].customerType == 25 || showAuditData[0].customerType == 26 || showAuditData[0].customerType == 27) && showAuditData[0].inspect && showAuditData[0].inspect.discardList && showAuditData[0].inspect.discardList.length > 0">
          <div class="font-weight-bolder">报废</div>
          <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlagThree">{{ flagThree && flagThree == '1' ? '收起' : '展开' }}</button>
        </div>
        <div v-if="(showAuditData[0].customerType == 25 || showAuditData[0].customerType == 26 || showAuditData[0].customerType == 27) && showAuditData[0].inspect && showAuditData[0].inspect.discardList && showAuditData[0].inspect.discardList.length > 0 && flagThree == 1">
          <template v-for="discard in showAuditData[0].inspect.discardList">
            <div class="pt-1 pb-1 border-bottom alert-secondary" :key="discard.recordId">
              <div class="d-flex justify-content-between">
                <div>
                  责任方:{{discard.discardCauseOne}}
                </div>
                <div>
                  责任原因:{{discard.discardCause}}
                </div>
              </div>
              <div class="d-flex justify-content-between" v-if="showAuditData[0].customerType == 25 || showAuditData[0].customerType == 27">
                <div class="col">
                  报废数量:{{discard.discardPcsQty}}
                </div>
                <div>
                  备注:{{discard.remark}}
                </div>
              </div>
              <div class="d-flex justify-content-between" v-if="showAuditData[0].customerType == 26">
                <div>
                  扣数工序:{{discard.dutyProcess ? discard.dutyProcess.category : null}}
                </div>
                <div>
                  报废数量:{{discard.discardPcsQty}}
                </div>
              </div>
              <div class="d-flex justify-content-between" v-if="showAuditData[0].customerType == 26">
                <div>
                  备注:{{discard.remark}}
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div>
        <div class="d-flex justify-content-between align-items-center pb-1 border-bottom" v-if="(showAuditData[0].customerType == 25 || showAuditData[0].customerType == 26 || showAuditData[0].customerType == 27) && showAuditData[0].inspect && showAuditData[0].inspect.taskDetailList && showAuditData[0].inspect.taskDetailList.length > 0">
          <div class="font-weight-bolder">替换/二次补料</div>
          <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlagFour">{{ flagFour && flagFour == '1' ? '收起' : '展开'</button>
        </div>
        <div v-if="(showAuditData[0].customerType == 25 || showAuditData[0].customerType == 26 || showAuditData[0].customerType == 27) && showAuditData[0].inspect && showAuditData[0].inspect.taskDetailList && showAuditData[0].inspect.taskDetailList.length > 0 && flagFour == 1">
          <template v-for="replace in showAuditData[0].inspect.taskDetailList">
            <div class="pt-1 pb-1 border-bottom alert-secondary" :key="replace.recordId">
              <div class="d-flex justify-content-between">
                <div>
                  责任方:{{replace.discardUnit}}
                </div>
                <div>
                  责任原因:{{replace.discardCause}}
                </div>
              </div>
              <div class="d-flex justify-content-between">
                <div>
                  类型:
                  <span v-if="replace.inspectType == 3">替换</span>
                  <span v-if="replace.inspectType == 4">二次补料</span>
                </div>
                <div>
                  <span v-if="replace.inspectType == 3">替换数量:</span>
                  <span v-if="replace.inspectType == 4">二次补料数量:</span>
                  {{audit.voList[0].inspect.replaceQty}}
                </div>
              </div>
              <div class="d-flex justify-content-between" v-if="replace.remark">
                <div>
                  备注:{{replace.remark}}
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "quality",
  props: {
    showAuditData: [Object, Array],
  },
  data() {
    return{
      flag: "1",
      flagTwo: "2",
      flagThree: "2",
      flagFour: "2"
    }
  },
  methods: {
    changeFlag() {
      if (this.flag == "1"){
        this.flag = "2";
      }else {
        this.flag = "1";
      }
    },
    changeFlagTwo() {
      if (this.flagTwo == "1"){
        this.flagTwo = "2";
      }else {
        this.flagTwo = "1";
      }
    },
    changeFlagThree() {
      if (this.flagThree == "1"){
        this.flagThree = "2";
      }else {
        this.flagThree = "1";
      }
    },
    changeFlagFour() {
      if (this.flagFour == "1"){
        this.flagFour = "2";
      }else {
        this.flagFour = "1";
      }
    },
  }
}
</script>