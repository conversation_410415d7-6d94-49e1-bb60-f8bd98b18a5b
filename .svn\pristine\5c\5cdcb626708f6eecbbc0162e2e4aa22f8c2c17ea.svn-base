<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">产品报价</h3>
    </div>
    <div class="card">
      <div class="card-body p-3">
        <div class="d-flex align-items-center font-size-lg font-weight-bolder">
          尊敬的供应商，您好！因业务需求，请每月5号前更新产品价格，以便更好合作，感谢支持！
        </div>
      </div>
    </div>
    <template v-for="(item, index) in functionItems">
      <div :class="['d-flex justify-content-between align-items-center p-3 border-bottom font-size-lg bg-white mt-3']" :key="index">
        <div class="d-flex align-items-center">
          <div style="background-color: white; border-radius: 0.5rem; padding: 0.5rem; text-align: center;
                  border: 1px solid rgba(0,0,0,0.05); box-shadow: 0 1px 2px rgba(0,0,0,0.05);
                  transition: transform 0.2s ease, box-shadow 0.2s ease;">
            <div
                :style="item.iconBg + '; width: 3rem; height: 3rem; margin: 0 auto 0.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.05);'"
            >
              <i class="fa" :class="['fa-lg', item.icon]" :style="item.iconColor" aria-hidden="true"></i>
            </div>
          </div>
          <div class="ml-3">
            <div style="font-size: 1.3rem;">{{ item.title }}</div>
          </div>
        </div>
        <div>
          <button class="btn btn-outline-primary" @click="router_page(item)">前往</button>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
name: "mainQuotation",
  props: {
    functionItems: {
      type: Array,
      default: () => [
        {
          title: "原材料报价",
          icon: "fa-balance-scale",
          iconBg: "background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.2) 100%);",
          iconColor: "color: #10B981; text-shadow: 0 1px 2px rgba(16, 185, 129, 0.2);",
          routerName: "/b/mainQuotation/rawMaterialQuotation"
        },
        {
          title: "线路板工厂报价",
          icon: "fa-industry",
          iconBg: "background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(67, 97, 238, 0.2) 100%);",
          iconColor: "color: #4361EE; text-shadow: 0 1px 2px rgba(67, 97, 238, 0.2);",
          routerName: "/b/mainQuotation/productQuotation"
        },
      ],
    },
  },
  methods: {
    router_page(item) {
      this.$router.push(item.routerName);
    },
  },
}
</script>