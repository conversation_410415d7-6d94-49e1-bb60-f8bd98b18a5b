<template>
  <div>
    <div class="bg-white rounded-sm p-2" v-if="commentEntity.recordId && commentEntity.recordList && commentEntity.recordList.length > 0">
      <div class="d-flex justify-content-between pb-2 border-bottom">
        <div class="font-weight-bolder">审批记录</div>
      </div>
      <div class="pt-2 pb-2 border-bottom" v-if="commentEntity.user">
        <div class="d-flex">
          <div class="image-container-common mr-2 mb-0" style="width: 2.8rem;height: 2.8rem;"><img :src="commentEntity.user.downloadUrl"></div>
          <div>
            <div class="d-flex align-items-center">
              <div class="badge badge-primary px-2 py-1">提交</div>
              <div class="pl-2">{{commentEntity.name}}</div>
            </div>
            <div class="text-muted pt-1">{{commentEntity.recordList[0].createDate}}</div>
          </div>
        </div>
      </div>
      <template v-for="item in commentEntity.recordList">
        <div class="pt-2 pb-2 border-bottom" :key="item.recordId">
          <div class="d-flex">
            <div class="image-container-common mr-2 mb-0" style="width: 2.8rem;height: 2.8rem;"><img :src="item.user && item.user.downloadUrl ? item.user.downloadUrl : !item.status ? loaderLogo : ''"></div>
            <div>
              <div class="d-flex align-items-center" v-if="item.status">
                <div class="badge badge-success px-2 py-1" v-if="item.status == 'assent'">同意</div>
                <div class="badge badge-danger px-2 py-1" v-else>驳回</div>
                <div class="pl-2">
                  {{item.handelPositionIdName}}-{{item.lastUpdateByName}}
                  <span v-if="item.status == 'assent'" class="pt-1 text-primary">({{item.approveUsersName}})</span>
                </div>
              </div>
              <div class="d-flex align-items-center" v-else>
                <div class="badge badge-warning px-2 py-1">待办</div>
                <div class="pl-2">{{item.handelPositionIdName}}({{item.approveUsersName ? item.approveUsersName : "无审批人，请联系行政完善流程"}})</div>
              </div>
              <div class="text-muted pt-1">
                {{item.lastUpdateDate ? item.lastUpdateDate : item.createDate}}
              </div>
            </div>
          </div>
          <div v-if="item.content">{{item.content}}</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: "auditLogList",
  props: {
    commentEntity: Object
  },
  computed: {
    loaderLogo() {
      return process.env.BASE_URL + "media/logos/wait.png";
    },
  },
}
</script>
