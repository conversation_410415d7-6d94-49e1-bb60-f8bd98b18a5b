<template>
  <div class="pl-3 pr-3 pt-3 rounded-lg bg-white">
    <div class="row">
      <!-- 产能购买 -->
      <div class="col-6 mb-3">
        <div class="card border border-light shadow-sm rounded-xl overflow-hidden hover-card">
          <div class="card-body p-3 d-flex align-items-center">
            <div class="w-12 h-12 rounded-lg bg-primary-light d-flex align-items-center justify-content-center mr-3 flex-shrink-0">
              <i class="fa fa-microchip text-primary text-xl"></i>
            </div>
            <div class="flex-1">
              <div class="font-weight-bold text-gray-800 text-truncate mb-0.5">产能购买</div>
              <div class="text-xs text-muted text-truncate line-height-xs">享优先生产</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 园区租赁 -->
      <div class="col-6 mb-3">
        <div class="card border border-light shadow-sm rounded-xl overflow-hidden hover-card">
          <div class="card-body p-3 d-flex align-items-center">
            <div class="w-12 h-12 rounded-lg bg-secondary-light d-flex align-items-center justify-content-center mr-3 flex-shrink-0">
              <i class="fa fa-building text-secondary text-xl"></i>
            </div>
            <div class="flex-1">
              <div class="font-weight-bold text-gray-800 text-truncate mb-0.5">园区租赁</div>
              <div class="text-xs text-muted text-truncate line-height-xs">线路板大生态圈</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 积分商场 -->
      <div class="col-6 mb-3">
        <div class="card border border-light shadow-sm rounded-xl overflow-hidden hover-card">
          <div class="card-body p-3 d-flex align-items-center">
            <div class="w-12 h-12 rounded-lg bg-accent-light d-flex align-items-center justify-content-center mr-3 flex-shrink-0">
              <i class="fa fa-gift text-accent text-xl"></i>
            </div>
            <div class="flex-1">
              <div class="font-weight-bold text-gray-800 text-truncate mb-0.5">积分商场</div>
              <div class="text-xs text-muted text-truncate line-height-xs">海量好礼免费换</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 在线客服 -->
      <div class="col-6 mb-3">
        <div class="card border border-light shadow-sm rounded-xl overflow-hidden hover-card">
          <div class="card-body p-3 d-flex align-items-center">
            <div class="w-12 h-12 rounded-lg bg-info-light d-flex align-items-center justify-content-center mr-3 flex-shrink-0">
              <i class="fa fa-comments text-info text-xl"></i>
            </div>
            <div class="flex-1">
              <div class="font-weight-bold text-gray-800 text-truncate mb-0.5">在线客服</div>
              <div class="text-xs text-muted text-truncate line-height-xs">24小时在线</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FunctionCards',
  data() {
    return {
      // 数据可从props传入
    }
  },
  methods: {
    // 点击事件处理
    router_page(item) {
      console.log('点击了:', item)
      // 可以通过this.$emit触发自定义事件
    }
  }
}
</script>

<style scoped>
/* 自定义颜色 */
.bg-primary-light {
  background-color: rgba(22, 93, 255, 0.1);
}

.bg-secondary-light {
  background-color: rgba(54, 211, 153, 0.1);
}

.bg-accent-light {
  background-color: rgba(255, 159, 67, 0.1);
}

.bg-info-light {
  background-color: rgba(58, 191, 248, 0.1);
}

.text-primary {
  color: #165DFF !important;
}

.text-secondary {
  color: #36D399 !important;
}

.text-accent {
  color: #FF9F43 !important;
}

.text-info {
  color: #3ABFF8 !important;
}

/* 自定义Bootstrap 4.5不支持的样式 */
.w-12 {
  width: 3rem;
}

.h-12 {
  height: 3rem;
}

.hover-card {
  transition: all 0.3s ease;
}

.hover-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mb-0.5 {
  margin-bottom: 0.125rem;
}

.line-height-xs {
  line-height: 1;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .card-body {
    padding: 0.75rem !important;
  }

  .w-12, .h-12 {
    width: 2.5rem;
    height: 2.5rem;
  }

  .text-xl {
    font-size: 1.1rem !important;
  }

  .font-weight-bold {
    font-size: 0.85rem;
  }

  .text-xs {
    font-size: 0.65rem;
  }
}
</style>
