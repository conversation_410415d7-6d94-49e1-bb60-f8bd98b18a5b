<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">
        工时报表
      </h3>
    </div>
    <div class="card card-custom gutter-b">
      <div class="card-body d-flex flex-column p-3">
        <div class="row pb-2 border-bottom">
          <div class="col-xl-12">
            <div class="row pb-2 align-items-center">
              <label class="text-muted pl-3">&nbsp;维保公司:</label>
              <div class="col">
                <select class="form-control input-icon-sm" v-model="repairCompanyId" v-on:change="loadTimeReport">
                  <option v-for="item in repairCompanyList" :key="item.recordId" :value="item.recordId">
                    {{item.name}}
                  </option>
                </select>
              </div>
            </div>
            <div class="row pb-2 align-items-center">
              <label class="text-muted pl-3">&nbsp;统计月份:</label>
              <div class="col">
                <select class="form-control input-icon-sm" v-model="monthly" v-on:change="loadTimeReport">
                  <option v-for="month in monthlyList" :key="month" :value="month">
                    {{month}}
                  </option>
                </select>
              </div>
            </div>
            <div class="row pb-2 align-items-center">
              <label class="text-muted pl-3">&nbsp;维修状态:</label>
              <div class="col">
                <select v-model="repairStatus" class="form-control input-icon-sm" placeholder="请选择维修状态"  v-on:change="loadTimeReport"
                        data-none-selected-text="维修状态">
                  <option value="">所有</option>
                  <option value= "1003">待确认</option>
                  <option value= "1004">已确认</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <div class="row pb-1 pt-1">
          <div class="col">
            <label class="text-muted font-size-h3">维保员工工时</label>
          </div>
        </div>
        <div class="row" v-if="!(repairPersonHourPageList && repairPersonHourPageList.length > 0)">
          <div class="col-xl-12">
            <h3 class="text-primary">本月暂无维保数据</h3>
          </div>
        </div>
        <template v-for="item in repairPersonHourPageList">
          <div class="row border-bottom pt-3 pb-3" :key="item.recordId">
            <div class="col-6 text-muted">
              {{item.userName}}
            </div>
            <div class="col text-right text-muted">
              <a href="#" v-on:click="showRepairDetail(item,1,null)">{{item.useHour ? item.useHour : 0}}</a>工时
            </div>
          </div>
        </template>
        <div class="row" v-if="userMsg.manageSatus == '1'">
          <div class="col-xl-12">
            <div class="row pb-1 pt-3 border-top">
              <div class="col">
                <label class="text-muted font-size-h3">维保客户工时</label><br>

                <label class="text-muted font-size-h5">现金：累计 ({{cashTotalHours ? cashTotalHours : 0}}工时/{{((cashTotalHours ? cashTotalHours:0) * 60).toFixed(2)}}元)&nbsp;已收 ({{cashReceivedHours ? cashReceivedHours : 0}}工时/
                  {{((cashReceivedHours ? cashReceivedHours : 0) * 60).toFixed(2)}}元)&nbsp;未收 ({{cashUnpaidHours ? cashUnpaidHours : 0}}工时/ {{((cashUnpaidHours ? cashUnpaidHours : 0) * 60).toFixed(2)}}元)</label><br>
                <label class="text-muted font-size-h5">套餐：维保 ({{maintenanceHours ? maintenanceHours : 0}}工时)&nbsp;累计客户充值 ({{customerTotalHoursObj.buyHoursTotal ? customerTotalHoursObj.buyHoursTotal : 0}}工时/{{(customerTotalHoursObj.amountReceivedTotal ? customerTotalHoursObj.amountReceivedTotal:0).toFixed(2)}}元)</label><br>
                <label class="text-muted font-size-h5">配件费：累计{{sparePartsCostObj.sparePartsCostSum}}&nbsp;已结算：{{sparePartsCostObj.settledPartCostSum}}</label>
              </div>
            </div>
            <div class="row pb-1 pt-3 border-top">
              <div class="col">
                <label class="text-muted font-size-h5">月份:{{monthly}}</label>
              </div>
            </div>
            <template v-for="(item, index) in customerHourPageList">
              <div class="row pt-2 pb-2 border-bottom" :key="index" v-if="purchasePlan == '1'">
                <div class="col-xl-12">
                  <div class="row">
                    <div class="col-xl-12">
                      <span v-if="item.status == 1" class="badge badge-warning">汇款中</span>
                      <span v-if="item.status == 2" class="badge badge-primary">使用中</span>
                      <span v-if="item.status == 3" class="badge badge-danger">已过期</span>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-xl-12">
                      套餐:&nbsp;{{item.buyHours}}工时&nbsp;{{item.amountReceived}}元&nbsp;&nbsp;&nbsp;时间:&nbsp;{{item.createdDate}}
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-xl-12">
                      <a href="#" v-on:click="showRepairDetail(item,2,null)">已用工时:&nbsp;{{item.useHour ? item.useHour : 0}}</a>&nbsp;&nbsp;&nbsp;
                      前套餐结余:&nbsp;{{item.surplusHours ? item.surplusHours : 0}}&nbsp;&nbsp;&nbsp;
                      总工时:&nbsp;{{((item.surplusHours ? item.surplusHours : 0) + item.buyHours).toFixed(2)}}
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-for="(item, index) in customerHourPageList">
              <div class="row pt-2 pb-2 border-bottom" :key="index" v-if="purchasePlan == '2'">
                <div class="col-xl-12">
                  <div class="row">
                    <div class="col-xl-12">
                      套餐:&nbsp;&nbsp;公司:&nbsp;{{item.name}} &nbsp;购买工时: {{item.buyHoursTotal ? item.buyHoursTotal : 0}}&nbsp;购买金额:&nbsp;{{item.amountReceivedTotal}}
                      <a href="#" v-on:click="showRepairDetail(item,2,1)">已用工时:&nbsp;{{item.useHour ? item.useHour : 0}}</a>&nbsp;<span v-if="item.purchaseTime">&nbsp;购买时间:&nbsp;{{item.purchaseTime}}</span>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-9 pt-3">
                      现金:&nbsp;&nbsp;累计<a href="#" v-on:click="showRepairDetail(item,2,2)">{{item.totalHours ? item.totalHours : 0}}工时</a>
                      /金额: &nbsp;{{((item.totalHours ? item.totalHours : 0) * 60).toFixed(2)}} &nbsp;已结算: &nbsp;<a href="#" v-on:click="showRepairDetail(item,2,4)">{{item.billedHours ? item.billedHours : 0}}工时</a>/金额：{{((item.billedHours ? item.billedHours : 0) * 60).toFixed(2)}}&nbsp;
                      &nbsp;<span class="text-danger" v-if="item.unbilledHoursTotal">历史应收未收:&nbsp;
                                                            <a href="#" v-on:click="showRepairDetail(item,2,3)">{{item.unbilledHoursTotal ? item.unbilledHoursTotal : 0}}工时</a></span>
                      <span v-if="item.unbilledHoursTotal">/金额: &nbsp;{{((item.unbilledHoursTotal ? item.unbilledHoursTotal : 0) * 60).toFixed(2)}}</span>
                    </div>
                    <div class="col-3 pt-3 text-right">
                      <button class="btn btn-sm btn-danger" v-if="((item.totalHours ? item.totalHours : 0) - (item.billedHours ? item.billedHours : 0) > 0)
                                                                || ((item.sparePartsCostSum ? item.sparePartsCostSum : 0) - (item.settledPartCostSum ? item.settledPartCostSum : 0) > 0)" v-on:click="openHourCloseRecord(item)">结账</button>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-xl-12">
                      配件费用: &nbsp;{{item.sparePartsCostSum ? item.sparePartsCostSum : 0}} &nbsp; 已结算:&nbsp;{{item.settledPartCostSum ? item.settledPartCostSum : 0}}
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <div class="row" v-if="purchasePlan == '1'">
              <div class="col-xl-12">
                <div class="row pb-1 pt-3 border-top">
                  <div class="col">
                    <label class="text-muted font-size-h5">未购买套餐工时</label>
                  </div>
                </div>
                <template v-for="(item, index) in notBuyPackageList">
                  <div class="row pt-2 pb-2 border-bottom" :key="index">
                    <div class="col-xl-12">
                      <div class="row">
                        <div class="col-xl-12">
                          维保公司:&nbsp;{{item.repaircompanyName}}&nbsp;&nbsp;&nbsp;维保客户:&nbsp;{{item.applycompanyName}}
                        </div>
                      </div>
                      <div class="row">
                        <div class="col-xl-12">
                          <a href="#" v-on:click="showRepairDetail(item,2,null)">已用工时:&nbsp;{{item.useHour ? item.useHour : 0}}</a>&nbsp;&nbsp;&nbsp;
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <b-modal ref="hourBillingWindow" title="工时结账" hide-footer>
      <div>
        <div class="row pt-3 pb-3">
          <div class="col-12">
            <label><span style="color: red;">*</span>工时:</label>
            <input type="text" class="form-control" placeholder="请输入结账工时" v-model="timeReport.closeHours"/>
          </div>
        </div>
        <div class="row pt-3 pb-3">
          <div class="col-12">
            <label><span style="color: red;">*</span>金额:</label>
            <input type="text" class="form-control" placeholder="请输入结账金额" v-model="timeReport.closeAccount"/>
          </div>
        </div>
        <div class="row pt-6">
          <div class="col-12 text-right">
            <button type="button" class="btn btn-light-primary font-weight-bold" v-on:click="closeWindow">取消</button>&nbsp;
            <button type="button" class="btn btn-light-primary font-weight-bold" v-on:click="saveHourCloseRecord">保存</button>
          </div>
        </div>
      </div>
    </b-modal>

    <b-modal ref="showHourDetail" :title="number === 1 ? '人员工时明细':'客户工时明细'" hide-footer>
      <div>
        <div class="row p-3">
          <div class="col-xl-12">
            <template v-for="(item, index) in showHourDetailList">
              <div class="row border-bottom pb-2" :key="index">
                <div class="col-xl-12">
                  <div class="row">
                    <div class="col-xl-12">
                      维保编号:{{item.preserveNo}}
                    </div>
                  </div>
                  <div class="row">
                    <div class="col">
                      申报:&nbsp;<span>{{item.applyCompany ? item.applyCompany.name : ""}}-{{item.applyName}}</span>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col">
                      维保:&nbsp;<span>{{item.repairCompany ? item.repairCompany.name : ""}}<span v-if="item.repairName">-{{item.repairName}}</span></span>
                    </div>
                  </div>
                  <div class="row font-weight-bold" v-if="item.materialName">
                    <div class="col">
                      {{item.materialType}}:&nbsp;{{item.materialName}}&nbsp;
                      规格:&nbsp;{{item.specification}}
                    </div>
                  </div>
                  <div class="row font-weight-bold" v-else>
                    <div class="col">
                      设备:&nbsp;{{item.deviceName}}
                    </div>
                  </div>
                  <div class="row" v-if="item.hourSectionList && item.hourSectionList.length > 0">
                    <div class="col-6 pt-1" v-for="row in item.hourSectionList" :key="row.recordId">
                      <span v-if="row.maintenanceTime && row.statusFlag == 1">开始：{{row.maintenanceTime}}</span>
                      <span v-if="row.maintenanceTime && row.statusFlag == 2">结束：{{row.maintenanceTime}}</span>
                      <span v-if="row.maintenanceTime && row.statusFlag == 3">暂停：{{row.maintenanceTime}}</span>
                    </div>
                  </div>
                  <div class="row" v-if="item.expectStart && !item.startDate">
                    <div class="col">
                      预估:&nbsp;{{item.expectStart}} 开始维保
                    </div>
                  </div>
                  <div class="row text-primary" v-if="item.startDate && item.endDate">
                    <div class="col">
                      工时:&nbsp;{{item.useHour? item.useHour : '0.00'}} &nbsp;&nbsp;
                      <span v-if="item.paidPackageHours"> 已结算套餐工时:{{item.paidPackageHours ? item.paidPackageHours : '0.00'}}</span>
                      <span v-if="item.billedHours"> 已结算现金工时:{{item.billedHours ? item.billedHours : '0.00'}}</span>
                    </div>
                  </div>
                  <div class="row" v-if="item.problem">
                    <div class="col">
                      描述:&nbsp;{{item.problem}}
                    </div>
                  </div>
                  <div class="row" v-if="item.remark">
                    <div class="col">
                      原因:&nbsp;{{item.remark}}
                    </div>
                    <div class="col" v-if="item.reason">
                      申诉原因:&nbsp;{{item.reason}}
                    </div>
                  </div>
                  <div class="row" v-if="item.repairStatus == 1003">
                    <div class="col-xl-12" v-if="item.remarkT">
                      <div class="row">
                        <div class="col">
                          <div class="checkbox-inline">
                            效率:&nbsp;
                            <div class="star-rating">
                              <template v-for="(itemT, index) in efficiencyList">
                                <i :class="{'fas fa-star text-danger': index + 1 <= item.checkedStarsOne, 'far fa-star': index + 1 > item.checkedStarsOne}" :key="itemT.value"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                              </template>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="row">
                        <div class="col">
                          <div class="checkbox-inline">
                            质量:&nbsp;
                            <div class="star-rating">
                              <template v-for="(itemT, index) in massScoreList">
                                <i :class="{'fas fa-star text-danger': index + 1 <= item.checkedStarsTwo, 'far fa-star': index + 1 > item.checkedStarsTwo}" :key="itemT.value"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                              </template>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="row">
                        <div class="col">
                          <div class="checkbox-inline">
                            态度:&nbsp;
                            <div class="star-rating">
                              <template v-for="(itemT, index) in attitudeScoreList">
                                <i :class="{'fas fa-star text-danger': index + 1 <= item.checkedStarsThree, 'far fa-star': index + 1 > item.checkedStarsThree}" :key="itemT.value"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                              </template>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="row">
                        <div class="col">
                          评价:&nbsp;{{item.remarkT}}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
export default {
name: "timeReport",
  created:function(){
    this.num = this.$route.query.num;
  },
  data(){
    return {
      searchInfo: '',
      message: '',
      monthlyList: [],
      monthly: "",
      repairCompanyId: null,
      customerHourPageList:[],
      repairPersonHourPageList:[],
      timeReport:{},
      showHourDetailList:[],
      number:null,
      applyCustomList:[],
      checkedStarsOne: 0,
      checkedStarsTwo:0,
      checkedStarsThree:0,
      efficiencyList:[
        {"recordId" : "1", "value": 1},
        {"recordId" : "2", "value": 2},
        {"recordId" : "3", "value": 3},
        {"recordId" : "4", "value": 4},
        {"recordId" : "5", "value": 5}
      ],
      massScoreList:[
        {"recordId" : "1", "value": 1},
        {"recordId" : "2", "value": 2},
        {"recordId" : "3", "value": 3},
        {"recordId" : "4", "value": 4},
        {"recordId" : "5", "value": 5}
      ],
      attitudeScoreList:[
        {"recordId" : "1", "value": 1},
        {"recordId" : "2", "value": 2},
        {"recordId" : "3", "value": 3},
        {"recordId" : "4", "value": 4},
        {"recordId" : "5", "value": 5}
      ],
      notBuyPackageList:[],
      purchasePlan:"2",
      currentDate:"",
      num:"",
      closeHoursTwo:"",
      closeAccountTwo:"",
      customBuyHoursTotal:"",
      repairUseHours:"",
      cashTotalHours:"",
      cashUnpaidHours:"",
      amountReceivedTotal:"",
      cashReceivedHours:"",
      maintenanceHours:"",
      customerTotalHoursObj:{},
      sparePartsCostObj:{},
      repairStatus:1004,
      repairCompanyList:[],
      userMsg:{},
    }
  },

  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },

  methods: {
    loadData: function () {
      const query = {};
      query.userId = this.userMsg.recordId;
      query.phone = this.userMsg.phone;
      query.moduleId = this.num;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/repair/getUserList", query).then(result => {
        if (result.data) {
          this.repairCompanyList = result.data.repairCompanyList;
          //获取月份
          this.monthlyList = result.data.monthlyList;
          if (this.repairCompanyList && this.repairCompanyList.length > 0)
          {
            this.repairCompanyId = this.repairCompanyList[0].recordId;
          }
          var currentDate = new Date();
          currentDate.setMonth(currentDate.getMonth());
          this.monthly = "" + currentDate.getFullYear() + (currentDate.getMonth() < 10 ? '0' + (currentDate.getMonth() + 1) : (currentDate.getMonth() + 1));
          this.currentDate = this.monthly;
        }
        this.$parent.enableLoadFlag(false);
        this.loadTimeReport();
      }).catch(err => {alert(err)});
    },
    //获取对应维保公司对应的客户
    loadTimeReport: function () {
      const query = {};
      query.userId = this.userMsg.recordId;
      query.monthly = this.monthly;
      query.repairCompanyId = this.repairCompanyId;
      query.repairStatus = this.repairStatus;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/repair/loadTimeReport", query).then(result => {
        if (result && result.data) {
          this.customerHourPageList = result.data.buyPackageList;
          this.repairPersonHourPageList = result.data.repairEmployeeHourList;
          this.notBuyPackageList = result.data.notBuyPackageList;
          this.cashUnpaidHours = result.data.cashUnpaidHours;
          this.cashTotalHours = result.data.cashTotalHours;
          this.amountReceivedTotal = result.data.amountReceivedTotal;
          this.cashReceivedHours = result.data.cashReceivedHours;
          this.maintenanceHours = result.data.maintenanceHours;
          this.customerTotalHoursObj = result.data.customerTotalHoursObj;
          this.sparePartsCostObj = result.data.sparePartsCostObj;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
    showRepairDetail:function (item,number,num) {
      item.monthly = this.monthly;
      this.number = number;
      item.number = number;
      if (number === 2)
      {
        item.applyCompanyId = item.recordId;
      }
      item.repairCompanyId = this.repairCompanyId;
      item.repairMan = item.recordId ? item.recordId : item.repairMan;
      item.num = num;
      this.num = num;
      item.repairStatus = this.repairStatus;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/repair/showRepairDetailList", item).then(result => {
        if (result && result.data) {
          this.showHourDetailList = result.data;
          for (let scoreT of this.showHourDetailList) {
            if (scoreT.efficiency) {
              scoreT.checkedStarsOne = scoreT.efficiency;
            }
            if (scoreT.massScore) {
              scoreT.checkedStarsTwo = scoreT.massScore;
            }
            if (scoreT.attitudeScore) {
              scoreT.checkedStarsThree = scoreT.attitudeScore;
            }
          }
          this.$refs['showHourDetail'].show();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
    closeWindow() {
      this.$refs['hourBillingWindow'].hide();
      this.$refs['showHourDetail'].hide();
    },
    openHourCloseRecord: function (item) {
      this.timeReport = {};
      this.timeReport.applyCompanyId = item.recordId;
      this.timeReport.repairCompanyId = this.repairCompanyId;
      let sparePartsCostHour = (item.sparePartsCostSum  - item.settledPartCostSum) /60;
      this.timeReport.closeHours = (parseFloat(item.totalHours - item.billedHours)  + parseFloat(sparePartsCostHour)).toFixed(4);
      this.timeReport.closeAccount = ((item.totalHours - item.billedHours) * 60 + (item.sparePartsCostSum - item.settledPartCostSum));
      this.closeHoursTwo = (parseFloat(item.totalHours - item.billedHours)  + parseFloat(sparePartsCostHour)).toFixed(4);
      this.closeAccountTwo =  ((item.totalHours - item.billedHours) * 60 + (item.sparePartsCostSum - item.settledPartCostSum));
      this.$refs['hourBillingWindow'].show();
    },
    saveHourCloseRecord: function () {
      if (!this.timeReport || !this.timeReport.closeHours || this.timeReport.closeHours === "0") {
        alert('请输入结账工时！');
        return;
      }
      if (!this.timeReport || !this.timeReport.closeAccount || this.timeReport.closeAccount === "0") {
        alert('请输入结账金额！');
        return;
      }
      if (parseFloat(this.timeReport.closeHours)  > parseFloat(this.closeHoursTwo))
      {
        alert("结账工时不能大于历史应收未收工时！");
        return;
      }
      if (parseFloat(this.timeReport.closeAccount) > parseFloat(this.closeAccountTwo))
      {
        alert("结账金额不能大于历史应收未收金额！");
        return;
      }
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/repair/saveHourCloseRecord", this.timeReport).then(result => {
        if (result && result.data) {
          alert("保存成功！");
          this.loadTimeReport();
          this.$refs['hourBillingWindow'].hide();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
  }
}
</script>
