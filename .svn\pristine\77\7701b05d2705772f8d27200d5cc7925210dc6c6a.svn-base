<template>
  <div>
    <Loader v-bind:logo="loaderLogo" :message="message"></Loader>
    <div class="row pb-3 align-items-center border-bottom" v-if="loadFlag">
      <div class="col-8">
        <div class="d-flex align-items-center">
          <div class="symbol symbol-success pr-3" v-if="userMsg && userMsg.downloadUrl">
            <img alt="Pic" :src="userMsg.downloadUrl" class="h-50"/>
          </div>
          <div class="d-flex flex-column">
            <div v-if="userMsg.phone">
              <span class="text-dark-75 font-weight-bolder">{{ userMsg.phone }}</span>
            </div>
            <div v-if="userMsg.userName">
              您好！{{ userMsg.userName }}
            </div>
            <div v-if="!(userMsg && userMsg.recordId)">请完善以下信息</div>
          </div>
        </div>
      </div>
      <div class="col-4 text-right" v-if="userMsg && userMsg.recordId">
        <button class="btn btn-outline-danger" v-on:click="logOut">切换账号</button>
      </div>
    </div>
    <div class="pt-7" v-if="userMsg && userMsg.recordId">
      <!--员工信息-->
      <div v-if="userMsg.departName">
        <div class="text-muted" style="font-weight: bold;color: white">员工信息</div>
        <div class="pt-1 pb-3 border-bottom">
          <div>
            <span class="text-dark-75 font-weight-bolder">{{userMsg.departName}}</span>
          </div>
          <div class="text-muted d-flex justify-content-between align-items-center">
            <div class="text-truncate">{{userMsg.departmentName}}</div>
            <div>
              <span class="badge badge-primary text-truncate" style="max-width: 12rem;">{{userMsg.position}}</span>
            </div>
          </div>
        </div>
      </div>
      <!--ERP公司-->
      <div class="pt-3" v-if="userMsg && userMsg.erpList">
        <div class="text-muted" style="font-weight: bold;color: white">ERP公司</div>
        <div class="row pt-1 pb-3 border-bottom align-items-center">
          <template v-for="item in userMsg.erpList">
            <div class="col-6 pt-2" :key="item.recordId">
              <span class="badge badge-info pt-3 pb-3 text-truncate w-100 text-left">{{item.name}}</span>
            </div>
          </template>
        </div>
      </div>
      <!--大生态圈企业-->
      <div class="pt-3" v-if="userMsg && userMsg.icloudCompanyList">
        <div class="text-muted" style="font-weight: bold;color: white">大生态圈企业</div>
        <div class="row pt-1 pb-3 border-bottom align-items-center">
          <template v-for="item in userMsg.icloudCompanyList">
            <div class="col-6 pt-2" :key="item.name">
              <span class="badge badge-info pt-3 pb-3 text-truncate w-100 text-left">{{item.name}}</span>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {LOGOUT} from "@/core/services/store/auth.module";
import Loader from "@/view/content/Loader.vue";
import {
  ADD_BODY_CLASSNAME,
  REMOVE_BODY_CLASSNAME
} from "@/core/services/store/htmlclass.module.js";
export default {
  name: "callback",
  components: {
    Loader
  },
  computed: {
    loaderLogo() {
      return process.env.BASE_URL + "media/logos/kyb.jpg";
    },
  },
  data() {
    return {
      userMsg: {},
      loginFlag: '1',
      phone: "",
      phoneCode: '',
      password: "",
      timeLeft: 0,
      isCountingDown: false,
      buttonText: '获取验证码',
      countdownInterval: null,
      loadFlag: false,
      message: '',
      countdown: 1,
      loadingFlag: false
    };
  },
  beforeUnmount() {
    clearInterval(this.countdownInterval);
  },
  beforeMount() {
    // show page loading
    this.$store.dispatch(ADD_BODY_CLASSNAME, "page-loading");
    // Simulate the delay page loading
    setTimeout(() => {
      // Remove page loader after some time
      this.$store.dispatch(REMOVE_BODY_CLASSNAME, "page-loading");
    }, 500);
  },
  mounted() {
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.loadFlag = true;
      this.userMsg = sysUser;
    }else {
      alert("请重新进入公众号");
    }
  },
  methods: {
    logOut: function (){
      if (this.userMsg && this.userMsg.openId && this.userMsg.defaultDb){
        this.message = `账号退出中...`;
        this.$store.dispatch(ADD_BODY_CLASSNAME, "page-loading");
        this.$axios.fetchPost("f/wechat/kybsoft/logOut", this.userMsg).then(result => {
          if(result.data && result.data == "success"){
            this.$store.dispatch(LOGOUT);
            this.message = `账号已退出...`;
            this.$router.push("/jumpNode");
          }else {
            alert("请重新进入公众号");
            this.$store.dispatch(REMOVE_BODY_CLASSNAME, "page-loading");
          }
        }).catch(err => {console.log(err);});
      }else {
        alert("关键信息缺失，请重新进入公众号");
      }
    },
  }
};
</script>
