<template>
  <div>
    <section class="pb-1">
      <Carousel :slides="carouselSlides" :interval="3000" />
    </section>
    <section class="pb-3">
      <NoticeBar/>
    </section>
    <section class="pb-3">
      <FunctionNav/>
    </section>
    <!-- 推荐订单 -->
<!--    <section class="pb-3">-->
<!--      <Plaza/>-->
<!--    </section>-->
<!--    <section class="pb-3">-->
<!--      <FunctionSample/>-->
<!--    </section>-->
<!--    <section class="pb-3">-->
<!--      <FunctionCommon/>-->
<!--    </section>-->
    <section class="pb-3">
      <HeroSection/>
    </section>
    <section class="pb-3">
      <ManufacturingCenter/>
    </section>
    <section class="pb-3">
      <ContactCard/>
    </section>
  </div>
</template>

<script>
import Carousel from '@/view/pages/wx/business/mainUtils/WechatCarousel';
import FunctionNav from '@/view/pages/wx/business/mainUtils/FunctionNav';
// import Plaza from '@/view/pages/wx/business/mainUtils/Plaza.vue';
// import FunctionSample from '@/view/pages/wx/business/mainUtils/FunctionSample.vue';
// import FunctionCommon from '@/view/pages/wx/business/mainUtils/FunctionCommon.vue';
import NoticeBar from '@/view/pages/wx/business/mainUtils/NoticeBar.vue';
import HeroSection from '@/view/pages/wx/business/mainUtils/HeroSection.vue';
import ManufacturingCenter from '@/view/pages/wx/business/mainUtils/ManufacturingCenter.vue';
import ContactCard from '@/view/pages/wx/business/mainUtils/ContactCard.vue';
export default {
  name: 'business',
  components: {
    Carousel,
    FunctionNav,
    // Plaza,
    // FunctionSample,
    // FunctionCommon,
    NoticeBar,
    HeroSection,
    ManufacturingCenter,
    ContactCard
  },
  data() {
    return {
      carouselSlides: [
        {
          src: process.env.BASE_URL + 'media/carousel/1.jpg',
          title: '领德集团-招商合作',
          description: '打造线路板创新生态圈，引领产业升级新方向'
        },
        {
          src: process.env.BASE_URL + 'media/carousel/2.jpg',
          title: '江西龙南生产基地',
          description: '配备智能化生产线、自动化仓储及全流程质量管控体系'
        },
        {
          src: process.env.BASE_URL + 'media/carousel/3.jpg',
          title: 'PCB免费打样',
          description: '急速出货，质量保证，优质板材'
        }
      ],
      customerList: [
        {
          src: process.env.BASE_URL + 'media/customer/pa1.png'
        },
        {
          src: process.env.BASE_URL + 'media/customer/pa2.png'
        },
        {
          src: process.env.BASE_URL + 'media/customer/pa3.png'
        },
        {
          src: process.env.BASE_URL + 'media/customer/pa5.png'
        },
        {
          src: process.env.BASE_URL + 'media/customer/pa6.png'
        },
        {
          src: process.env.BASE_URL + 'media/customer/pa7.png'
        },
      ]
    }
  },
  mounted() {
  },
  beforeDestroy() {
  }
}
</script>
