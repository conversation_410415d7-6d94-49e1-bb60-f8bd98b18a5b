<template>
  <div>
    <template v-for="(item, index) in reportList">
      <div class="p-1 border-bottom" :key="item.recordId" v-if="index < 100">
        <div class="d-flex justify-content-between align-items-center">
          <div class="text-truncate text-primary" style="width: 20rem;">
            <span v-if="item.flag == 1" class="badge badge-secondary rounded-xl mr-1">日</span>
            <span v-if="item.flag == 2" class="badge badge-warning rounded-xl mr-1">周</span>
            <span v-if="item.flag == 3" class="badge badge-success rounded-xl mr-1">月</span>
            <span class="pr-1">R-{{item.recordId}}</span>
            ({{item.name}})
            <span v-if="item.flag == 1 && item.day">{{item.day}}日</span>
            <span v-if="item.flag == 2 && item.day">{{(item.day).substring(0, 4)}}第{{(item.day).substring(4, 6)}}周</span>
            <span v-if="item.flag == 3 && item.day">{{(item.day).substring(0, 4)}}第{{(item.day).substring(4, 6)}}月</span>
          </div>
          <button class="btn btn-sm btn-outline-primary px-2 py-1" v-on:click="showReportDetails(item)">
            进入
          </button>
        </div>
        <div class="pt-2 text-truncate">工作成效：{{item.content}}</div>
        <div class="pt-2 text-truncate">总结心得：{{item.contentTwo}}</div>
        <div class="pt-2 text-truncate">计划内容：{{item.contentThree}}</div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: "shareReport",
  props: {
    reportList: Array
  },
  methods: {
    showReportDetails:function(item){
      this.$router.push({path: '/report/reportDetails', query: {reportId: item.recordId, inFlag: '2'}});
    },
  }
}
</script>