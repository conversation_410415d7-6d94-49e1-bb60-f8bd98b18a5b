<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">原材料报价</h3>
    </div>
    <div class="card">
      <div class="card-body p-3">
        <div class="input-group input-group-solid">
          <span class="pl-3 font-weight-bolder">公司：</span>
          <select class="form-control form-control-sm" v-model="companyId" v-on:change="loadCustomerList">
            <template v-for="item in companyList">
              <option :key="item.recordId" :value="item.recordId">
                {{item.name}}
              </option>
            </template>
          </select>
        </div>
        <div class="input-group input-group-solid mt-2">
          <span class="pl-3 font-weight-bolder">客户：</span>
          <select class="form-control form-control-sm" v-model="cusCompanyId" v-on:change="queryList">
            <template v-for="item in cusCompanyList">
              <option :key="item.recordId" :value="item.recordId">
                {{item.name}}
              </option>
            </template>
          </select>
        </div>
        <div class="input-group input-group-solid mt-2">
          <span class="pl-3 pr-3 font-weight-bolder"><i class="flaticon2-search-1 icon-md"></i></span>
          <input class="form-control form-control-sm" placeholder="请输入要搜索的产品" v-model="serchMsg" v-on:blur="queryList"/>
        </div>
        <div class="border-bottom pt-3 pb-3">
          <div class="d-flex justify-content-end" v-if="companyId">
            <button class="btn btn-outline-primary px-3 py-1" v-on:click="confimStock">确认</button>
          </div>
        </div>
        <template v-for="item in productList">
          <div class="pt-2 pb-2 border-bottom" :key="item.recordId">
            <div class="d-flex justify-content-between align-items-center font-weight-bolder">
              <div v-if="!cusCompanyId || cusCompanyId ==''">YL{{item.recordId}}</div>
              <div v-else>{{item.no}}</div>
              <button class="btn btn-outline-success px-3 py-1" v-on:click="stockRecord(item.recordId,item.materialKind,item.erpId)">维护历史</button>
            </div>
            <div v-if="!cusCompanyId || cusCompanyId ==''">{{item.specifications}}</div>
            <div v-else>{{item.name}}&nbsp;{{item.specification}}</div>
            <div class="pt-1 d-flex justify-content-between align-items-center">
              <div>当前库存:<span class="pl-3">{{item.stocks == null?0:item.stocks}}</span></div>
              <div>
                <div class="input-group align-items-center">
                  <div class="input-group-prepend mr-3">调整至</div>
                  <div class="input-group-prepend">
                    <input type="text" class="form-control form-control-sm" v-model="item.updateStocks" v-on:blur="valStocks(item)" style="width: 60px;"/>
                  </div>
                </div>
              </div>
            </div>
            <div class="pt-1 d-flex justify-content-between align-items-center">
              <div>当前单价:<span class="pl-3">{{item.price == null?0:item.price}}</span></div>
              <div>
                <div class="input-group align-items-center">
                  <div class="input-group-prepend mr-3">调整至</div>
                  <div class="input-group-prepend">
                    <input type="text" class="form-control form-control-sm" v-model="item.updatePrice" v-on:blur="valStocks(item)" style="width: 60px;"/>
                  </div>
                </div>
              </div>
            </div>
            <div class="pt-1 d-flex justify-content-between align-items-center">
              <div>生产周期:<span class="pl-3">{{item.leadTime == null?0:item.leadTime}}</span></div>
              <div>
                <div class="input-group align-items-center">
                  <div class="input-group-prepend mr-3">调整至</div>
                  <div class="input-group-prepend">
                    <input type="text" class="form-control form-control-sm" v-model="item.updateLeadTime" v-on:blur="valStocks(item)" style="width: 60px;"/>
                  </div>
                </div>
              </div>
            </div>
            <div class="d-flex align-items-center pr-3">
              <template v-for="detail in item.deailList">
                <div class="pr-3" :key="detail.recordId">
                  {{detail.name}}：{{detail.dictValue ? detail.dictValue : detail.value}}
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div style="height: 100px;"></div>
    <b-modal ref="confimStock" title="库存维护确认" hide-footer>
      <div>
        <template v-for="item in productList">
          <div :key="item.recordId">
            <div v-if="item.updateStocks != item.stocks || item.updatePrice != item.price || item.updateLeadTime != item.leadTime">
              <div>{{item.no}}</div>
              <div class="d-flex align-items-center">
                <div>{{item.name}}</div>
                <div class="pl-3">{{item.specification}}</div>
              </div>
              <div class="row">
                <div class="col-4">库存：{{item.stocks == null ?0:Number(item.stocks)}}</div>
                <div class="col-4">调整后：{{item.updateStocks == null ? 0:Number(item.updateStocks)}}</div>
                <div class="col-4 text-danger">变更：{{(item.updateStocks == null ? 0:Number(item.updateStocks)) - (item.stocks == null ? 0 : Number(item.stocks))}}</div>
              </div>
              <div class="row">
                <div class="col-4">价格：{{item.price == null ?0:Number(item.price)}}</div>
                <div class="col-4">调整后：{{item.updatePrice == null ? 0:Number(item.updatePrice)}}</div>
                <div class="col-4 text-danger">变更：{{(item.updatePrice == null ? 0:Number(item.updatePrice))-(item.price == null ?0:Number(item.price))}}</div>
              </div>
              <div class="row">
                <div class="col-4">周期：{{item.leadTime == null ?0:Number(item.leadTime)}}</div>
                <div class="col-4">调整后：{{item.updateLeadTime == null ? 0:Number(item.updateLeadTime)}}</div>
                <div class="col-4 text-danger">变更：{{(item.updateLeadTime == null ? 0:Number(item.updateLeadTime))-(item.leadTime == null ?0:Number(item.leadTime))}}</div>
              </div>
            </div>
          </div>
        </template>
        <div class="d-flex justify-content-end align-items-center pt-7">
          <button type="button" class="btn btn-light-primary font-weight-bold ml-3" v-on:click="runConfimStock">执行</button>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
export default {
name: "rawMaterialQuotation",
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.loadMyCompanyList();
    }else {
      alert("请重新进入公众号");
    }
  },
  created:function(){
    this.scroll();
  },
  destroyed () {
    window.removeEventListener('scroll',this.scrollEvent,false);
  },
  beforeRouteLeave(to, from, next){
    window.removeEventListener('scroll',this.scrollEvent,false);
    next();
  },
  activated(){
    this.scroll();
  },
  data(){
    return {
      companyList: [],
      companyId: {},
      productList: [],
      serchMsg: "",
      pageNo: 0,
      pageSize: 7,
      isLoading: false,
      isMore: true,
      cusCompanyList: [],
      cusCompanyId: null,
      erpId: null,
      adjustList: [],
      userMsg: {},
    }
  },
  methods:{
    queryList(){
      this.pageNo = 0;
      this.isLoading = false;
      this.getProductList();
    },
    scroll(){
      window.addEventListener('scroll', this.scrollEvent);
    },
    scrollEvent(){
      const top = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop;
      const bottomOfWindow = document.body.scrollHeight - top - window.innerHeight <= 100;
      if (bottomOfWindow && this.isMore && !this.isLoading){
        this.isLoading = true;
        this.pageNo = this.pageNo + 1;
        this.getProductList();
      }
    },
    loadMyCompanyList: function () {
      this.companyList = [];
      const query = {};
      query.userId = this.userMsg.recordId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/loadMyCompanyList", query).then(result => {
        if (result.data && result.data.length > 0){
          this.companyList = result.data;
          if (!this.companyId || isNaN(this.companyId)){
            this.companyId = result.data[0].recordId;
          }
          this.loadCustomerList();
        }else {
          alert("你没有添加企业，请先添加企业");
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    loadCustomerList: function () {
      this.cusCompanyList = [];
      let company = {};
      company.recordId = this.companyId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/loadCustomerList", company).then(result => {
        if (result.data && result.data.length > 0){
          this.cusCompanyList = result.data;
          this.cusCompanyId = this.cusCompanyList[0].recordId;
          this.erpId = this.cusCompanyList[0].erpCompanyId;
          this.queryList();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    getProductList: function () {
      if (this.pageNo == 0) {
        this.productList = [];
      }
      const product = {};
      if (!this.companyId){
        alert("请选择企业");
      }
      product.supplierId = this.companyId;
      product.customerId = this.cusCompanyId;
      product.erpId = this.erpId;
      product.serchMsg = this.serchMsg;
      product.pageNo = this.pageNo * this.pageSize;
      product.pageSize = this.pageSize;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/getCustomerProductList", product).then(result => {
        if (this.pageNo > 0) {
          for (let i=0;i<result.data.length;i++){
            this.productList.push(result.data[i]);
          }
        }else {
          this.productList = result.data;
        }
        for(let produce of this.productList)
        {
          produce.stocks = produce.stocks ? produce.stocks : 0;
          produce.price = produce.price ? produce.price : 0;
          produce.leadTime = produce.leadTime ? produce.leadTime : 0;
          produce.updateStocks = produce.stocks;
          produce.updatePrice = produce.price;
          produce.updateLeadTime = produce.leadTime;
        }
        this.isLoading = false;
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    confimStock: function () {
      let list = [];
      for (let i=0;i<this.productList.length;i++){
        let product = this.productList[i];
        if ((product.updateStocks != product.stocks || product.updatePrice != product.price || product.updateLeadTime != product.leadTime)){
          const record = {};
          record.productId =product.recordId;
          record.initStocks = product.stocks;
          record.stocks = product.updateStocks == null ? 0 : Number(product.updateStocks);

          record.initPrice = product.price;
          record.price = product.updatePrice == null ? 0 : Number(product.updatePrice);

          record.initLeadTime = product.leadTime;
          record.leadTime = product.updateLeadTime == null ? 0 : Number(product.updateLeadTime);
          record.erpId = this.erpId;
          record.icloudCompanyId = this.companyId;
          record.materialKind = product.materialKind;
          list.push(record);
        }
      }
      if(!list || list.length == 0)
      {
        alert('无调整内容，不需要确认');
        return;
      }
      this.adjustList = list;
      this.$refs['confimStock'].show();
    },
    runConfimStock: function () {
      const productObj = {};
      productObj.customerId = this.cusCompanyId;
      productObj.list = this.adjustList;
      productObj.userId = this.userMsg.recordId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/adjustStocks", productObj).then(result => {
        if (result.data)
        {
          alert('调整成功');
          this.$refs['confimStock'].hide();
          this.getProductList();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    valStocks: function (item) {
      let flag = false;
      if (!item.updateStocks || isNaN(item.updateStocks)){
        flag = true;
        alert("请填写数字");
      } else if (item.updateStocks < 0) {
        flag = true;
        alert("库存必须大于0");
      }
      if (flag) {
        item.updateStocks = item.stocks;
        for (let i=0;i<this.productList.length;i++){
          if (this.productList[i].recordId == item.recordId){
            this.$set(this.productList, i, item);
            break;
          }
        }
      }
      flag = false;
      if (!item.updatePrice || isNaN(item.updatePrice)){
        flag = true;
        alert("请填写数字");
      } else if (item.updatePrice < 0) {
        flag = true;
        alert("单价必须大于0");
      }
      if (flag) {
        item.updatePrice = item.price;
        for (let i=0;i<this.productList.length;i++){
          if (this.productList[i].recordId == item.recordId){
            this.$set(this.productList,i,item);
            break;
          }
        }
      }
      if (!item.updateLeadTime || isNaN(item.updateLeadTime)){
        flag = true;
        alert("请填写数字");
      } else if (item.updateLeadTime < 0) {
        flag = true;
        alert("交货周期必须大于0");
      }
      if (flag) {
        item.updateLeadTime = item.leadTime;
        for (let i=0;i<this.productList.length;i++){
          if (this.productList[i].recordId == item.recordId){
            this.$set(this.productList,i,item);
            break;
          }
        }
      }
    },
    stockRecord: function (id,materialKind,erpId) {
      this.$router.push({path: "/b/mainQuotation/rawMaterialQuotation/sotck_history",
        query: {
          recordId: id,
          companyId: this.companyId,
          erpId: erpId
        }
      });
    },
  }
}
</script>