<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">
        录入设备
      </h3>
    </div>
    <div class="card card-custom gutter-b">
      <div class="card-body d-flex flex-column p-3">
        <div class="row border-bottom pb-2">
          <div class="col-12 text-right">
            <button class="btn btn-light-primary" v-on:click="addEquipment()">录入设备</button>
          </div>
        </div>
        <div class="row border-bottom">
          <div class="col-xl-12">
            <div class="row pb-2 pt-2 align-items-center">
              <label class="text-muted pl-3">维保公司:</label>
              <div class="col">
                <select class="form-control input-icon-sm" v-model="repairCompanyIdTwo" v-on:change="initData">
                  <option v-for="item in repairCompanyList" :key="item.recordId" :value="item.recordId">
                    {{item.name}}
                  </option>
                </select>
              </div>
            </div>
            <div class="row pb-2 align-items-center">
              <label class="text-muted pl-3">物料类型:</label>
              <div class="col">
                <select class="form-control input-icon-sm" v-model="materialTypeIdTwo" v-on:change="initData">
                  <option v-for="item in materialTypesMaterial" :key="item.recordId" :value="item.recordId">
                    {{item.value}}
                  </option>
                </select>
              </div>
            </div>
            <div class="row pb-2 align-items-center">
              <label class="text-muted pl-3">设备信息:</label>
              <div class="col">
                <input type="text" class="form-control" placeholder="请输入要搜索的设备信息" v-model="searchInfo" v-on:blur="initData"/>
              </div>
            </div>
          </div>
        </div>
        <template v-for="item in maintenanceEquipmentList">
          <div class="row align-items-center pt-2 pb-2 border-bottom" :key="item.recordId">
            <div class="col-12">
              <div class="row pb-2">
                <div class="col-12">
                  物料编号 : {{item.no}}
                </div>
              </div>
              <div class="row pb-2" v-if="item.repairCompanyName">
                <div class="col-12">
                  维保公司 : {{item.repairCompanyName}}
                </div>
              </div>
              <div class="row pb-2" v-if="item.applyCompanyName">
                <div class="col-12">
                  客户公司 : {{item.applyCompanyName}}
                </div>
              </div>
              <div class="row pb-2 align-items-center">
                <div class="col-8">
                  {{item.materialType.value}} : &nbsp;<a href="#" v-on:click="addEquipment(item)">{{item.name}} &nbsp;规格 : {{item.specification}}</a>
                </div>
                <div class="col text-right font-weight-bolder text-primary">
                  <button class="btn btn-light-danger btn-sm" v-on:click="deleteEquipment(item.recordId)">删除</button>
                </div>
              </div>
              <div class="row pb-2" v-if="item.remark">
                <div class="col-12">
                  备注说明 : {{item.remark}}
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <b-modal ref="addEquipmentStatic" title="设备管理" hide-footer>
      <div>
        <div class="row pt-3 pb-3">
          <div class="col-12">
            <label><span style="color: red;">*</span>维保公司:</label>
            <select class="form-control input-icon-sm" v-model="repairCompanyId">
              <option v-for="item in repairCompanyList" :key="item.recordId" :value="item.recordId">
                {{item.name}}
              </option>
            </select>
          </div>
        </div>
        <div class="row pt-3 pb-3">
          <div class="col-12">
            <label>客户公司:</label>
            <select class="form-control input-icon-sm" v-model="applyCompanyId">
              <option v-for="item in applyCompanyList" :key="item.recordId" :value="item.recordId">
                {{item.name}}
              </option>
            </select>
          </div>
        </div>
        <div class="row pt-3 pb-3">
          <div class="col-12">
            <label><span style="color: red;">*</span>物料类型:</label>
            <select class="form-control input-icon-sm" v-model="materialTypeId">
              <option v-for="item in materialTypesMaterial" :key="item.recordId" :value="item.recordId">
                {{item.value}}
              </option>
            </select>
          </div>
        </div>
        <div class="row pt-3 pb-3">
          <div class="col-12">
            <label><span style="color: red;">*</span>设备名称:</label>
            <input type="text" class="form-control" placeholder="请输入设备名称" v-model="maintenanceEquipment.name"/>
          </div>
        </div>
        <div class="row pt-3 pb-3">
          <div class="col-12">
            <label><span style="color: red;">*</span>设备规格:</label>
            <input type="text" class="form-control" placeholder="请输入设备规格" v-model="maintenanceEquipment.specification"/>
          </div>
        </div>
        <div class="row pt-3 pb-3">
          <div class="col-12">
            <label>备注说明:</label>
            <textarea type="textarea" class="form-control" placeholder="请输入备注说明" v-model="maintenanceEquipment.remark"/>
          </div>
        </div>
        <div class="row pt-6">
          <div class="col-12 text-right">
            <button type="button" class="btn btn-light-primary font-weight-bold" v-on:click="closeWindow()">取消</button>&nbsp;
            <button type="button" class="btn btn-light-primary font-weight-bold" v-on:click="save">保存</button>
          </div>
        </div>
      </div>
    </b-modal>

    <div class="modal fade" id="expEquipmentModal" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="expEquipmentModal" aria-hidden="true" style="overflow:auto!important;">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              导入设备
            </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <i aria-hidden="true" class="ki ki-close"></i>
            </button>
          </div>
          <div class="d-flex flex-column-fluid">
            <div class="container">

              <div class="row pt-2 pb-2">
                <div class="col-12">
                  <input type="text" class="form-control" placeholder="请输入要搜索的设备信息" v-model="searchInfo" v-on:blur="initData"/>
                </div>
              </div>
              <div class="row align-items-center pt-2 pb-2 border-bottom">
                <div class="col text-right">
                  <button class="btn btn-light-primary" v-on:click="addEquipment()">录入设备</button>
                </div>
              </div>
              <div class="pt-3">
                <div class="row align-items-center pt-2 pb-2 border-bottom" v-for="item in maintenanceEquipmentList" :key="item.recordId">
                  <div class="col-12">
                    <div class="row pt-2 font-weight-bolder text-primary" v-on:click="addEquipment(item)">
                      <div class="col-12">
                        名称：{{item.name}}
                      </div>
                    </div>
                    <div class="pt-2">
                      <div class="row">
                        <div class="col-12">
                          规格：{{item.specification}}
                        </div>
                      </div>
                    </div>
                    <div class="pt-2">
                      <div class="row">
                        <div class="col-12">
                          原因：{{item.remark}}
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-12 text-right">
                        <button class="btn btn-light-danger btn-sm" v-on:click="deleteEquipment(item.recordId)">删除</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import $ from "jquery";

export default {
name: "inputEquipment",
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.initData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return {
      maintenanceEquipment:{},
      maintenanceEquipmentList:[],
      expEquipmentList:[],
      serchMsgExport:'',
      allChecked: false,
      searchInfo:'',
      applyCompanyList:[],
      applyCompanyId:'',
      materialTypesMaterial:[],
      materialTypeId:'',
      materialTypeIdTwo:'',
      applyCompanyIdTwo:'',
      repairCompanyList:[],
      repairCompanyId:'',
      repairCompanyIdTwo:'',
      userMsg:{},
    }
  },
  methods: {
    getMaintenanceCompanyList:function()
    {
      let company = {};
      company.userId = this.userMsg.recordId;
      this.$parent.enableLoadFlag(true);
      const _this = this;
      this.$axios.fetchPost("f/wechat/repair/getMaintenanceCompanyList", company).then(result => {
        _this.applyCompanyList = result.data.applyCompanyList;
        //物料类型
        this.materialTypesMaterial = result.data.materialTypesMaterial;
        _this.repairCompanyList = result.data.repairCompanyList;
        if(_this.applyCompanyList && _this.applyCompanyList.length > 0)
        {
          _this.repairCompanyId = _this.repairCompanyList[0].recordId;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
    initData:function(){
      let query = {};
      query.searchInfo = this.searchInfo;
      query.userId = this.userMsg.recordId;
      query.repairCompanyId = this.repairCompanyIdTwo;
      query.materialTypeId = this.materialTypeIdTwo;
      query.phone = this.userMsg.phone;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/repair/getMaintenanceEquipmentList", query).then(result => {
         this.maintenanceEquipmentList = result.data;
        this.getMaintenanceCompanyList();
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
    addEquipment:function(item){
      if(item)
      {
        this.maintenanceEquipment = JSON.parse(JSON.stringify(item));
        this.materialTypeId = this.maintenanceEquipment.materialType.recordId;
        this.applyCompanyId = this.maintenanceEquipment.icloudCompanyId;
      }
      else
      {
        this.maintenanceEquipment = {};
      }
      this.getMaintenanceCompanyList();
      this.$refs['addEquipmentStatic'].show();
    },
    save:function(){
      if(!this.repairCompanyId)
      {
        alert("请选择维保公司!");
        return;
      }
      if(!this.materialTypeId)
      {
        alert("请选择物料类型!");
        return;
      }
      if(!this.maintenanceEquipment.name)
      {
        alert("请输入设备名称!");
        return;
      }
      if(!this.maintenanceEquipment.specification)
      {
        alert("请输入设备规格!");
        return;
      }
      this.maintenanceEquipment.repairCompanyId = this.repairCompanyId;
      this.maintenanceEquipment.applyCompanyId = this.applyCompanyId;
      this.maintenanceEquipment.userId = this.userMsg.recordId;
      this.maintenanceEquipment.materialTypeId = this.materialTypeId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/repair/saveEquipmentEntry", this.maintenanceEquipment).then(result => {
        if (result.data === "success"){
          alert("保存成功!");
          this.$refs['addEquipmentStatic'].hide();
          this.initData();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
    deleteEquipment: function (id) {
      const _this = this;
      const entity = {};
      entity.recordId = id;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/repair/deleteEquipment", entity).then(result => {
        if (result.data == 'success'){
          alert("删除成功");
          _this.initData();
        }else {
          alert("请刷新重试");
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
    expEquipmentModal: function (){
      this.getExpEquipmentList();
      $('#expEquipmentModal').modal();
    },
    getExpEquipmentList: function () {
      this.expEquipmentList = [];
      const material = {};
      material.specification = this.serchMsgExport;
      material.companyId = 17;
      material.pageNo = 0;
      material.pageSize = 300;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/repair/getExpEquipmentList", material).then(result => {
        this.expEquipmentList = result.data;
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
    setAllCheckedFun: function () {
      let check = false;
      if (this.allChecked){
        check = this.allChecked;
      }
      for (let i=0;i<this.expEquipmentList.length;i++){
        this.expEquipmentList[i].checked = check;
      }
    },
    expEquipment: function (){
      const list = [];
      for (let i=0;i<this.expEquipmentList.length;i++){
        if (this.expEquipmentList[i].checked){
          const pro = {};
          pro.materialId = this.expEquipmentList[i].recordId;
          pro.name = this.expEquipmentList[i].name;
          pro.specification = this.expEquipmentList[i].specification;
          pro.userId = this.user.userId;
          pro.remark = '导入';
          list.push(pro);
        }
      }
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/repair/expEquipment", list).then(result => {
        $('#expEquipmentModal').modal('hide');
        if (result.data == 'success'){
          alert("导入成功");
          this.initData();
        }else {
          alert(result.data);
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
    closeWindow()
    {
      this.$refs['addEquipmentStatic'].hide();
    },
  }
}
</script>