/**
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2020 <PERSON><PERSON><PERSON> <<EMAIL>>
 */

import between from './between';
import blank from './blank';
import callback from './callback';
import choice from './choice';
import creditCard from './creditCard';
import date from './date';
import different from './different';
import digits from './digits';
import emailAddress from './emailAddress';
import file from './file';
import greaterThan from './greaterThan';
import identical from './identical';
import integer from './integer';
import ip from './ip';
import lessThan from './lessThan';
import notEmpty from './notEmpty';
import numeric from './numeric';
import promise from './promise';
import regexp from './regexp';
import remote from './remote';
import stringCase from './stringCase';
import stringLength from './stringLength';
import uri from './uri';

// Additional validators
import base64 from './base64';
import bic from './bic';
import color from './color';
import cusip from './cusip';
import ean from './ean';
import ein from './ein';
import grid from './grid';
import hex from './hex';
import iban from './iban';
import id from './id/index';
import imei from './imei';
import imo from './imo';
import isbn from './isbn';
import isin from './isin';
import ismn from './ismn';
import issn from './issn';
import mac from './mac';
import meid from './meid';
import phone from './phone';
import rtn from './rtn';
import sedol from './sedol';
import siren from './siren';
import siret from './siret';
import step from './step';
import uuid from './uuid';
import vat from './vat/index';
import vin from './vin';
import zipCode from './zipCode';

/* tslint:disable:object-literal-sort-keys */
export default {
    between,
    blank,
    callback,
    choice,
    creditCard,
    date,
    different,
    digits,
    emailAddress,
    file,
    greaterThan,
    identical,
    integer,
    ip,
    lessThan,
    notEmpty,
    numeric,
    promise,
    regexp,
    remote,
    stringCase,
    stringLength,
    uri,
    // Additional validators
    base64,
    bic,
    color,
    cusip,
    ean,
    ein,
    grid,
    hex,
    iban,
    id,
    imei,
    imo,
    isbn,
    isin,
    ismn,
    issn,
    mac,
    meid,
    phone,
    rtn,
    sedol,
    siren,
    siret,
    step,
    uuid,
    vat,
    vin,
    zipCode,
};
/* tslint:enable:object-literal-sort-keys */
