<template>
  <div>
    <div class="mb-3">
      <div class="d-flex py-3 bg-white rounded-sm font-weight-bolder">
        <template v-for="item in tabList">
          <button :class="['buttonClass', activeTab == item.id ? 'text-primary border-bottom border-primary' : '']" :key="item.id" v-on:click="changeTab(item.id)">
            <i :class="['fa', item.iconClass, 'mr-1', activeTab == item.id ? 'text-primary' : '']"></i> {{item.name}}
          </button>
        </template>
      </div>
    </div>
    <div v-if="activeTab === '1'">
      <AUDIT></AUDIT>
    </div>
    <div v-if="activeTab === '2'">
      <DAYTHING></DAYTHING>
    </div>
    <div v-if="activeTab === '3'">
      <REPORT></REPORT>
    </div>
    <div v-if="activeTab === '4'">
      <TASK></TASK>
    </div>
    <WINDOWFLOAT/>
  </div>
</template>

<script>
import AUDIT from "@/view/pages/wx/kybsoft/work/audit/main";
import REPORT from "@/view/pages/wx/kybsoft/work/report/main";
import TASK from "@/view/pages/wx/kybsoft/work/task/main";
import DAYTHING from "@/view/pages/wx/kybsoft/work/dayThing/main";
import WINDOWFLOAT from "@/view/pages/wx/windowFloat";
export default {
  name: 'work',
  components: {
    AUDIT,
    REPORT,
    TASK,
    DAYTHING,
    WINDOWFLOAT
  },
  mounted() {
    let activeTab = activeTab ? activeTab : window.localStorage.getItem("tabId");
    this.activeTab = activeTab ? activeTab : this.activeTab;
    if (!this.activeTab){
      this.activeTab = '1';
    }
  },
  data() {
    return {
      activeTab: '',
      tabList: [
        {id: '1', name: "审批", iconClass: "fa-list-alt"}, {id: '2', name: "日程", iconClass: "fa-calendar"}, {id: '3', name: "报告", iconClass: "fa-file"}, {id: '4', name: "任务", iconClass: "fa-tasks"},
      ]
    }
  },
  methods: {
    changeTab(id) {
      window.localStorage.removeItem("audit_activeCategory");
      window.localStorage.removeItem("audit_condition");
      window.localStorage.removeItem("audit_applicationsType");
      window.localStorage.removeItem("audit_typeIds");

      window.localStorage.removeItem("dayThing_activeCategory");
      window.localStorage.removeItem("dayThing_status");
      window.localStorage.removeItem("dayThing_title");
      window.localStorage.removeItem("dayThing_categoryFlag");

      window.localStorage.removeItem("report_activeCategory");
      window.localStorage.removeItem("report_readType");
      window.localStorage.removeItem("report_title");
      window.localStorage.removeItem("report_departmentName");
      window.localStorage.removeItem("report_status");
      window.localStorage.removeItem("report_employeeId");

      window.localStorage.removeItem("task_activeCategory");
      window.localStorage.removeItem("task_title");
      window.localStorage.removeItem("task_emergencyLevel");
      window.localStorage.removeItem("task_shareIds");
      this.activeTab = id;
    },
    enableLoadFlag(flag) {
      this.$parent.enableLoadFlag(flag);
    },
  }
}
</script>

<style scoped>
.buttonClass {
  flex: 1 1 0;
  padding: 0.5rem 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
