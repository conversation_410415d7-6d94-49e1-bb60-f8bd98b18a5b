<template>
  <div>
    <div class="font-size-h5 pb-3 font-weight-bolder">企业服务</div>
    <template v-for="item in coopList">
      <div class="row pb-2 align-items-center" :key="item.name">
        <div class="col-9">
          <div class="d-flex align-items-center">
        <span :class="item.symbolStyle">
          <span class="symbol-label">
            <span :class="item.svgStyle">
              <inline-svg :src="item.svgUrl" />
            </span>
          </span>
        </span>
            <div class="d-flex flex-column">
              <div>
                <span class="text-dark-75 font-weight-bolder">{{ item.name }}</span>
              </div>
              <div class="text-muted">
                {{ item.remark }}
              </div>
            </div>
          </div>
        </div>
        <div class="col-3 text-right">
          <span class="badge badge-success">运行中</span>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
export default {
  name: "cooperation",
  data(){
    return{
      coopList: [
        {symbolStyle: "symbol  symbol-50 symbol-light-primary mr-2",svgStyle: "svg-icon svg-icon-xl svg-icon-primary",svgUrl: process.env.BASE_URL + "media/svg/icons/Communication/Add-user.svg",
          name: "我是员工", remark: "加入圈子，成为某个企业的员工", status: 1},
        {symbolStyle: "symbol  symbol-50 symbol-light-success mr-2",svgStyle: "svg-icon svg-icon-xl svg-icon-success",svgUrl: process.env.BASE_URL + "media/svg/icons/Shopping/Cart1.svg",
          name: "成为客户", remark: "专业PCB智造商和PCB产业服务商", status: 1},
        {symbolStyle: "symbol  symbol-50 symbol-light-info mr-2",svgStyle: "svg-icon svg-icon-xl svg-icon-info",svgUrl: process.env.BASE_URL + "media/svg/icons/Communication/Group.svg",
          name: "成为供应商", remark: "提供铝基板材、油墨、模具等用品", status: 1},
        {symbolStyle: "symbol  symbol-50 symbol-light-danger mr-2",svgStyle: "svg-icon svg-icon-xl svg-icon-danger",svgUrl: process.env.BASE_URL + "media/svg/icons/Shopping/Sale2.svg",
          name: "维保服务", remark: "为制造业提供专业的设备维保服务", status: 1}
      ]
    }
  },
  props: {
    name: String,
    remark: String,
    symbolStyle: String,
    svgStyle: String,
    svgUrl: String
  }
}
</script>
