<template>
  <div class="pb-1">
    <div class="p-1 jsc_card_pox3" v-if="normalRanking && normalRanking.length > 0" style="background-color: rgba(4,51,77,0.6)">
      <div class="pb-1 d-flex align-items-center">
        <div class="font-weight-bolder">{{ name }}</div>
        <div>
          <template v-for="(item, index) in showList">
            <span :key="index" :class="['ml-3', flag == item ? 'text-white' : 'text-primary']" v-on:click="setFlag(item)">{{getName(item)}}</span>
          </template>
        </div>
      </div>
      <div v-if="flag == '1'">
        <RANK :name="'面积'" :rankList="normalRanking" :showDate="showDate"></RANK>
        <RANK :name="'金额'" :rankList="custRankingM" :showDate="showDate"></RANK>
        <RANK :name="'利润'" :rankList="custRankingP" :showDate="showDate"></RANK>
      </div>
      <div v-if="flag == '2'">
        <RANK :name="'面积'" :rankList="departRankingA" :showDate="showDate"></RANK>
        <RANK :name="'金额'" :rankList="departRankingM" :showDate="showDate"></RANK>
        <RANK :name="'利润'" :rankList="departRankingP" :showDate="showDate"></RANK>
      </div>
      <div v-if="flag == '3'">
        <RANK :name="'面积'" :rankList="saleRankingA" :showDate="showDate"></RANK>
        <RANK :name="'金额'" :rankList="saleRankingM" :showDate="showDate"></RANK>
        <RANK :name="'利润'" :rankList="saleRankingP" :showDate="showDate"></RANK>
      </div>
      <div v-if="flag == '4'">
        <RANK :name="'面积'" :rankList="userRankingA" :showDate="showDate"></RANK>
        <RANK :name="'金额'" :rankList="userRankingM" :showDate="showDate"></RANK>
        <RANK :name="'利润'" :rankList="userRankingP" :showDate="showDate"></RANK>
      </div>
      <div v-if="flag == '5'">
        <RANK :rankList="normalRanking" :showDate="showDate"></RANK>
      </div>
      <div v-if="flag == '6'">
        <RANK :rankList="departRankingA" :showDate="showDate"></RANK>
      </div>
      <div v-if="flag == '7'">
        <RANK :rankList="companyRanking" :showDate="showDate"></RANK>
      </div>
      <div v-if="flag == '8'">
        <RANK :rankList="periodRanking" :showDate="showDate"></RANK>
      </div>
      <div v-if="flag == '9'">
        <RANK :rankList="normalRanking" :showDate="showDate"></RANK>
      </div>
    </div>
  </div>
</template>

<script>
import RANK from "@/view/pages/wx/report/utils/rank/rank";
export default {
  name: "pageRank",
  components: {
    RANK
  },
  props: {
    normalRanking: Array,
    custRankingM: Array,
    custRankingP: Array,
    departRankingA: Array,
    departRankingM: Array,
    departRankingP: Array,
    saleRankingA: Array,
    saleRankingM: Array,
    saleRankingP: Array,
    userRankingA: Array,
    userRankingM: Array,
    userRankingP: Array,
    companyRanking: Array,
    periodRanking: Array,
    showList: Array,
    name: String,
    showDate: String,
    showFlag: String,
  },
  data() {
    return{
      flag: ''
    }
  },
  mounted() {
    this.flag = this.showFlag;
  },
  methods: {
    setFlag(val) {
      this.flag = val;
    },
    getName(val){
      if (val == '1'){
        return "客户";
      }else if (val == '2'){
        return "部门";
      }else if (val == '3'){
        return "业务员";
      }else if (val == '4'){
        return "跟单员";
      }else if (val == '5'){
        return "客户";
      }else if (val == '6'){
        return "部门";
      }else if (val == '7'){
        return "公司";
      }else if (val == '8'){
        return "账期";
      }else if (val == '9'){
        return "供应商";
      }
    }
  }
}
</script>