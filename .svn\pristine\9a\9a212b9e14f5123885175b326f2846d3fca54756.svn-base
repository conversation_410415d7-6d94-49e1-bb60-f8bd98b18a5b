<template>
  <div>
    <div class="d-flex justify-content-between mb-5">
      <h3 class="text-white">交易中心</h3>
      <div class="d-flex justify-content-end pt-1 mr-3">
        <i class="fa fa-compress text-white" v-on:click="runShowFlag('1')" v-if="showFlag == '2'"></i>
        <i class="fa fa-expand text-white" v-on:click="runShowFlag('2')" v-else></i>
      </div>
    </div>
    <!-- 筛选栏 -->
    <div class="card mb-1">
      <div class="card-body p-3">
        <div class="input-group input-group-solid align-items-center" v-on:click="openFilter">
          <span class="pl-3 font-weight-bold">筛选：</span>
          <span class="form-control form-control-sm"></span>
        </div>
      </div>
    </div>
    <!-- 订单列表 -->
    <template v-for="order in productList">
      <div :key="order.id" class="bg-white mb-2 pl-3 pr-3 pt-1 pb-1 rounded-sm">
        <div class="d-flex justify-content-between align-items-center">
          <div class="font-size-lg font-weight-bolder">{{order.customerPo}}</div>
          <div v-if="showFlag == '2'">
            <div v-if="userMsg.employeeId && order.type !== 1">
              <span class="badge badge-info" v-if="order.type == 1 || order.type == 2">销售订单</span>
              <span class="badge badge-primary" v-if="order.type == 3 || order.type == 4">采购订单</span>
            </div>
            <div v-else>
              <span class="badge badge-info" v-if="order.type == 3 || order.type == 4">销售订单</span>
              <span class="badge badge-primary" v-if="order.type == 1 || order.type == 2">采购订单</span>
            </div>
          </div>
        </div>
        <div class="border-bottom pb-1" v-if="order.specification">{{order.specification}}</div>
        <div class="pt-1 d-flex justify-content-between" v-if="showFlag == '1'">
          <div>
            <div v-if="userMsg.employeeId && order.type !== 1">
              <span v-if="order.type == 1 || order.type == 2">需求方：{{order.purchaser}}</span>
              <span v-if="order.type == 3 || order.type == 4">供应方：{{order.supplier}}</span>
            </div>
            <div v-else>
              <span v-if="order.type == 3 || order.type == 4">需求方：{{order.purchaser}}</span>
              <span v-if="order.type == 1 || order.type == 2">供应方：{{order.supplier}}</span>
            </div>
          </div>
          <div v-if="Number(order.showStatus) == 3"><span class="badge badge-success px-1 py-1">生产中</span></div>
          <div v-else-if="Number(order.showStatus) == 1"><span class="badge badge-success px-1 py-1">已审核</span></div>
          <div v-else-if="Number(order.showStatus) == 2 && order.payWayValue"><span class="badge badge-success px-1 py-1">{{order.payWayValue == 1 ? '现金' : '月结'}}</span></div>
          <div v-else-if="Number(order.showStatus) == 4"><span class="badge badge-success px-1 py-1">已发货</span></div>
          <div v-else><span class="badge badge-secondary px-1 py-1">待确认</span></div>
        </div>
        <div v-else>
          <div v-if="userMsg.employeeId && order.type !== 1">
            <div :class="['d-flex align-items-center pt-1', (order.type == 1 || order.type == 2 ? 'text-primary font-weight-bolder' : '')]">供应方：{{order.supplier}}</div>
            <div :class="['d-flex align-items-center pt-1', (order.type == 3 || order.type == 4 ? 'text-primary font-weight-bolder' : '')]">需求方：{{order.purchaser}}</div>
          </div>
          <div v-else>
            <div :class="['d-flex align-items-center pt-1', (order.type == 3 || order.type == 4 ? 'text-primary font-weight-bolder' : '')]">供应方：{{order.supplier}}</div>
            <div :class="['d-flex align-items-center pt-1', (order.type == 1 || order.type == 2 ? 'text-primary font-weight-bolder' : '')]">需求方：{{order.purchaser}}</div>
          </div>
          <div class="d-flex align-items-center text-muted">{{order.craftStr}}</div>
          <div class="d-flex justify-content-between align-items-center font-weight-bolder text-primary pt-1 pb-1">
            <div>数量：{{order.quantity}}{{order.unint}}</div>
            <div>{{order.currencyTypeValue =='RMB' ? '￥' : order.currencyTypeValue}}{{bigNumberTransform(order.amount)}}</div>
          </div>
          <div class="d-flex justify-content-between align-items-center pl-3 pr-3">
            <div class="d-flex flex-column align-items-center">
              <div :class="['d-flex justify-content-center align-items-center', 'bg-success']" style="border-radius: 50%;width: 1.8rem;height: 1.8rem;">
                <i class="fa fa-check-circle text-white fa-sm"></i>
              </div>
              <span class="text-center text-muted">创建</span>
            </div>
            <div class="d-flex flex-column align-items-center">
              <div :class="['d-flex justify-content-center align-items-center',
             (order.showStatus && Number(order.showStatus) > 0) ? (Number(order.showStatus) == 1 ? 'bg-primary' : 'bg-success') : 'bg-secondary']"
                   style="border-radius: 50%;width: 1.8rem;height: 1.8rem;">
                <i class="fa fa-check-circle text-white fa-sm"></i>
              </div>
              <span class="text-center text-muted ">已审核</span>
            </div>
            <div class="d-flex flex-column align-items-center">
              <div :class="['d-flex justify-content-center align-items-center',
            (order.showStatus && Number(order.showStatus) > 1) ? (Number(order.showStatus) == 2 ? 'bg-primary' : 'bg-success') : 'bg-secondary']"
                   style="border-radius: 50%;width: 1.8rem;height: 1.8rem;">
                <i class="fa fa-check-circle text-white fa-sm"></i>
              </div>
              <span class="text-center text-muted text-sm">{{order.payWayValue == 1 ? '现金' : '月结'}}</span>
            </div>
            <div class="d-flex flex-column align-items-center" v-on:click="showProduce(order)">
              <div :class="['d-flex justify-content-center align-items-center',
            (order.showStatus && Number(order.showStatus) > 2) ? (Number(order.showStatus) == 3 ? 'bg-primary' : 'bg-success') : 'bg-secondary']"
                   style="border-radius: 50%;width: 1.8rem;height: 1.8rem;">
                <i :class="['fa fa-cog text-white fa-sm', (order.showStatus && Number(order.showStatus) > 2) ? 'fa-spin' : '']"></i>
              </div>
              <span class="text-center text-muted text-sm">生产</span>
            </div>
            <div class="d-flex flex-column align-items-center" v-on:click="showDeliveryDetail(order)">
              <div :class="['d-flex justify-content-center align-items-center',
            (order.showStatus && Number(order.showStatus) > 3) ? 'bg-success' : 'bg-secondary']"
                   style="border-radius: 50%;width: 1.8rem;height: 1.8rem;">
                <i :class="['fa fa-clock text-white fa-sm', (order.showStatus && Number(order.showStatus) > 3) ? 'fa-spin' : '']"></i>
              </div>
              <span class="text-center text-muted text-sm">发货</span>
            </div>
          </div>
        </div>
        <div v-if="order.produceRecords && order.produceRecords.length > 0 && Number(order.showStatus) == 3">
          <div class="text-danger" v-if="order.showProduce">{{ order.showProduce }}</div>
          <div class="text-success" v-else>已全部入库，等待发货</div>
        </div>
        <div class="alert-secondary ml-1 mr-1 mt-1 pl-3 pr-3" v-if="order.deliveryDeitalFlag">
          <div class="timeline timeline-2" v-for="detail in order.ddList" :key="detail.id">
            <div class="timeline-bar"></div>
            <div class="timeline-item">
              <div class="timeline-badge bg-success"></div>
              <div class="timeline-content">
                <div class="d-flex align-items-center justify-content-between">
                  <div v-if="detail.deliveryNo">单号:{{detail.deliveryNo}}</div>
                  <div>数量:{{detail.quantity}}</div>
                </div>
                <div>{{detail.createdDate}}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="alert-secondary ml-1 mr-1 mt-1 pl-3 pr-3" v-if="order.produceFlag">
          <div class="timeline timeline-2" v-for="record in order.produceRecords" :key="record.id">
            <div class="timeline-bar"></div>
            <div class="timeline-item">
              <div class="timeline-badge bg-success"></div>
              <div class="timeline-content">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="mr-3">{{record.processName}}</span>
                  <span class="text-muted text-right">
                    <span v-if="record.handOverQtyPcsT">{{ record.handOverQtyPcsT }}</span>
                    <span v-else>已结束</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row pt-3" v-if="showFlag == '2'">
          <div class="col-12">
            <div class="progress" style="height: 3px;">
              <div class="progress-bar" role="progressbar" :style="'width:' + order.showStatus * 25 + '%'" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>
        </div>
        <div class="row pt-1 text-muted">
          <div class="col-6">开始：{{order.startDateStr}}</div>
          <div class="col-6 text-right">预计：{{order.endDateStr}}</div>
        </div>
      </div>
    </template>
    <div style="height: 100px;"></div>
    <b-modal ref="filter" hide-footer hide-header>
      <div>
        <div class="font-size-lg font-weight-bolder border-bottom pb-3">交易筛选</div>
        <div class="pt-3">
          <div class="text-muted">类型</div>
          <select class="form-control" v-model="serchType">
            <option value="">全部</option>
            <option value="1">销售</option>
            <option value="2">采购</option>
          </select>
        </div>
        <div class="pt-3">
          <div class="text-muted">状态</div>
          <select class="form-control" v-model="serchStatus">
            <option value="">全部</option>
            <option value="1">创建</option>
            <option value="2">审核</option>
            <option value="3">生产</option>
            <option value="4">送货</option>
          </select>
        </div>
        <div class="pt-3">
          <div class="text-muted">需求方</div>
          <input class="form-control" placeholder="请输入需求方名称" v-model="serchCustName">
        </div>
        <div class="pt-3">
          <div class="text-muted">供应方</div>
          <input class="form-control" placeholder="请输入供应方名称" v-model="serchSupName">
        </div>
        <div class="pt-3">
          <div class="text-muted">订单号或规格型号</div>
          <input class="form-control" placeholder="请输入单号或者规格型号" v-model="serchMsg">
        </div>
        <div class="d-flex justify-content-end pt-3">
          <button class="btn btn-outline-secondary" v-on:click="cancelFiter">清空</button>
          <button class="btn btn-outline-primary ml-3" v-on:click="applyFiter">应用</button>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
export default {
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  created:function(){
    this.scroll();
  },
  destroyed () {
    window.removeEventListener('scroll',this.scrollEvent,false);
  },
  activated(){
    this.scroll();
  },
  data() {
    return {
      pageNo:0,
      pageSize: 10,
      isLoading:false,
      productList:[],
      serchMsg: "",
      serchType: "",
      serchStatus: "",
      serchCustName: "",
      serchSupName: "",
      showFlag: "2"
    }
  },
  methods: {
    runShowFlag(flag) {
      this.showFlag = flag;
    },
    queryList(){
      this.pageNo = 0;
      this.isLoading = false;
      this.productList = [];
      this.loadData();
    },
    scroll(){
      window.addEventListener('scroll',this.scrollEvent);
    },
    scrollEvent(){
      const top = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop;
      const bottomOfWindow = document.body.scrollHeight - top - window.innerHeight <= 100;
      if (bottomOfWindow && !this.isLoading){
        this.isLoading = true;
        this.pageNo = this.pageNo + 1;
        this.loadData();
      }
    },
    showProduce(obj) {
      this.$set(obj,'produceFlag',!obj.produceFlag);
    },
    showDeliveryDetail(obj) {
      this.$set(obj,'deliveryDeitalFlag',!obj.deliveryDeitalFlag);
    },
    openFilter() {
      this.$refs['filter'].show();
    },
    applyFiter() {
      this.$refs['filter'].hide();
      this.queryList();
    },
    cancelFiter() {
      this.$refs['filter'].hide();
    },
    loadData()
    {
      const query = {};
      query.userId = this.userMsg.recordId;
      query.employeeLevel = this.userMsg.employeeLevel;
      query.phone = this.userMsg.phone;
      query.pageNo = this.pageNo * this.pageSize;
      query.pageSize = this.pageSize;
      query.serchMsg = this.serchMsg;
      if (!this.userMsg.employeeId) {
        // 无员工，对应模式要反过来
        if (this.serchType == "1"){
          query.serchType = "2";
        }else if (this.serchType == "2"){
          query.serchType = "1";
        }
      }else {
        query.empId = this.userMsg.employeeId;
      }
      query.serchStatus = this.serchStatus;
      query.serchCustName = this.serchCustName;
      query.serchSupName = this.serchSupName;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/getTradingCenterData",query).then(result => {
        if(result && result.data && result.data.tradingCenterList){
          if (this.pageNo > 0) {
            for (let i=0;i<result.data.tradingCenterList.length;i++){
              this.productList.push(result.data.tradingCenterList[i]);
            }
          }else {
            this.productList = result.data.tradingCenterList;
          }
        }
        this.isLoading = false;
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    bigNumberTransform(value)
    {
      if(!value || value == '')
      {
        value = 0;
      }
      let type = 1;
      if(value < 0)
      {
        type = 2;
        value = value * -1;
      }
      value = Number(Number(value).toFixed(2));
      const newValue = ['', '', '']
      let fr = 1000
      let num = 3
      let text1 = ''
      let fm = 1
      while (value / fr >= 1) {
        fr *= 10
        num += 1
      }
      if (num <= 4) {
        newValue[0] = value;
      } else if (num <= 8) { // 万
        text1 = parseInt(num - 4) / 3 > 1 ? '千万' : '万'
        fm = text1 === '万' ? 10000 : 10000000
        if (value % fm === 0) {
          newValue[0] = parseInt(value / fm) + ''
        } else {
          newValue[0] = parseFloat(value / fm).toFixed(2) + ''
        }
        newValue[1] = text1
      } else if (num <= 16) { // 亿
        text1 = (num - 8) / 3 > 1 ? '千亿' : '亿'
        text1 = (num - 8) / 4 > 1 ? '万亿' : text1
        text1 = (num - 8) / 7 > 1 ? '千万亿' : text1
        fm = 1
        if (text1 === '亿') {
          fm = 100000000
        } else if (text1 === '千亿') {
          fm = 100000000000
        } else if (text1 === '万亿') {
          fm = 1000000000000
        } else if (text1 === '千万亿') {
          fm = 1000000000000000
        }
        if (value % fm === 0) {
          newValue[0] = parseInt(value / fm) + ''
        } else {
          newValue[0] = parseFloat(value / fm).toFixed(2) + ''
        }
        newValue[1] = text1
      }
      if (value < 1000) {
        newValue[0] = value + ''
        newValue[1] = ''
      }
      if(type == 2)
      {
        return '-'+newValue.join('');
      }
      else
      {
        return newValue.join('');
      }
    }
  }
}
</script>
