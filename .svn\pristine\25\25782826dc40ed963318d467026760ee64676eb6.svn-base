<template>
<!--发布招标-->
  <div>
    <div v-if="showAuditData && showAuditData.length > 0">
      <div class="pt-2">
        <div class="d-flex justify-content-between align-items-center pb-1 border-bottom">
          <div class="font-weight-bolder">招标单</div>
          <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlag">{{ flag && flag == '1' ? '收起' : '展开' }}</button>
        </div>
        <div v-if="flag == '1'">
          <div v-for="(biddingFlags, index) in showAuditData" :key="index">
            <div class="d-flex justify-content-between pt-1">
              <div>编号：{{biddingFlags.no}}</div>
              <div>预期到货：{{biddingFlags.deliveryDateStr}}</div>
            </div>
            <div class="d-flex pt-1">
              <div>标题：{{biddingFlags.title}}</div>
            </div>
            <div class="d-flex justify-content-between pt-1">
              <div>招标目标：{{biddingFlags.materialName}}</div>
              <div>招标数量：{{biddingFlags.quantity}}</div>
            </div>
            <div class="d-flex pt-1" style="word-break: break-all;">
              <div>规格参数：{{biddingFlags.specification}}</div>
            </div>
            <div class="d-flex pt-1" style="word-break: break-all;">
              <div>备注说明：{{biddingFlags.content}}</div>
            </div>
            <div class="d-flex pt-1">
              <div>招标时间：{{biddingFlags.startTimeStr}}</div>
            </div>
            <div class="d-flex pt-1">
              <div>招标地点：{{biddingFlags.address}}</div>
            </div>
            <div class="pt-2" v-if="biddingFlags.supplierList && biddingFlags.supplierList.length > 0">
              <div class="d-flex text-left align-items-center pb-3 border-bottom">
                <div class="font-weight-bolder">参与供应商列表</div>
              </div>
              <div class="border-bottom" v-for="item in biddingFlags.supplierList" :key="item.recordId">
                <div class="d-flex justify-content-between pt-1">
                  <div>编号：{{item.no}}</div>
                  <div>简称：{{item.shortName ? item.shortName : item.name}}</div>
                </div>
                <div class="d-flex justify-content-between pt-1">
                  <div>联系人：{{item.bizPerson ? item.bizPerson : item.legalPhone}}</div>
                  <div>手机号码：{{item.wxPhone}}</div>
                </div>
                <div class="d-flex pt-1">
                  <div>
                    <span>等级：<span class="font-weight-bold">{{item.supplierLevelValue}}</span>&nbsp;</span>
                    <span>行业：<span class="font-weight-bold">{{item.industry}}</span>&nbsp;</span>
                    <span>产品：<span class="font-weight-bold">{{item.supplierTypeValue}}</span>&nbsp;</span>
                    <span>分类：<span class="font-weight-bold">{{item.classificationValue}}</span>&nbsp;</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
name: "publishTender",
  props: {
    showAuditData: [Object, Array],
  },
  data() {
    return{
      flag: "2"
    }
  },
  methods: {
    changeFlag() {
      if (this.flag == "1"){
        this.flag = "2";
      }else {
        this.flag = "1";
      }
    }
  }
}
</script>