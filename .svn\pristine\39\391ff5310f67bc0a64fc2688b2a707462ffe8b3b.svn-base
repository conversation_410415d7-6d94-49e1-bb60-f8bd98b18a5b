<template>
  <div>
    <div class="d-flex justify-content-between align-items-center mb-5">
      <h3 class="text-white">{{ number == 1 ? "下级绩效" : "下下级绩效"}}</h3>
      <span class="badge badge-success" v-if="commitNo == '1'">已提交</span>
      <span class="badge badge-secondary" v-else>待评估</span>
    </div>
    <div class="bg-white rounded-sm p-3 mb-3">
      <div class="d-flex justify-content-between align-items-center font-weight-bolder">
        <div>{{employeeMessage.position}}：{{employeeMessage.name}}</div>
        <div>考核月份：{{monthly}}</div>
      </div>
      <div class="pt-1">
        部门：{{employeeMessage.departmentName}}
      </div>
    </div>
    <div class="bg-white p-1 rounded-sm mb-3">
      <template v-for="item in markSorceTreeList">
        <div class="p-1 border d-flex justify-content-between" :key="item.recordId" v-if="item.markLevel == 2">
          <div style="width: 10%;">
            <span class="font-weight-bolder">{{ item.sorceType }}</span><span v-if="item.score" class="font-weight-bolder text-primary">({{item.score}}%)</span>
            <div v-if="item.content" class="text-muted "><span>指标：</span>{{item.content}}</div>
            <div v-if="item.title" class="text-muted"><span>奖罚：</span>{{item.title}}</div>
          </div>
          <div class="border-left border-right" style="width: 80%;">
            <div v-if="!item.markSorceList && item.scoreType">
              <div class="checkbox-inline" v-if="item.scoreType == 1 || item.scoreType == 3">
                <template v-for="row in item.gradingOptionsList">
                  <label class="checkbox" :key="row.recordId">
                    <input type="checkbox" :value="row.checked" v-model="row.checked" v-on:change="handleCheckboxChange(row, item)" :disabled="commitNo == '1'"/>
                    <span></span>
                    &nbsp;{{ row.content }}
                    <a v-if="item.chooseIdT == row.recordId" class="text-danger">(上级)</a>
                    ({{ row.score }})
                  </label>
                </template>
              </div>
              <div v-if="item.scoreType == 2 || item.scoreType == 3">
                <span class="text-danger">上级 {{row.manualScoreT ? row.manualScoreT : 0}}</span>
                <input class="form-control" v-model="item.scoreT" v-on:change="calculateTotalValue" :disabled="commitNo == '1'" oninput="this.value = this.value.replace(/^0*(\d+(\.\d*)?)$|^(\.(\d*))?$|^[^\d.].*$|(\d+\.\d*\.|\d*\.0*)/g, '$1').replace(/[^\d.]/g, '');">
              </div>
            </div>
            <template v-for="row in item.markSorceList">
              <div :key="row.recordId" v-if="row.markLevel == 3">
                <div class="border-bottom alert-primary">
                  <span class="font-weight-bolder">{{ row.sorceType }}</span><span v-if="row.score" class="font-weight-bolder text-primary">({{row.score}}%)</span>
                  <div>
                    <div v-if="row.content"><span class="text-muted">指标：</span>{{row.content}}</div>
                    <div v-if="row.title"><span class="text-muted">奖罚：</span>{{row.title}}</div>
                  </div>
                </div>
                <div>
                  <template v-for="mark in row.markSorceList">
                    <div :key="mark.recordId" v-if="mark.markLevel == 4">
                      <div class="border-bottom alert-primary">
                        {{ mark.sorceType }}<span v-if="mark.score" class="font-weight-bolder text-primary">({{mark.score}}%)</span>
                        <div>
                          <div v-if="mark.content"><span class="text-muted">指标：</span>{{mark.content}}</div>
                          <div v-if="mark.title"><span class="text-muted">奖罚：</span>{{mark.title}}</div>
                        </div>
                      </div>
                      <div class="p-2">
                        <div v-if="!mark.markSorceList && mark.scoreType">
                          <div class="row" v-if="mark.scoreType == 1 || mark.scoreType == 3">
                            <template v-for="grade in mark.gradingOptionsList">
                              <div class="col-6 pb-2" :key="grade.recordId">
                                <label class="checkbox">
                                  <input type="checkbox" class="form-control" :value="grade.checked" v-model="grade.checked" v-on:change="handleCheckboxChange(grade, mark)" :disabled="commitNo == '1'"/>
                                  <span></span>
                                  &nbsp;{{ grade.content }} ({{ grade.score }})<a v-if="mark.chooseIdT == grade.recordId" class="text-danger">(上级)</a>
                                </label>
                              </div>
                            </template>
                          </div>
                          <div v-if="mark.scoreType == 2 || mark.scoreType == 3">
                            <span class="text-danger">上级 {{row.manualScoreT ? row.manualScoreT : 0}}</span>
                            <input class="form-control form-control-sm" v-model="mark.scoreT" v-on:change="calculateTotalValue" :disabled="commitNo == '1'" oninput="this.value = this.value.replace(/^0*(\d+(\.\d*)?)$|^(\.(\d*))?$|^[^\d.].*$|(\d+\.\d*\.|\d*\.0*)/g, '$1').replace(/[^\d.]/g, '');">
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                  <div class="p-2" v-if="!row.markSorceList && row.scoreType">
                    <div class="row" v-if="row.scoreType == 1 || row.scoreType == 3">
                      <template v-for="option in row.gradingOptionsList">
                        <div class="col-6 pb-2" :key="option.recordId">
                          <label class="checkbox">
                            <input type="checkbox" class="form-control" :value="option.checked" v-model="option.checked" v-on:change="handleCheckboxChange(option, row)" :disabled="commitNo == '1'"/>
                            <span></span>
                            &nbsp;{{ option.content }}
                            <a v-if="row.chooseIdT == option.recordId" class="text-danger">(上级)</a>
                            ({{ option.score }})
                          </label>
                        </div>
                      </template>
                    </div>
                    <div v-if="row.scoreType == 2 || row.scoreType == 3">
                      <span class="text-danger">上级 {{row.manualScoreT ? row.manualScoreT : 0}}</span>
                      <input class="form-control form-control-sm" v-model="row.scoreT" v-on:change="calculateTotalValue" :disabled="commitNo == '1'" oninput="this.value = this.value.replace(/^0*(\d+(\.\d*)?)$|^(\.(\d*))?$|^[^\d.].*$|(\d+\.\d*\.|\d*\.0*)/g, '$1').replace(/[^\d.]/g, '');">
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div style="width: 10%;">
            {{item.scoreValue}}
          </div>
        </div>
      </template>
      <div class="d-flex justify-content-end font-weight-bolder pt-2 pb-2">
        汇总分数：{{totalValue}}
      </div>
    </div>
    <div class="bg-white rounded-sm p-3 mb-3">
      <div>
        <div class="text-muted pb-1">工作成效(自己)</div>
        <div style="word-break: break-all">
          {{oaDailyContent.content}}
        </div>
      </div>
      <div class="pt-3">
        <div class="text-muted pb-1">总结心得(自己)</div>
        <div style="word-break: break-all">
          {{oaDailyContent.contentTwo}}
        </div>
      </div>
      <div class="pt-3">
        <div class="text-muted pb-1">计划内容(自己)</div>
        <div style="word-break: break-all">
          {{oaDailyContent.contentThree}}
        </div>
      </div>
    </div>
    <div class="bg-white rounded-sm p-3 mb-3">
      <div>
        <div class="text-muted pb-1">本期完成情况(上级)</div>
        <div style="word-break: break-all" v-if="number == 2">
          {{bonusTaskObj.finshTextT}}
        </div>
        <div v-if="number == 1">
          <textarea class="form-control" rows="3" v-model="bonusTaskObj.finshTextT" :disabled="commitNo == '1'"></textarea>
        </div>
      </div>
      <div class="pt-3">
        <div class="text-muted pb-1">改进建议(上级)</div>
        <div style="word-break: break-all" v-if="number == 2">
          {{bonusTaskObj.adivceTextT}}
        </div>
        <div v-if="number == 1">
          <textarea class="form-control" rows="3" v-model="bonusTaskObj.adivceTextT" :disabled="commitNo == '1'"></textarea>
        </div>
      </div>
    </div>
    <div class="bg-white rounded-sm p-3 mb-3" v-if="number == 2">
      <div>
        <div class="text-muted pb-1">本期完成情况(上上级)</div>
        <div>
          <textarea class="form-control" rows="3" v-model="bonusTaskObj.finshText" :disabled="commitNo == '1'"></textarea>
        </div>
      </div>
      <div class="pt-3">
        <div class="text-muted pb-1">改进建议(上上级)</div>
        <div>
          <textarea class="form-control" rows="3" v-model="bonusTaskObj.adivceText" :disabled="commitNo == '1'"></textarea>
        </div>
      </div>
    </div>
    <div class="bg-white rounded-sm p-3">
      <div class="font-weight-bolder">附件清单</div>
      <div v-if="fileList && fileList.length > 0">
        <template v-for="(item,index) in fileList">
          <div :key="index">
            <div class="d-flex justify-content-between align-items-center pt-2 pb-2 border-bottom">
              <div style="max-width: 220px;word-break: break-all;" class="text-truncate">{{item.name}}</div>
              <div>
                <button class="btn btn-outline-primary px-2 py-1 ml-3" v-on:click="showFile(item)">预览</button>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="bottom-actions" v-if="commitNo == '0'">
      <button class="btn-primary" v-on:click="saveMarkScore(1)">保存</button>
      <button class="btn-danger" v-on:click="saveMarkScore(2)">提交</button>
    </div>
    <div style="height: 100px;"></div>
  </div>
</template>

<script>
import {showPreview} from "@/assets/js/utils/wechatImgUtils";
import wx from "weixin-js-sdk";

export default {
name: "performanceDetails",
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.employeeId && !this.empId && !this.monthly){
        alert("请刷新重试");
        return;
      }
      this.empId = this.$route.query.empId;
      this.monthly = this.$route.query.monthly;
      this.superId = this.$route.query.superId;
      this.superiorId = this.$route.query.superiorId;
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data () {
    return {
      userMsg: {},
      empId: "",
      monthly: "",
      superId: "",
      superiorId: "",
      employeeMessage: {},
      number: "",
      markSorceTreeList: [],
      totalValue: "",
      commitNo: '0',
      commitFlag: "",
      oaDailyContent: {},
      bonusTaskObj: {},
      fileList: [],
      qty: 0,
      clickFlag: false,
      list: [],
    }
  },
  methods:{
    loadData(){
      if (this.userMsg.employeeId == this.superId) {
        this.number = 1;
      }else if (this.userMsg.employeeId == this.superiorId) {
        this.number = 2;
      }
      if (this.number){
        this.loadPerformaceData();
      }else {
        alert("你没有评估权限");
      }
    },
    loadPerformaceData() {
      this.employeeMessage = {};
      let query = {};
      query.recordId = this.empId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("hr/markSource/getSelectMarkNameMessage", query).then(result => {
        if (result && result.data) {
          this.employeeMessage = result.data; //考核员工信息
        }
        if (this.employeeMessage && this.employeeMessage.recordId){
          this.getMarkSorceTreeList();
        }else {
          alert("获取不到待评估的员工，请刷新重试");
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {
        console.log(err);
      })
    },
    //获取评分管理架构树
    getMarkSorceTreeList() {
      this.bonusTaskObj = {};
      this.fileList = [];
      this.oaDailyContent = {};
      this.markSorceTreeList = [];
      const query = {};
      query.structureId = this.employeeMessage.groupId;
      query.scorerId = this.userMsg.employeeId; //上级或上上级
      query.monthly = this.monthly;
      query.employeeId = this.employeeMessage.recordId;
      query.positionName = this.employeeMessage.position;
      this.$axios.fetchPost("hr/recruit/getMarkSorceTreeListTwo", query).then(result => {
        if (result && result.data) {
          this.markSorceTreeList = result.data.markSorceTreeList; //绩效配置
          this.oaDailyContent = result.data.personModalContent;
          if (this.oaDailyContent && this.oaDailyContent.attachList) {
            this.fileList = this.oaDailyContent.attachList;
          }
          let bonusTaskList = result.data.hr_bonusTask_total;
          if (bonusTaskList && bonusTaskList.length > 0){
            this.bonusTaskObj = bonusTaskList[0]; //汇总数据
          }
        }
        this.getMarkScore(); //获取评分项
      }).catch(err => {
        console.log(err);
      })
    },
    //获取对应的评分项
    getMarkScore() {
      this.commitNo = '0';
      this.commitFlag = "";
      if (this.markSorceTreeList && this.markSorceTreeList.length > 0) {
        for(let item of this.markSorceTreeList) {
          this.markScoreReflection(item,1);
        }
      }
      this.calculateTotalValue();
    },
    markScoreReflection(item,parentScore) {
      if (!item.markSorceList || item.markSorceList.length == 0) {
        if (item.scoreType && (item.scoreRecord || item.scoreRecordT)){
          let chooseId = "";
          // eslint-disable-next-line no-unused-vars
          let scoreRecord = "";
          if (this.number === 1) {
            chooseId = item.chooseIdT;
            scoreRecord = item.scoreRecordT;
            this.commitNo = item.statusT ? item.statusT : '0';
            this.commitFlag = "1";
          }else if (this.number === 2){
            chooseId = item.chooseId;
            scoreRecord = item.scoreRecord;
            this.commitNo = item.status ? item.status : '0';
            this.commitFlag = "1";
          }
          if (item.gradingOptionsList && item.gradingOptionsList.length > 0 && item.scoreType == "1") {
            for(let option of item.gradingOptionsList) {
              if (chooseId === option.recordId) {
                option.checked = true;
              }else{
                option.checked = false;
              }
            }
          }
          if (this.number === 1) {
            item.scoreT = item.manualScoreT;
          }else if (this.number === 2){
            item.scoreT = item.manualScoreTwo;
          }
        }
      }else{
        if (this.number === 1) {
          item.scoreT = item.manualScoreT;
        }else if (this.number === 2){
          item.scoreT = item.manualScoreTwo;
        }
        for (let row of item.markSorceList) {
          this.markScoreReflection(row,Number(parentScore * (item.score ? item.score : 1)/(item.score? 100 : 1)));
        }
      }
    },
    calculateTotalValue() {
      let totalValue = 0;
      if(this.markSorceTreeList && this.markSorceTreeList.length > 0) {
        for (const item of this.markSorceTreeList) {
          if(item.markLevel == 2) {
            this.qty = 0;
            this.calculateTotalValueComput(item,1);
            item.scoreValue = Number(this.qty?this.qty : 0).toFixed(2);
            totalValue += Number(item.scoreValue);
          }
        }
      }
      totalValue = Number(totalValue).toFixed(2);
      this.totalValue = totalValue;
    },
    calculateTotalValueComput(item,parentScore)
    {
      if(!item.markSorceList || item.markSorceList.length == 0) {
        if(item.gradingOptionsList && item.gradingOptionsList.length > 0 && item.scoreType == "1"){
          if (!item.score){
            alert("请确定权重是否已经录入正确");
            return;
          }
          const selectedOption = item.gradingOptionsList.find(option => option.checked === true);
          if (selectedOption) {
            if (parentScore == 1)
            {
              this.qty += (Number(selectedOption.score) * Number(item.score)/100);
            }else{
              this.qty += (Number(selectedOption.score) * Number(parentScore) * Number(item.score)/100);
            }
          }
        } else if(item.scoreT && item.scoreType == "2")
        {
          if (!item.score){
            alert("请确定权重是否已经录入正确");
            return;
          }
          if (parentScore == 1)
          {
            this.qty += (Number(item.scoreT) * Number(item.score)/100);
          }else{
            this.qty += (Number(item.scoreT) * Number(parentScore) * Number(item.score)/100);
          }
        }
      }
      else
      {
        for (let row of item.markSorceList)
        {
          this.calculateTotalValueComput(row,Number(parentScore * (item.score ? item.score : 1)/(item.score? 100 : 1)));
        }
      }
    },
    handleCheckboxChange(selectedOption, item) {
      let optionsList = item.gradingOptionsList;
      if (optionsList && item.score){
        for (let i= 0;i< optionsList.length;i++) {
          let option = optionsList[i]
          if (option.recordId == selectedOption.recordId && option.checked) {
            option.checked = true;
          }else {
            option.checked = false;
          }
          this.$set(optionsList,i,option);
          this.calculateTotalValue();
        }
        this.$set(item,"gradingOptionsList",optionsList);
      }else {
        alert("请确定选项和权重是否已经录入正确");
      }
    },
    saveMarkScore(num){
      if(this.clickFlag) {
        alert("请勿多次点击！");
        return;
      }
      const query = {};
      this.list = [];
      query.scorerId = this.userMsg.employeeId; //评分人Id
      if (this.number === 1)
      {
        if (!this.bonusTaskObj.finshTextT)
        {
          alert("请填写本期完成情况！");
          return;
        }
        if (!this.bonusTaskObj.adivceTextT)
        {
          alert("请填写改进建议！");
          return;
        }
        query.employeeId = this.employeeMessage.recordId; //被评分人Id
        query.monthly = this.monthly;
      }else if (this.number === 2)
      {
        if (!this.bonusTaskObj.finshText)
        {
          alert("请填写本期完成情况！");
          return;
        }
        if (!this.bonusTaskObj.adivceText)
        {
          alert("请填写改进建议！");
          return;
        }
        query.employeeId = this.employeeMessage.recordId; //被评分Id
        query.monthly = this.monthly;
      }
      query.type = this.number;
      if (this.markSorceTreeList && this.markSorceTreeList.length >0)
      {
        for(let item of this.markSorceTreeList)
        {
          if (item.markLevel == 2)
          {
            this.getSaveMarkScoreList(item,1);
          }
        }
      }
      if(!this.list || this.list.length === 0)
      {
        alert("请选择评分项再进行保存!");
        return;
      }
      let saveList = [];
      for(let numobj of this.list)
      {
        var obj = JSON.parse(JSON.stringify(query));
        obj.chooseId = numobj.chooseId;
        obj.score = numobj.score;
        obj.recordId = numobj.recordId
        obj.allId = numobj.allId;
        obj.manualScore = numobj.manualScore;
        saveList.push(obj);
      }
      const queryOne = {};
      queryOne.scoreTableList = saveList;
      queryOne.numNo = num;
      if (this.number === 1)
      {
        queryOne.employeeId = this.employeeMessage.recordId;
        queryOne.monthly = this.monthly;
        queryOne.structureId = this.employeeMessage.groupId;
        queryOne.positionName = this.employeeMessage.position;
      }else if (this.number === 2)
      {
        queryOne.employeeId = this.employeeMessage.recordId;
        queryOne.monthly = this.monthly;
        queryOne.structureId = this.employeeMessage.groupId;
        queryOne.positionName = this.employeeMessage.position;
      }
      queryOne.scorerId = query.scorerId; //评分人Id
      queryOne.type = query.type;
      queryOne.finshText = this.bonusTaskObj.finshText;
      queryOne.adivceText = this.bonusTaskObj.adivceText;
      queryOne.finshTextT = this.bonusTaskObj.finshTextT;
      queryOne.adivceTextT = this.bonusTaskObj.adivceTextT;
      // queryOne.empName = this.LoginMessageStruct.name;
      this.clickFlag = true;
      this.$axios.fetchPost("hr/markSource/saveMarkScore", queryOne).then(result => {
        if(result.data === "success" && num === 1)
        {
          alert("保存成功！");
        }else if (result.data === "success" && num === 2)
        {
          alert("提交成功!");
        }
        this.loadData();
        this.clickFlag = false;
      }).catch(err => {console.log(err);})
    },
    getSaveMarkScoreList(item,parentScore)
    {
      if ((!item.markSorceList || item.markSorceList.length == 0))
      {
        if(item.gradingOptionsList && item.gradingOptionsList.length > 0 && item.scoreType == "1")
        {
          for(let option of item.gradingOptionsList)
          {
            if (option.checked)
            {
              const messageObj = {};
              messageObj.chooseId = option.recordId;
              if (parentScore == 1)
              {
                messageObj.score = (Number(option.score) * Number(item.score) / 100).toFixed(2);
              }else{
                messageObj.score = (Number(option.score) * Number(item.score) * parentScore / 100).toFixed(2);
              }
              messageObj.allId = option.correspondingScoreId;
              this.list.push(messageObj);
            }
          }
        }else if (item.scoreT && item.scoreType == "2")
        {
          const messageObj = {};
          if(parentScore == 1)
          {
            messageObj.score = (Number(item.scoreT) * Number(item.score) / 100).toFixed(2);
          }else{
            messageObj.score = (Number(item.scoreT) * Number(item.score) * parentScore / 100).toFixed(2);
          }
          messageObj.manualScore = Number(item.scoreT);
          messageObj.allId = item.recordId;
          this.list.push(messageObj);
        }
      }else{
        for (let row of item.markSorceList) {
          this.getSaveMarkScoreList(row,Number(parentScore * (item.score ? item.score : 1) /(item.score? 100 : 1)));
        }
      }
    },
    showFile(file){
      const url = file.downloadUrl;
      if (!url) {
        alert("文件已失效，请刷新重试");
        return;
      }
      const flag = showPreview(file);
      if (flag){
        if (flag == '1'){
          this.localIds.push(url);
          this.preview(url);
        }else if (flag == '2'){
          window.location.href = url;
        }else if (flag == '3'){
          window.location.href = "https://view.officeapps.live.com/op/view.aspx?src=" + encodeURIComponent(url);
        }
      }
    },
    preview:function(item){
      wx.previewImage({
        current: item, // 当前显示图片的http链接
        urls: this.localIds // 需要预览的图片http链接列表
      });
    },
  },
}
</script>
<style>
.bottom-actions {
  position: fixed;
  bottom: 0rem;
  left: 0;
  right: 0;
  background-color: white;
  padding: 0.75rem 1rem;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 1rem;
}

.bottom-actions button {
  flex: 1;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
  border: none;
}

.btn-secondary {
  background-color: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-danger {
  background-color: #dc2c2c;
  color: white;
  border: none;
}
</style>