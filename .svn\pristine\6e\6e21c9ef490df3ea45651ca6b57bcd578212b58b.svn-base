<template>
  <div class="float-ball"
       :style="{left: ballPosition.x + 'px', top: ballPosition.y + 'px'}"
       @mousedown="startDrag"
       @touchstart="startDrag"
       :class="{dragging: isDragging}">
    <!-- 这里可以放置你的豆包图标 -->
    <img :src="loaderLogo" alt="回到顶部" class="w-100 h-100 rounded-xl" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      isDragging: false,
      offset: { x: 0, y: 0 },
      ballPosition: { x: 0, y: 0 },
      initialPosition: { x: 20, y: 100 }, // 初始位置（右侧和底部距离）
      clickTimer: null,
      clickCount: 0
    };
  },
  computed: {
    viewportWidth() {
      return window.innerWidth;
    },
    viewportHeight() {
      return window.innerHeight;
    },
    ballSize() {
      return 60; // 小球尺寸
    },
    loaderLogo() {
      return process.env.BASE_URL + "media/logos/kyb.jpg";
    },
  },
  mounted() {
    // 初始化位置
    this.updatePosition();

    // 添加事件监听
    document.addEventListener('mousemove', this.handleDrag);
    document.addEventListener('touchmove', this.handleDrag, { passive: false });
    document.addEventListener('mouseup', this.stopDrag);
    document.addEventListener('touchend', this.stopDrag);
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 移除事件监听
    document.removeEventListener('mousemove', this.handleDrag);
    document.removeEventListener('touchmove', this.handleDrag);
    document.removeEventListener('mouseup', this.stopDrag);
    document.removeEventListener('touchend', this.stopDrag);
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    router_page() {
      // 如果是拖拽操作，不处理点击
      // if (this.isDragging) return;
      // 增加点击计数
      this.clickCount++;

      // 设置定时器检测双击
      if (this.clickCount === 1) {
        this.clickTimer = setTimeout(() => {
          // 单击逻辑（如果需要，可以在这里添加单击响应）
          this.clickCount = 0;
        }, 300); // 300ms内没有第二次点击，则认为是单击
      } else if (this.clickCount === 2) {
        // 双击逻辑
        clearTimeout(this.clickTimer);
        this.$router.push("/worktable");
        this.clickCount = 0;
      }
    },
    // 开始拖拽
    startDrag(e) {
      e.preventDefault();

      const clientX = e.type.includes('mouse') ? e.clientX : e.touches[0].clientX;
      const clientY = e.type.includes('mouse') ? e.clientY : e.touches[0].clientY;

      this.offset.x = clientX - this.ballPosition.x;
      this.offset.y = clientY - this.ballPosition.y;
      this.isDragging = true;
      this.router_page();
    },

    // 处理拖拽
    handleDrag(e) {
      if (!this.isDragging) return;
      e.preventDefault();

      const clientX = e.type.includes('mouse') ? e.clientX : e.touches[0].clientX;
      const clientY = e.type.includes('mouse') ? e.clientY : e.touches[0].clientY;

      // 计算新位置
      let newX = clientX - this.offset.x;
      let newY = clientY - this.offset.y;

      // 边界限制
      newX = Math.max(0, Math.min(newX, this.viewportWidth - this.ballSize));
      newY = Math.max(0, Math.min(newY, this.viewportHeight - this.ballSize));

      this.ballPosition = { x: newX, y: newY };
    },

    // 停止拖拽
    stopDrag() {
      this.isDragging = false;
    },

    // 窗口大小变化处理
    handleResize() {
      // 保持小球在相对位置
      this.updatePosition();
    },

    // 更新位置计算
    updatePosition() {
      this.ballPosition = {
        x: this.viewportWidth - this.ballSize - this.initialPosition.x,
        y: this.viewportHeight - this.ballSize - this.initialPosition.y
      };
    },

    // 回到顶部
    goToTop() {
      if (!this.isDragging) {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }
    }
  }
};
</script>

<style scoped>
.float-ball {
  position: fixed;
  width: 46px;
  height: 46px;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  cursor: move;
  transition: transform 0.2s ease;
  z-index: 9999;
  overflow: hidden;
}

.float-ball.dragging {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  transition: none;
}

.float-ball:hover:not(.dragging) {
  transform: scale(1.05);
}
</style>