<template>
  <div>
    <template v-for="(item, index) in taskList">
      <div class="p-1 border-bottom" :key="item.recordId" v-on:click="showTaskDetails(item)" v-if="index < 100">
        <div class="d-flex justify-content-between align-items-center">
          <div class="text-truncate text-primary" style="width: 20rem;">
            {{item.title}}
            <span class="text-danger pr-1" v-if="item.finishDay > 0">&nbsp;逾期{{item.finishDay}}天</span>
          </div>
          <button class="btn btn-sm btn-outline-primary px-2 py-1" v-on:click="showTaskDetails(item)">
            进入
          </button>
        </div>
        <div class="text-truncate">负责人：{{item.principals.name}}&nbsp;&nbsp;&nbsp;参与人：{{item.participates}}</div>
        <div class="pt-1 text-truncate">{{item.content}}</div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: "shareTask",
  props: {
    taskList: Array
  },
  methods: {
    showTaskDetails:function(item){
      this.$router.push({path: '/task/taskDetails', query: {taskId: item.recordId, inFlag: '2'}});
    },
  }
}
</script>