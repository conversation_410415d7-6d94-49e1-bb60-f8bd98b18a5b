<template>
  <div class="bg-white rounded-sm p-3">
    <div class="font-weight-bolder">过数记录</div>
    <div class="text-muted pt-3">厂编</div>
    <div class="row">
      <div class="col-12">
        <input class="form-control form-control-sm" v-model="craftNo" v-on:change="loadData">
      </div>
    </div>
    <div class="pt-3 text-muted">工序</div>
    <div class="row">
      <div class="col-12">
        <input class="form-control form-control-sm" v-model="countItemName" v-on:change="loadData">
      </div>
    </div>
    <div class="pt-7 text-muted">汇总</div>
    <div class="row pb-3 border-bottom">
      <div class="col-4">
        <div class="font-weight-bolder">{{allPcs?allPcs.toFixed(2):0}}&nbsp;PCS</div>
      </div>
      <div class="col-4 text-center">
        <div class="font-weight-bolder">{{allArea?allArea.toFixed(2):0}}&nbsp;㎡</div>
      </div>
      <div class="col-4 text-right">
        <div class="font-weight-bolder">{{countList.length}}&nbsp;款</div>
      </div>
    </div>
    <template v-for="item in countList">
      <div class="row align-items-center pt-3 pb-3 border-bottom" :key="item.recordId">
        <div class="col-12" style="word-break: break-all;">
          <div>
            {{item.no}}&nbsp;&nbsp;&nbsp;{{item.noticeNos}}&nbsp;&nbsp;&nbsp;{{item.customerModel}}
          </div>
          <div class="pt-1">
            <span class="text-danger font-weight-bolder">{{item.craftNo}}({{item.process.category}})</span>&nbsp;&nbsp;&nbsp;
            {{item.area}}㎡&nbsp;&nbsp;&nbsp;{{item.takeOverQtyPcsT}}PCS&nbsp;&nbsp;&nbsp;
            报废:{{Number(item.handOverQtyPcsT) - Number(item.takeOverQtyPcsT)}}PCS
          </div>
          <div class="row pt-1 text-primary font-weight-bolder">
            <div class="col-6">
              接板人:<span v-if="item.createdBy && item.createdBy.userName">{{item.createdBy.userName}}</span>
            </div>
            <div class="col-6 text-right">
              交板人:<span v-if="item.lastUpdBy && item.lastUpdBy.userName">{{item.lastUpdBy.userName}}</span>
            </div>
          </div>
          <div class="row pt-1">
            <div class="col-6">
              接板时间:{{item.takeOverTimeStr}}
            </div>
            <div class="col-6 text-right">
              交板时间:{{item.handOverTimeStr}}
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { getErpList, getCompany } from '@/assets/js/utils/userAuth';
export default {
  name: "countList",
  mounted() {
    this.userMsg = {};
    this.erpList = [];
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.erpList = getErpList(sysUser);
      this.company = getCompany(sysUser);
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return{
      company: {},
      countList: [],
      craftNo: "",
      countItemName: "",
      allArea: 0,
      allPcs: 0,
    }
  },
  // 方法编写
  methods:{
    loadData:function()
    {
      this.allArea = 0;
      this.allPcs = 0;
      this.countList = [];
      if(this.company && this.company.userId){
        // 加载当天的过数记录
        this.$parent.enableLoadFlag(true);
        const pro = {};
        pro.company = this.company;
        pro.userId = this.company.userId;
        pro.craftNo = this.craftNo;
        pro.cetageName = this.countItemName;
        this.$axios.fetchPost("f/wechat/produce/getCountList", pro).then(result => {
          if(result.data){
            this.countList = result.data;
            if(this.countList && this.countList.length > 0){
              for(let i=0;i<this.countList.length;i++){
                this.allArea = Number(this.allArea) + Number(this.countList[i].area);
                this.allPcs = Number(this.allPcs) + Number(this.countList[i].handOverQtyPcsT);
              }
            }
          }else {
            alert("获取信息失败");
          }
          this.$parent.enableLoadFlag(false);
        }).catch(err => {console.log(err);});
      }
    }
  }
}
</script>
