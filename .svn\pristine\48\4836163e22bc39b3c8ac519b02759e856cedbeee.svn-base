<template>
  <div>
    <canvas id="myCanvas" style="position:absolute; top:0; left:0;pointer-events: none;"></canvas>
  </div>
</template>

<script>
export default {
  name: "animation",
  mounted:function(){
    this.backgroundAnimation();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCanvas);
  },
  methods: {
    backgroundAnimation: function () {
      // 背景动画
      const canvas = document.getElementById('myCanvas');
      const ctx = canvas.getContext('2d');
      const particles = [];
      const numParticles = 70;

      // 动态地更新 canvas 的尺寸
      function resizeCanvas() {
        canvas.width = document.documentElement.clientWidth;
        canvas.height = document.documentElement.scrollHeight;
      }

      // 初始化 canvas 的尺寸
      resizeCanvas();

      // 在窗口尺寸改变的时候，更新 canvas 的尺寸
      window.addEventListener('resize', resizeCanvas);

      function Particle() {
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.vx = 0.4; //速度
        this.vy = 0.4;
      }

      Particle.prototype.update = function() {
        this.x += this.vx;
        this.y += this.vy;

        if (this.x < 0 || this.x > canvas.width) {
          this.vx = -this.vx;
        }

        if (this.y < 0 || this.y > canvas.height) {
          this.vy = -this.vy;
        }
      };

      Particle.prototype.draw = function() {
        ctx.fillStyle = '#fff';
        ctx.beginPath();
        //                      粒子大小
        ctx.arc(this.x, this.y, 0.8, 0, 2 * Math.PI, false);
        ctx.fill();
      };

      for (var i = 0; i < numParticles; i++) {
        particles.push(new Particle());
      }

      function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        for (var i = 0; i < particles.length; i++) {
          particles[i].update();
          particles[i].draw();
        }
        requestAnimationFrame(animate);
      }
      animate();
    }
  }
}
</script>
