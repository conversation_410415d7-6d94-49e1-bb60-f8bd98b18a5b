<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">头像管理</h3>
    </div>
    <div class="card card-custom">
      <div class="card-body p-3">
        <div class="d-flex align-items-center pb-2 border-bottom">
          <div class="image-container-common"><img :src="userMsg.downloadUrl"/></div>
          <div class="font-weight-bolder pl-3">你好，{{userMsg.userName}}</div>
        </div>
        <div class="border-bottom pt-2 pb-2">
          <div class="font-weight-bolder">默认头像选择</div>
          <div class="row">
            <template v-for="item in imgList">
              <div class="col-3 p-3 pl-7 pr-7" :key="item.id">
                <div class="image-container-common"><img :src="item.imgUrl" v-on:click="selDefaultImg(item.id)"/></div>
              </div>
            </template>
          </div>
        </div>
        <div class="pt-3 pb-3">
          <div class="font-weight-bolder">自定义头像</div>
          <div class="pt-3">
            <button class="btn btn-sm btn-outline-primary" v-on:click="openFileSelector">电脑上传</button>
            <input id="file" type="file" ref="fileInput" v-on:change="uploadFile" v-show="false">
          </div>
        </div>
      </div>
    </div>
    <div style="height: 100px;"></div>
  </div>
</template>
<script>
export default {
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
    }else {
      alert("请重新进入公众号");
    }
  },
  data() {
    return {
      userMsg:{},
      imgList: [
        {id: 1, imgUrl: process.env.BASE_URL + "media/headDefaultImg/default_beauty.jpg"},
        {id: 2, imgUrl: process.env.BASE_URL + "media/headDefaultImg/default_colleagues.jpg"},
        {id: 3, imgUrl: process.env.BASE_URL + "media/headDefaultImg/default_family.jpg"},
        {id: 4, imgUrl: process.env.BASE_URL + "media/headDefaultImg/default_friend.jpg"},
        {id: 5, imgUrl: process.env.BASE_URL + "media/headDefaultImg/default_handsome.jpg"},
        {id: 6, imgUrl: process.env.BASE_URL + "media/headDefaultImg/default_trade.jpg"}
      ]
    };
  },
  methods: {
    openFileSelector() {
      this.$refs.fileInput.click();
    },
    selDefaultImg(item) {
      if (this.userMsg && this.userMsg.recordId) {
        this.userMsg.defaultImg = item;
        this.$axios.fetchPost('hr/user/selDefaultImg', this.userMsg).then((data) => {
          if (data && data.data) {
            if (data.data === 'success') {
              alert("更换成功");
            }else {
              alert("更换失败");
            }
          }
        }).catch(err => {console.log(err)})
      }
    },
    uploadFile(file) {
      if (this.userMsg && this.userMsg.recordId) {
        const f = file.target.files[0];
        const type = ['image/jpg', 'image/jpeg', 'image/png'];
        if (f && type.indexOf(f.type) >= 0) {
          // 通过form表单进行提交
          const param = new FormData();
          param.append('file', f);
          param.append('id', this.userMsg.recordId);
          this.$axios.formPost('hr/user/uploadFile', param).then((data) => {
            if (data.data) {
              if (data.data === 'have') {
                alert("该文件名已经上传!");
              } else if (data.data === 'false') {
                alert("上传失败，请刷新重试!");
              } else if (data.data === 'success') {
                alert("上传成功!");
                this.$router.push("/my");
              }
            }
          }).catch(err => {console.log(err);})
        } else {
          alert("只能上传jpg/jpeg/png后缀名的文件!");
        }
      }
    },
  }
}
</script>
