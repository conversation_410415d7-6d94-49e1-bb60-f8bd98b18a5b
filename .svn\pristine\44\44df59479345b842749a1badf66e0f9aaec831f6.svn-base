<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">通知公告</h3>
    </div>
    <div class="bg-white rounded-sm p-3 mb-3">
      <div class="input-group input-group-solid">
        <span class="pl-3 font-weight-bolder">筛选：</span>
        <input class="form-control form-control-sm text-truncate" v-model="searchContent" v-on:change="loadData"/>
      </div>
    </div>
    <section v-if="noticeList && noticeList.length > 0">
      <template v-for="row in noticeList">
        <div class="bg-white rounded-sm p-3 mb-3" :key="row.recordId">
          <div class="font-weight-bolder" style="word-break: break-all">
            {{row.title}}
          </div>
          <div class="pt-1 text-muted d-flex justify-content-between align-items-center">
            <div>发布人：{{row.name}}</div>
            <div>发布时间：{{row.createdDate}}</div>
          </div>
          <div class="pt-1" style="word-break: break-all">
            {{row.noticeMsg}}
          </div>
        </div>
      </template>
    </section>
    <div style="height: 100px;"></div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      userMsg: {},
      noticeList: [],
      searchContent: "",
    }
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.employeeId){
        return;
      }
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  methods: {
    loadData() {
      this.noticeList = [];
      const query = {};
      query.searchContent = this.searchContent;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/getCurrNotice",query).then(result => {
        if (result && result.data && result.data.length > 0) {
          this.noticeList = result.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    }
  }
}
</script>