<template>
  <!--客诉单作废审批-->
  <div>
    <div v-if="audit && audit.auditResult && audit.auditResult.trim()">
      <template v-for="(item, index) in audit.auditResult.split(';')">
        <div class="pt-1" v-if="item.trim() && item.includes('原因')" :key="index">
          <span style="word-break: break-all;" :class="item.includes('原因') ? 'text-danger font-weight-bolder' : ''">{{ item.trim() }}</span>
        </div>
      </template>
    </div>
    <div v-if="showAuditData">
      <div class="pt-1 text-primary font-weight-bolder">提交公司：{{showAuditData.saleComName}}</div>
      <div class="d-flex justify-content-between pt-1">
        <div>客诉单编号：{{showAuditData.no}}</div>
        <div>申请人：{{showAuditData.createdBy ? showAuditData.createdBy.userName : null}}</div>
      </div>
      <div class="d-flex pt-1">
        <div>客户编号：{{showAuditData.customer.no}}</div>
        <div class="pl-3">客户简称：{{showAuditData.customer.shortName}}</div>
      </div>
      <div class="d-flex pt-1">
        <div>送货日期：{{showAuditData.deliveryDateStr}}</div>
        <div class="pl-3">申请时间：{{showAuditData.businessHappenTimeStr}}</div>
      </div>
      <div class="d-flex pt-1">
        <div>合同号：{{showAuditData.contractNo}}</div>
        <div class="pl-3">生产编号：{{showAuditData.craftNos}}</div>
      </div>
      <div class="d-flex pt-1">
        <div>退货数量：{{showAuditData.quantity}}</div>
        <div class="pl-3">补发单号：{{showAuditData.fedNo}}</div>
        <div class="pl-3">补发数量：{{showAuditData.fedNum}}</div>
      </div>
      <div class="d-flex pt-1">
        <div>退货金额：{{showAuditData.normalAmount}}</div>
        <div class="pl-3">赔偿总额：{{showAuditData.satisfactionAmount}}</div>
      </div>
      <div class="d-flex pt-1">
        <div class="font-weight-bolder text-danger">客诉总金额：{{showAuditData.amount}}</div>
        <div class="pl-3">生产承担：{{showAuditData.groupAmount}}</div>
      </div>
      <div class="pt-1">原因及要求：{{showAuditData.rejectCause}}</div>
      <div class="pt-1">驳回原因：{{showAuditData.rejectionCause}}</div>
      <div class="pt-1">作废原因：{{showAuditData.cancellationCause}}</div>
      <div class="pt-1">备注：{{showAuditData.remark}}</div>
    </div>
  </div>
</template>

<script>
export default {
name: "customerComplaint",
  props: {
    showAuditData: [Object, Array],
    audit: Object
  }
}
</script>