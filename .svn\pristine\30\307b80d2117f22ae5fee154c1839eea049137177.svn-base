<template>
  <div>
    <div class="text-muted" v-if="!(item.inputType == 16 || item.inputType == 17)">{{item.name}}</div>
    <div v-if="item.inputType == 1 || item.inputType == 3 || item.inputType == 4 ||item.inputType == 5">
      <!--单行文本-->
      <input class="form-control form-control-sm" v-model="val.value" :disabled="editStatus == 1"/>
    </div>
    <div v-else-if="item.inputType == 2">
      <!--多行文本-->
      <textarea class="form-control form-control-sm" v-model="val.value" :disabled="editStatus == 1"></textarea>
    </div>
    <div class="checkbox-inline" v-else-if="item.inputType == 6 || item.inputType == 7">
      <!--复选框组-->
      <template v-for="row in item.dictValueList">
        <label class="checkbox checkbox-rounded" :key="row.id">
          <input type="checkbox" :checked="isIdInString(row.id)" :disabled="editStatus == 1" v-on:click="setVal(row)"/>
          <span></span>
          {{row.name}}
        </label>
      </template>
    </div>
    <div v-else-if="item.inputType == 8 || item.inputType == 9">
      <!--日期-->
      <input :id="'date' + val.id" class="form-control form-control-sm" :disabled="editStatus == 1"/>
    </div>
    <div v-else-if="item.inputType == 10">
      <!--单选下拉框-->
      <select class="form-control form-control-sm" v-model="val.value" :disabled="editStatus == 1">
        <template v-for="row in item.dictValueList">
          <option :key="row.id" :value="row.id">{{row.name}}</option>
        </template>
      </select>
    </div>
    <div v-else-if="item.inputType == 11 || item.inputType == 12 || item.inputType == 15">
      <!--搜索下拉框-->
      <div class="form-control form-control-sm text-truncate" v-on:click="openFilter" v-if="item.dictValueList && item.dictValueList.length > 0">
        <template v-for="row in item.dictValueList"><span :key="row.id" v-if="isIdInString(row.id)">{{ row.name }}&nbsp;</span></template>
      </div>
      <div class="form-control form-control-sm text-truncate text-muted" v-else>等待加载......</div>
    </div>
    <b-modal ref="filter" hide-footer hide-header>
      <div>
        <div class="pb-3 border-bottom">
          <div class="text-muted">搜索</div>
          <input class="form-control form-control-sm" style="width: 10rem;" v-model="msgFilter"/>
        </div>
        <div class="row pt-3" style="max-height: 30vh;overflow-y: auto;">
          <div class="col-12 pb-3"><template v-for="row in item.dictValueList"><span :key="row.id" v-if="isIdInString(row.id)">{{ row.name }}&nbsp;</span></template></div>
          <template v-for="row in item.dictValueList">
            <div class="col-4" :key="row.id" v-if="row.name && row.name.indexOf(msgFilter) !== -1">
              <button :class="['btn text-left', (isIdInString(row.id) ? 'btn-primary' : 'btn-outline-secondary'), 'w-100 mb-2 text-truncate']" v-on:click="setVal(row)">{{row.name}}</button>
            </div>
          </template>
        </div>
        <div class="row pt-10">
          <div class="col-12 text-right">
            <button class="btn btn-outline-secondary" v-on:click="cancelFilter">清空</button>
            <button class="btn btn-outline-primary ml-3" v-on:click="applyFilter">应用</button>
          </div>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
export default {
  name: "config",
  props: {
    item: Object,
    val: Object,
    editStatus: String,

  },
  data() {
    return {
      msgFilter: "",
    }
  },
  methods: {
    openFilter() {
      if (this.editStatus == '2'){
        this.$refs['filter'].show();
      }
    },
    cancelFilter() {
      this.val.value = "";
      this.$refs['filter'].hide();
    },
    applyFilter() {
      if (this.item.description){
        this.$parent.childLoadMsg(this.item, this.val.value);
      }
      this.$refs['filter'].hide();
    },
    isIdInString(targetId) {
      let idString = this.val ? this.val.value : "";
      if (!idString) {
        return false;
      }
      const idArray = idString.split(',').map(id => id.trim());
      return idArray.includes(targetId);
    },
    setVal(row) {
      if (this.editStatus !== '2'){
        return;
      }
      let idString = this.val ? this.val.value : "";
      let resIds = "";
      if (this.item.inputType == 12 || this.item.inputType == 7){
        if (this.isIdInString(row.id)){
          const parts = idString.split(',');
          for (const part of parts) {
            if (part.trim() !== row.id) {
              resIds = resIds ? resIds + "," + part.trim() : part.trim();
            }
          }
        }else {
          resIds = idString ? idString + "," + row.id : row.id;
        }
      }else {
        resIds = row.id;
      }
      this.val.value = resIds;
    },
  }
}
</script>