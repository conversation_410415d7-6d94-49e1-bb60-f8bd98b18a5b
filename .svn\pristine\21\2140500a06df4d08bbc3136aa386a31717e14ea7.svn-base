<template>
  <div class="d-flex flex-column flex-root">
    <div class="d-flex flex-row flex-column-fluid page">
      <div class="d-flex flex-column flex-row-fluid wrapper">
        <div class="content d-flex flex-column flex-column-fluid">
          <div class="subheader subheader-transparent" id="kt_subheader">
            <div class="container d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
              <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                <div class="d-flex flex-column">
                  <h5 class="text-white font-weight-bold my-2 mr-5">产品详情</h5>
                </div>
              </div>
            </div>
          </div>
          <div class="d-flex flex-column-fluid">
            <div class="container">
              <div class="row">
                <div class="col-12">
                  <div class="card card-custom gutter-b">
                    <div class="card-body d-flex flex-column p-3">
                      <div class="pl-3 pr-3 pt-3">
                        <div class="pt-1">
                          <div class="row">
                            <div class="col">

                              <div class="row pt-2 pb-2">
                                <div class="col font-weight-bolder">
                                  <div class="input-group align-items-center">
                                    <div class="input-group-append">
                                      产品：
                                    </div>
                                    <input class="form-control" v-model="product.configName" disabled>
                                  </div>
                                </div>
                              </div>
                              <div class="row pt-2 pb-2">
                                <div class="col font-weight-bolder">
                                  <div class="input-group align-items-center">
                                    <div class="input-group-append">
                                      名字：
                                    </div>
                                    <input class="form-control" v-model="product.name" disabled>
                                  </div>
                                </div>
                              </div>
                              <div class="row pt-2 pb-2">
                                <div class="col font-weight-bolder">
                                  <div class="input-group align-items-center">
                                    <div class="input-group-append">
                                      规格：
                                    </div>
                                    <input class="form-control" v-model="product.specifications" disabled>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="row" v-for="item in configList" :key="item.recordId">
                            <div class="col">
                              <div class="row pt-3 pb-3">
                                <div class="col font-weight-bolder">
                                  {{item.name}}
                                </div>
                              </div>
                              <div class="row pt-2 pb-2" v-for="row in item.list" :key="row.recordId" v-if="item.list">
                                <div class="col font-weight-bolder">
                                  {{row.name}}:
                                  <div class="row" v-if="row.inputType == 1">
                                    <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                      <div class="input-group">
                                        <input class="form-control" v-model="row.value" disabled>
                                        <div class="input-group-append" v-if="row.unitType">
                                          <button class="btn btn-outline-secondary">{{row.unitType}}</button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="row" v-if="row.inputType == 2">
                                    <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                      <div class="input-group">
                                        <input class="form-control" placeholder="长" v-model="row.value1" disabled>
                                        <div class="input-group-append">
                                          <button class="btn btn-outline-secondary">*</button>
                                        </div>
                                        <input class="form-control" placeholder="宽" v-model="row.value2" disabled>
                                        <div class="input-group-append" v-if="row.unitType">
                                          <button class="btn btn-outline-secondary">{{row.unitType}}</button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="row" v-if="row.inputType == 3">
                                    <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                      <div class="input-group">
                                        <input class="form-control" placeholder="长" v-model="row.value1" disabled>
                                        <div class="input-group-append">
                                          <button class="btn btn-outline-secondary">*</button>
                                        </div>
                                        <input class="form-control" placeholder="宽" v-model="row.value2" disabled>
                                        <div class="input-group-append">
                                          <button class="btn btn-outline-secondary">/</button>
                                        </div>
                                        <input class="form-control" placeholder="拼接数" v-model="row.value3" disabled>
                                        <div class="input-group-append" v-if="row.unitType">
                                          <button class="btn btn-outline-secondary">{{row.unitType}}</button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="row" v-if="row.inputType == 4">
                                    <div class="col-12">
                                      <button :class="craft.checked == 1? 'btn btn-outline-warning mr-1 mb-1': 'btn btn-outline-secondary mr-1 mb-1'"
                                              v-for="craft in row.list" :key="craft.recordId">
                                        {{ craft.value }}
                                      </button>
                                    </div>
                                  </div>
                                  <div class="row" v-if="row.inputType == 5">
                                    <div class="col-12">
                                      <button :class="craft.checked == 1? 'btn btn-outline-warning mr-1 mb-1': 'btn btn-outline-secondary mr-1 mb-1'"
                                              v-for="craft in row.list" :key="craft.recordId">
                                        {{ craft.value }}
                                      </button>
                                    </div>
                                  </div>
                                  <div class="row" v-if="row.inputType == 6">
                                    <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                      <button class="btn btn-primary">上传文件</button>
                                    </div>
                                  </div>
                                  <div class="row" v-if="row.inputType == 7">
                                    <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                      <textarea class="form-control" v-model="row.value" disabled></textarea>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
name: "supplierProduct_deail",
  created:function(){
    this.productId = this.$route.query.productId;
    this.loadProductDeail();
  },
  data(){
    return {
      productId: "",
      configId: "",
      configList: [],
      product: {}
    }
  },
  methods:{
    loadProductDeail: function () {
      this.product = [];
      const entity = {};
      entity.recordId = this.productId;
      entity.type = 2;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost('f/wechat/business/getProductDeail', entity).then(result => {
        this.product = result.data;
        this.configId = this.product.configId;
        this.loadConfigList();
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    loadConfigList: function () {
      this.configList = [];
      if (!this.configId){
        alert("请刷新重试");
        return;
      }
      const entity = {};
      entity.configId = this.configId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost('f/wechat/business/getConfigList', entity).then(result => {
        if (result.data && result.data.length > 0){
            this.configList = result.data;
          if (this.product){
            // 给相关工艺赋值
             this.setCraftVal();
          }
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)});
    },
    setCraftVal: function () {
      if (this.product && this.product.deailList && this.product.deailList.length > 0
          && this.configList && this.configList.length > 0){
        for (let i=0;i<this.configList.length;i++){
          const config = this.configList[i];
          if(!config.list){
            continue;
          }
          for (let k=0;k<config.list.length;k++){
            const deail = config.list[k];
            deail.value = "";
            deail.value1 = "";
            deail.value2 = "";
            deail.value3 = "";
            for (let j=0;j<this.product.deailList.length;j++){
              if (this.product.deailList[j].configDeailId == deail.recordId){
                if (deail.inputType == 1){
                  deail.value = this.product.deailList[j].value;
                }else if (deail.inputType == 2){
                  deail.value1 = this.product.deailList[j].value1;
                  deail.value2 = this.product.deailList[j].value2;
                }else if (deail.inputType == 3){
                  deail.value1 = this.product.deailList[j].value1;
                  deail.value2 = this.product.deailList[j].value2;
                  deail.value3 = this.product.deailList[j].value3;
                }else if (deail.inputType == 4){
                  if (this.product.deailList[j].value){
                    if(!deail.list){
                      continue;
                    }
                    for (let z = 0; z < deail.list.length; z++) {
                      if (deail.list[z].recordId == this.product.deailList[j].value){
                        deail.list[z].checked = 1;
                        break;
                      }
                    }
                  }
                }else if (deail.inputType == 5){
                  if (this.product.deailList[j].value){
                    const vals = this.product.deailList[j].value.split(",");
                    for (let x=0;x<vals.length;x++){
                      for (let z = 0; z < deail.list.length; z++) {
                        if (deail.list[z].recordId == vals[x]){
                          deail.list[z].checked = 1;
                        }
                      }
                    }
                  }
                }
                break;
              }
            }
          }
        }
      }
    }
  }
}
</script>