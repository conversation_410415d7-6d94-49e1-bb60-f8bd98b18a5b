<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">发布需求</h3>
    </div>
    <div class="bg-white rounded-lg p-3 mb-3">
      <label class="mb-2">类型</label>
      <select class="w-100 p-3 rounded-lg form-control" v-model="demand.type">
        <option value="1">我要采购</option>
        <option value="2">我要销售</option>
      </select>
    </div>
    <div class="bg-white rounded-lg p-3 mb-3">
      <label class="mb-2">物品</label>
      <input type="text" placeholder="请输入需求物品" class="w-100 p-3 rounded-lg form-control" v-model="demand.title">
    </div>
    <div class="bg-white rounded-lg p-3 mb-3 align-items-center">
      <label class="mb-2">需求数量</label>
      <input
          type="text"
          placeholder="请输入需求数量"
          class="w-100 p-3 rounded-lg form-control"
          v-model="demand.quantity"
      >
    </div>
    <div class="bg-white rounded-lg p-3 mb-3">
      <label class="mb-2">具体描述</label>
      <textarea
          placeholder="请详细描述您的需求..."
          rows="5"
          class="w-100 p-3 rounded-lg form-control"
          v-model="demand.description"
          @input="updateCharCount"
      ></textarea>
      <div class="text-right mt-1">
        {{ charCount }}/500
      </div>
    </div>
    <div class="bg-white rounded-lg p-3 mb-3">
      <label class="mb-2">预算范围</label>
      <div class="d-flex gap-3 align-items-center">
        <input
            type="number"
            placeholder="最低"
            class="flex-1 p-3 rounded-lg form-control" style="width: 120px;"
            v-model.number="demand.minBudget"
        >
        <span class="pl-3 pr-3">至</span>
        <input
            type="number"
            placeholder="最高"
            class="flex-1 p-3 rounded-lg form-control" style="width: 120px;"
            v-model.number="demand.maxBudget"
        >
        <select class="w-20 p-3 rounded-lg" v-model="demand.budgetUnit">
          <option value="rmb">元</option>
          <option value="thousand">千元</option>
          <option value="tenThousand">万元</option>
        </select>
      </div>
    </div>

    <!-- 期望完成时间 -->
    <div class="bg-white rounded-lg p-3 mb-3">
      <label class="mb-2">期望完成时间</label>
      <input type="date" class="w-100 p-3 rounded-lg form-control" v-model="demand.expectedDate">
    </div>

    <!-- 联系人信息 -->
    <div class="bg-white rounded-lg p-3 mb-3">
      <label class="mb-2">联系人姓名</label>
      <input
          type="text"
          placeholder="请输入联系人姓名"
          class="w-100 p-3 rounded-lg form-control"
          v-model="demand.contactName"
      >
    </div>

    <!-- 联系电话 -->
    <div class="bg-white rounded-lg p-3 mb-3">
      <label class="mb-2">联系电话</label>
      <input
          type="text"
          placeholder="请输入联系电话"
          class="w-100 p-3 rounded-lg form-control"
          v-model="demand.contactPhone"
      >
    </div>
    <div class="bg-white rounded-lg p-3 mb-3">
      <label class="block text-neutral-700 mb-2">上传附件（选填）</label>
      <div class="flex flex-wrap gap-3">
        <div class="upload-container w-20 h-20 border-2 border-dashed border-neutral-200 rounded-lg flex items-center justify-center cursor-pointer hover:border-primary transition-colors" @click="openFileSelector">
          <div class="text-center">
            <i class="fa fa-plus text-neutral-400 text-xl"></i>
            <p class="text-xs text-neutral-400 mt-1">上传附件</p>
          </div>
          <input type="file" v-show="false" ref="fileInput" @change="handleFileUpload">
        </div>
        <!-- 已上传文件预览区域 -->
        <div v-if="selectedFile" class="d-flex justify-content-between align-items-center border-bottom pt-1 pb-1">
          <img :src="selectedFilePreview" alt="附件预览" class="w-25 rounded-lg">
          <div>
            <button class="btn btn-sm btn-danger px-1 py-1" @click="removeFile">删除</button>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-white rounded-lg p-3 mb-3">
      <div class="row">
<!--        <div class="col-12">-->
<!--          &lt;!&ndash; 隐私协议 &ndash;&gt;-->
<!--          <div class="flex">-->
<!--            <input type="checkbox" id="privacyAgreement" class="text-primary" v-model="privacyAgreement">-->
<!--            <label for="privacyAgreement">-->
<!--              我已阅读并同意<a href="#" class="text-primary" @click.prevent="showAgreement('user')">《用户协议》</a>和<a href="#" class="text-primary" @click.prevent="showAgreement('privacy')">《隐私政策》</a>-->
<!--            </label>-->
<!--          </div>-->
<!--        </div>-->
        <div class="col-12 text-right">
          <button class="btn btn-outline-primary w-100" v-on:click="submitDemand">发布</button>
        </div>
      </div>
    </div>
    <div style="height: 150px;"></div>
  </div>
</template>

<script>
export default {
  name: 'PublishDemand',
  mounted() {
    this.userMsg = {};
    this.erpList = [];
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
    }else {
      alert("请重新进入公众号");
    }
  },
  data() {
    return {
      // 表单数据
      demand: {
        title: '',
        categoryId: '',
        description: '',
        minBudget: null,
        maxBudget: null,
        budgetUnit: 'rmb',
        expectedDate: '',
        contactName: '',
        contactPhone: '',
        type:'',
        quantity:"",
      },
      // 字符计数
      charCount: 0,
      // 文件上传
      selectedFile: null,
      selectedFilePreview: '',
      // 隐私协议同意状态
      privacyAgreement: false,
      // 模态框状态
      successModalVisible: false,
      agreementVisible: false,
      agreementType: 'user',
      userMsg:{},
    };
  },
  methods: {
    // 更新字符计数
    updateCharCount() {
      this.charCount = this.demand.description.length;

      // 限制最大长度
      if (this.charCount > 500) {
        this.demand.description = this.demand.description.substring(0, 500);
        this.charCount = 500;
      }
    },

    // 打开文件选择器
    openFileSelector() {
      this.$refs.fileInput.click();
    },

    // 处理文件上传
    handleFileUpload(event) {
      const file = event.target.files[0];
      if (file) {
        this.selectedFile = file;

        // 创建文件预览URL
        const reader = new FileReader();
        reader.onload = (e) => {
          this.selectedFilePreview = e.target.result;
        };
        reader.readAsDataURL(file);
      }
    },

    // 移除已选择的文件
    removeFile() {
      this.selectedFile = null;
      this.selectedFilePreview = '';
      this.$refs.fileInput.value = '';
    },

    // 提交需求
    submitDemand() {
      // 表单验证
      if (!this.demand.title) {
        alert("请输入需求标题");
        return;
      }
      if (!this.demand.type) {
        alert("请选择需求类型");
        return;
      }
      if (!this.demand.quantity) {
        alert("请选择需求数量");
        return;
      }
      if (!this.demand.minBudget) {
        alert("预算最低范围不能为空");
        return;
      }
      if (!this.demand.maxBudget) {
        alert("预算最高范围不能为空");
        return;
      }
      if (!this.demand.budgetUnit) {
        alert("预算范围范围不能为空");
        return;
      }
      if (Number(this.demand.minBudget) > Number(this.demand.maxBudget))
      {
        alert("预算最低范围不能大于预算最高范围");
        return;
      }
      if (!this.demand.description) {
        alert("请输入需求描述");
        return;
      }
      if (!this.demand.contactName) {
        alert("请输入联系人姓名");
        return;
      }
      if (!this.demand.contactPhone) {
        alert("请输入联系电话");
        return;
      }
      this.$parent.enableLoadFlag(true);
      this.demand.userId = this.userMsg.recordId;
      this.$axios.fetchPost("f/wechat/business/saveDemand",this.demand).then(result => {
        if(result.data && result.data == "success"){
          alert("发布需求成功！");
        }else {
          alert("发布失败，请刷新重试");
        }
        //重置为空
        this.demand = {};
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
      // 模拟API调用成功
      setTimeout(() => {
        this.successModalVisible = true;
      }, 500);
    },

    // 关闭成功模态框
    closeSuccessModal() {
      this.successModalVisible = false;

      // 返回上一页
      setTimeout(() => {
        this.$router.back();
      }, 300);
    },

    // 显示协议
    showAgreement(type) {
      this.agreementType = type;
      this.agreementVisible = true;
    },

    // 关闭协议
    closeAgreement() {
      this.agreementVisible = false;
    }
  }
}
</script>

<style scoped>
@layer utilities {
  .content-auto {
    content-visibility: auto;
  }
  .form-input-focus {
    @apply border-primary ring-1 ring-primary;
  }
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}
</style>
