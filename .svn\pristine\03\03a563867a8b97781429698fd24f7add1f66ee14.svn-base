<template>
  <!--业务员设置审批-->
  <div>
    <div class="pb-1" v-if="audit && audit.auditResult && audit.auditResult.trim()">
      <template v-for="(item, index) in audit.auditResult.split(';')">
        <div class="pt-1" v-if="item.trim()" :key="index">
          <span style="word-break: break-all;" :class="item.includes('原因') ? 'text-danger font-weight-bolder' : ''">{{ item.trim() }}</span>
        </div>
      </template>
    </div>
    <div v-if="showAuditData">
      <div class="text-primary font-weight-bolder">提交公司：{{showAuditData.companyName}}</div>
      <div class="text-primary font-weight-bolder">客户名称：{{showAuditData.shortName}}</div>
      <div class="d-flex justify-content-between">
        <div class="p-1 text-danger">
          <div class="font-weight-bolder">变更前</div>
          <div class="pt-1">部门：{{ showAuditData.editBeforeDepartmentName }}</div>
          <div class="pt-1">业务员：{{ showAuditData.editBeforeSalesmanName }}</div>
          <div class="pt-1">
            客户类型：
            <span v-if="showAuditData.editBeforeCustType == '1'">公司分配</span>
            <span v-else-if="showAuditData.editBeforeCustType == '2'">自主开发</span>
            <span v-else>未知类型</span>
          </div>
        </div>
        <div class="p-1 text-success">
          <div class="font-weight-bolder">变更后</div>
          <div class="pt-1">部门：{{ showAuditData.editAfterDepartmentName }}</div>
          <div class="pt-1">业务员：{{ showAuditData.editAfterSalesmanName }}</div>
          <div class="pt-1">
            客户类型：
            <span v-if="showAuditData.editAfterCustType == '1'">公司分配</span>
            <span v-else-if="showAuditData.editAfterCustType == '2'">自主开发</span>
            <span v-else>未知类型</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
name: "salesPersonSetup",
  props: {
    showAuditData: [Object, Array],
    audit: Object
  }
}
</script>