<template>
  <div v-if="userMsg && userMsg.recordId">
    <div class="mb-5">
      <h3 class="text-white">个人中心</h3>
    </div>
    <section class="bg-white p-2 rounded-sm">
      <div class="d-flex align-items-center">
        <img :src="userMsg.downloadUrl" alt="用户头像" class="avatar-common">
        <div class="flex-grow-1 pl-3">
          <div class="d-flex justify-content-between align-items-center">
            <div class="font-weight-bolder font-size-h5">{{ userMsg.userName }}</div>
            <span class="badge alert-primary d-flex align-items-center"><i class="fa fa-medal mr-1 text-primary"></i>会员</span>
          </div>
          <div>手机号码：{{userMsg.phone}}</div>
          <div class="text-muted" v-if="userMsg.icloudCompanyList && userMsg.icloudCompanyList.length > 0">
            {{ userMsg.icloudCompanyList[0].name }}<span v-if="userMsg.icloudCompanyList.length > 1">......</span>
          </div>
<!--          <div class="user-actions">
            <button v-on:click="router_page('/my/password')"><i class="fa fa-key"></i>我的二维码</button>
            <button v-on:click="router_page('/my/password')"><i class="fa fa-gift"></i>会员卡</button>
          </div>-->
        </div>
      </div>
    </section>

    <!-- 账户信息 -->
<!--    <section class="account-info rounded-sm">
      <div class="grid-3">
        <template v-for="item in headerList">
          <div class="account-item" :key="item.id">
            <p class="amount">{{ item.num }}</p>
            <label style="font-size: 12px;color: #666;">{{ item.name }}</label>
          </div>
        </template>
      </div>
    </section>-->

    <!-- 功能列表 -->
    <section class="bg-white mt-6 rounded-sm">
      <div class="pl-3 pr-3">
        <ul>
          <template v-for="item in itemList">
            <li class="d-flex align-items-center border-bottom pb-4 pt-4" :key="item.id" v-on:click="router_page(item.routerName)">
              <div style="width: 3rem;"><i :class="['fa', item.iconClass]"></i></div>
              <div class="flex-grow-1">{{ item.name }}</div>
              <div><i class="fa fa-angle-right"></i></div>
            </li>
          </template>
        </ul>
      </div>
    </section>

    <!-- 客服与设置 -->
    <section class="bg-white mt-6 rounded-sm">
      <div class="pl-3 pr-3">
        <ul>
          <template v-for="item in rowList">
            <li class="d-flex align-items-center border-bottom pb-4 pt-4" :key="item.id" v-on:click="router_page(item.routerName)">
              <div style="width: 3rem;"><i :class="['fa', item.iconClass]"></i></div>
              <div class="flex-grow-1">{{ item.name }}</div>
              <div><i class="fa fa-angle-right"></i></div>
            </li>
          </template>
        </ul>
      </div>
    </section>

    <!-- 底部按钮 -->
    <div class="pt-7">
      <button class="btn btn-outline-primary bg-white text-dark w-100" v-on:click="logOut">退出登录</button>
    </div>
    <div style="height: 100px;"></div>
  </div>
</template>

<script>
import {ADD_BODY_CLASSNAME, REMOVE_BODY_CLASSNAME} from "@/core/services/store/htmlclass.module";
import {LOGOUT} from "@/core/services/store/auth.module";

export default {
  name: 'PersonalCenter',
  data() {
    return {
      userMsg: {},
      itemList: [
        {id: 1, name: "个人信息", iconClass: "fa-user-circle", routerName: "/b/userMsg"},
        {id: 3, name: "企业信息", iconClass: "fa-star", routerName: "/b/company"},
        {id: 4, name: "合作伙伴", iconClass: "fa-users", routerName: "/b/partner"},
        {id: 5, name: "历史反馈", iconClass: "fa-history", routerName: "/b/historyBack"},
        {id: 6, name: "我的需求", iconClass: "fa-list", routerName: "/b/historyDemand"},
      ],
      rowList:[
        {id: 1, name: "联系客服", iconClass: "fa-headphones", routerName: "/b/service"},
        {id: 2, name: "帮助与反馈", iconClass: "fa-question-circle", routerName: "/b/help"},
      ],
      headerList: [
        {id: 1, name: "钱包", num: "100", routerName: ""},
        {id: 2, name: "打样券", num: "3", routerName: ""},
        {id: 3, name: "积分", num: "1000", routerName: ""}
      ]
    };
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
    }else {
      alert("请重新进入公众号");
    }
  },
  methods: {
    router_page(name) {
      this.$router.push(name);
    },
    logOut: function (){
      if (this.userMsg && this.userMsg.openId && this.userMsg.defaultDb){
        this.message = `账号退出中...`;
        this.$store.dispatch(ADD_BODY_CLASSNAME, "page-loading");
        this.$axios.fetchPost("f/wechat/kybsoft/logOut", this.userMsg).then(result => {
          if(result.data && result.data == "success"){
            this.$store.dispatch(LOGOUT);
            this.message = `账号已退出...`;
            this.$router.push("/jumpNode");
          }else {
            alert("请重新进入公众号");
            this.$store.dispatch(REMOVE_BODY_CLASSNAME, "page-loading");
          }
        }).catch(err => {console.log(err);});
      }else {
        alert("关键信息缺失，请重新进入公众号");
      }
    },
  }
}
</script>
