<template>
  <div>
    <div class="bg-white rounded-sm p-3 mb-3 font-size-lg">
      <div>
        <span class="text-danger">*</span>标题
        <input class="form-control" v-model="dayThing.title"/>
      </div>
      <div class="pt-3">
        <span class="text-danger">*</span>内容
        <textarea class="form-control" v-model="dayThing.content"></textarea>
      </div>
      <div class="pt-3">
        <span class="text-danger">*</span>参与人员
        <div v-on:click="openFilter">
          <label class="form-control">
            <template v-for="item in empList">
              <label v-if="isIdInString(item.recordId)" :key="item.recordId">
                {{item.name}}&nbsp;
              </label>
            </template>
          </label>
        </div>
      </div>
      <div class="pt-3">
        <span class="text-danger">*</span>地点
        <input class="form-control" v-model="dayThing.address"/>
      </div>
      <div class="pt-3 row">
        <div class="col-6">
          <span class="text-danger">*</span>开始时间
          <input class="form-control" id="startTime"/>
        </div>
        <div class="col-6">
          <span class="text-danger">*</span>结束时间
          <input class="form-control" id="endTime"/>
        </div>
      </div>
      <div class="pt-3 row">
        <div class="col-6">
          提示参与人
          <select class="form-control" v-model="dayThing.shareTimeWx">
            <option value="1">1天前</option>
            <option value="2">2天前</option>
            <option value="3">3天前</option>
          </select>
        </div>
        <div class="col-6">
          提示发布人
          <select class="form-control" v-model="dayThing.createTimeWx">
            <option value="1">1天前</option>
            <option value="2">2天前</option>
            <option value="3">3天前</option>
          </select>
        </div>
      </div>
    </div>
    <b-modal ref="filter" hide-footer hide-header>
      <div>
        <div class="pb-3 border-bottom">
          <div class="text-muted">参与人员</div>
          <input class="form-control form-control-sm" style="width: 10rem;" v-model="empFilter"/>
        </div>
        <div class="row pt-3" style="max-height: 30vh;overflow-y: auto;">
          <template v-for="item in empList">
            <div class="col-4" :key="item.recordId" v-if="item.name.indexOf(empFilter) !== -1">
              <button :class="['btn text-left', (isIdInString(item.recordId) ? 'btn-primary' : 'btn-outline-secondary'), 'w-100 mb-2']" v-on:click="setEmpVal(item)">{{item.name}}</button>
            </div>
          </template>
        </div>
        <div class="row pt-10">
          <div class="col-12 text-right">
            <button class="btn btn-outline-secondary" v-on:click="cancelFilter">清空</button>
            <button class="btn btn-outline-primary ml-3" v-on:click="applyFilter">应用</button>
          </div>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
import $ from "jquery";
export default {
  name: "addDayThing",
  props: {
    dayThing: Object
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.employeeId){
        return;
      }
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return{
      userMsg: {},
      empList:[],
      empFilter: "",
      shareId: "",
      startTime: "",
      endTime: ""
    }
  },
  methods: {
    loadData() {
      this.getEmpList();
    },
    getEmpList () {
      const query = {};
      query.departId = this.userMsg.departId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/getEmpList",query).then(result => {
        this.empList = result.data;
        if (this.dayThing && this.dayThing.recordId) {
          this.shareId = this.dayThing.shareId;
          this.startTime = this.dayThing.startTime;
          this.endTime = this.dayThing.endTime;
          this.initQueryDate('startTime', this.startTime);
          this.initQueryDate('endTime', this.endTime);
        }else{
          this.getDataT();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    getDataT: function (){
      this.startTime = new Date();
      this.endTime = new Date();
      this.initQueryDate('startTime', new Date());
      this.initQueryDate('endTime', new Date());
    },
    initQueryDate: function (id, startDates) {
      if ($('#' + id + '').is(':visible')) {
        const _this = this;
        $('#' + id + '').daterangepicker({
          'singleDatePicker': true,
          'showDropdowns': true,
          'timePicker': true,
          'timePicker24Hour': true,
          'startDate': startDates, // 设置开始日期
          'opens': 'center',
          'drops': 'down',
          'locale': {
            'format': 'YYYY-MM-DD HH:mm:ss',
            'separator': ' - ',
            'applyLabel': '确定',
            'cancelLabel': '取消',
            'fromLabel': 'From',
            'toLabel': '到',
            'customRangeLabel': 'Custom',
            'weekLabel': 'W',
            'daysOfWeek': [
              '日',
              '一',
              '二',
              '三',
              '四',
              '五',
              '六'
            ],
            'monthNames': [
              '一月',
              '二月',
              '三月',
              '四月',
              '五月',
              '六月',
              '七月',
              '八月',
              '九月',
              '十月',
              '十一月',
              '十二月'
            ],
            'firstDay': 1
          }
        }, function (start) {
          if (id === 'startTime') {
            _this.startTime = start.format('YYYY-MM-DD HH:mm:ss');
          }
          if (id === 'endTime') {
            _this.endTime = start.format('YYYY-MM-DD HH:mm:ss');
          }
        })
      } else {
        if (this.temp > 50) {
          this.temp = 0;
        }
        this.temp++;
        // 递归 等待dom渲染完毕
        const _this = this;
        setTimeout(function () { _this.initQueryDate(id, startDates); }, 500)
      }
    },
    openFilter() {
      this.$refs['filter'].show();
    },
    cancelFilter() {
      this.shareId = "";
      this.$refs['filter'].hide();
    },
    applyFilter() {
      this.$refs['filter'].hide();
    },
    setEmpVal(item) {
      this.clickEmp(item.recordId);
    },
    isIdInString(targetId) {
      let idString = this.shareId;
      if (!idString) {
        return false;
      }
      const idArray = idString.split(',').map(id => id.trim());
      return idArray.includes(targetId);
    },
    clickEmp(id, flag) {
      let idString = this.shareId;
      let resIds = "";
      if (this.isIdInString(id, flag)){
        const parts = idString.split(',');
        for (const part of parts) {
          if (part.trim() !== id) {
            resIds = resIds ? resIds + "," + part.trim() : part.trim();
          }
        }
      }else {
        resIds = idString ? idString + "," + id : id;
      }
      this.shareId = resIds;
    }
  }

}
</script>
