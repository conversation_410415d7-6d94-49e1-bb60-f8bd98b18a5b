<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">联系客服</h3>
    </div>
    <div class="card">
      <div class="card-body p-3">
        <div class="d-flex align-items-center font-size-h5 font-weight-bolder">
          <div>客服工作时间</div>
          <div class="pl-3">9:00-21:00（工作日）</div>
        </div>
      </div>
    </div>
    <template v-for="(item, index) in userList">
      <div :class="['d-flex justify-content-between align-items-center p-3 border-bottom font-size-lg bg-white mt-3']" :key="index">
        <div class="d-flex align-items-center">
          <div class="image-container-common"><img :src="item.downloadUrl"></div>
          <div class="ml-3">
            <div style="font-size: 1.3rem;">{{ item.chineseContent }}：{{ item.userName }}</div>
            <div class="pt-3">
              <a :href="['mailto:', item.email]" class="border-bottom" style="text-decoration: underline;">邮箱：{{ item.email }}</a>
            </div>
          </div>
        </div>
        <div>
          <a :href="['tel:', item.phone]" style="text-decoration: underline;">{{ item.phone }}</a>
        </div>
      </div>
    </template>
    <div style="height: 100px;"></div>
  </div>
</template>
<script>
export default {
  mounted() {
    this.loadData();
  },
  data() {
    return {
      userList: []
    }
  },
  methods: {
    loadData() {
      this.userList = [];
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost('hr/user/getManageUserList').then((result) => {
        if (result && result.data) {
          this.userList = result.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)})
    }
  }
}
</script>