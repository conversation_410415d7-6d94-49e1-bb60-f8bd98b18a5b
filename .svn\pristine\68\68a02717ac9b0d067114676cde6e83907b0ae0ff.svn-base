<template>
<div>
  <div v-if="showAuditData && showAuditData.length > 0">
    <div class="pt-2">
      <div class="d-flex justify-content-between align-items-center pb-1 border-bottom">
        <div class="font-weight-bolder">订单明细</div>
        <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlag">{{ flag && flag == '1' ? '收起' : '展开' }}</button>
      </div>
      <div v-if="flag == '1'">
        <div v-for="(item,index) in showAuditData" :key="index">
          <div class="pt-2" v-if="index == 0">
            <div class="d-flex text-left font-weight-bolder">盘点汇总</div>
            <div class="d-flex justify-content-between">
              <div>实盘笔数：{{item.material.xboardQty}}</div>
              <div>
                <span v-if="item.material.storeHouseId != -1">实盘金额:{{item.material.totalPcsQuantity}}</span>
                <span v-if="item.material.storeHouseId == -1">实盘面积:{{item.material.totalPcsQuantity}}</span>
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>账务相符笔数：{{item.material.beConvertxboardQty}}</div>
              <div>
                <span v-if="item.material.storeHouseId != -1">账务相符金额:{{item.material.lowPrice}}</span>
                <span v-if="item.material.storeHouseId == -1">账务相符面积:{{item.material.lowPrice}}</span>
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>盘盈笔数：{{item.material.convertStocks}}</div>
              <div>
                <span v-if="item.material.storeHouseId != -1">盘盈金额:{{item.material.unitArea}}</span>
                <span v-if="item.material.storeHouseId == -1">盘盈面积:{{item.material.unitArea}}</span>
              </div>
            </div>
            <div class="d-flex justify-content-between">
              <div>盘亏笔数：{{item.material.deliveryOaQty}}</div>
              <div>
                <span v-if="item.material.storeHouseId != -1">盘亏金额:{{item.material.totalArea}}</span>
                <span v-if="item.material.storeHouseId == -1">盘亏面积:{{item.material.totalArea}}</span>
              </div>
            </div>
          </div>
          <div class="pt-2" v-if="index>0">
            <div class="font-weight-bolder">盈亏明细详情</div>
            <div class="d-flex justify-content-between">
              <div v-if="item.material.status == 1">物料编号:{{item.material.no}}</div>
              <div v-if="item.material.status == 1">物料名称:{{item.material.name}}</div>
              <div v-if="item.material.status != 1">生产编号:{{item.material.no}}</div>
              <div v-if="item.material.status != 1">工序:{{item.material.storehouse.name}}</div>
            </div>
            <div class="d-flex justify-content-between">
              <div v-if="item.material.status == 1">账面数:{{item.material.stocks}}</div>
              <div v-if="item.material.status == 1">盘点数:{{item.material.snapStrocks }}</div>
              <div v-if="item.material.status != 1">账面A板:{{item.material.storehouse.recordId}}</div>
              <div v-if="item.material.status != 1">账面B板:{{item.material.stocks}}</div>
            </div>
            <div class="d-flex justify-content-between" v-if="item.material.status != 1">
              <div>A板实盘:{{item.material.materialId}}</div>
              <div>B板实盘:{{item.material.snapStrocks}}</div>
            </div>
            <div class="d-flex justify-content-between" v-if="item.material.status == 1">
              <div>
                盈亏数量:{{(item.material.snapStrocks?item.material.snapStrocks:0)- (item.material.stocks?item.material.stocks:0)}}
              </div>
              <div>
                盈亏金额:
                <span v-if="item.material.price">{{((item.material.snapStrocks?item.material.snapStrocks:0) - (item.material.stocks?item.material.stocks:0))*item.material.price}}</span>
                <span v-if="!item.material.price">0</span>
              </div>
            </div>
            <div class="d-flex justify-content-between" v-if="item.material.status != 1">
              <div>
                A板盈亏数量({{item.material.name}}):{{(item.material.materialId?item.material.materialId:0)- (item.material.storehouse.recordId?item.material.storehouse.recordId:0)}}
              </div>
              <div>
                B板盈亏数量({{item.material.name}}):{{(item.material.snapStrocks?item.material.snapStrocks:0) - (item.material.stocks?item.material.stocks:0)}}
              </div>
            </div>
            <div class="d-flex">
              <div>差异说明:{{item.material.remark}}</div>
            </div>
          </div>
        </div>
      </div>
      </div>
  </div>
</div>
</template>

<script>
export default {
name: "inventoryCheck",
  props: {
    showAuditData: [Object, Array],
  },
  data() {
    return{
      flag: "1"
    }
  },
  methods: {
    changeFlag() {
      if (this.flag == "1"){
        this.flag = "2";
      }else {
        this.flag = "1";
      }
    }
  }
}
</script>
