<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">MES工具</h3>
    </div>
    <div class="bg-white rounded-sm p-3">
      <div class="d-flex justify-content-between align-items-center">
        <div style="width: 16rem;">
          <select class="form-control form-control-sm" v-model="company" v-on:change="loadData">
            <template v-for="item in erpList">
              <option :value="item" :key="item.recordId">
                {{item.name}}
              </option>
            </template>
          </select>
        </div>
        <button class="btn btn-sm btn-outline-primary" v-on:click="showCountItem()">过数权限</button>
      </div>
      <div class="pt-3 font-weight-bolder" v-if="userMsg && userMsg.empName">
        你好！{{userMsg.empName}}
      </div>
      <div class="d-flex justify-content-center" style="padding-top: 60px;">
        <button class="btn btn-primary btn-lg" style="border-radius: 50%;height: calc(3em + 10rem + 10px);width: calc(3em + 10rem + 10px);" v-on:click="wxScancode"><h1>扫一扫</h1></button>
      </div>
      <div class="d-flex justify-content-between align-items-center" style="padding-top: 80px;padding-bottom: 80px;">
        <button class="btn btn-sm btn-outline-secondary" v-on:click="countList">过数记录</button>
        <button class="btn btn-sm btn-outline-secondary">当日排单<span class="text-danger">&nbsp;3</span></button>
      </div>
    </div>
    <b-modal ref="countItems" hide-footer title="过数权限" no-close-on-backdrop>
      <div>
        <div class="pb-3 border-bottom">
          <div class="text-muted">工序筛选</div>
          <input class="form-control form-control-sm" style="width: 10rem;" v-model="categorysFilter"/>
        </div>
        <div class="row pt-3" v-if="company.categorys">
          <template v-for="(item, index) in countItemList">
            <div class="col-3 pb-2" :key="index" v-if="item && item.indexOf(categorysFilter) !== -1">
              <span class="badge alert-success w-100 text-left text-truncate">{{item}}</span>
            </div>
          </template>
        </div>
        <span v-else>无过数权限</span>
      </div>
    </b-modal>
    <b-modal ref="applyProcess" size="sm" hide-footer title="工序排单解禁" no-close-on-backdrop>
      <div>
        <table class="table table-striped table-bordered table-hover">
          <thead>
          <tr class="heading">
            <th>
              <input type="checkbox" v-model="allCheck" v-on:change="selectionAllCheck"/>&nbsp;全选
            </th>
            <th>序号</th>
            <th>过数工序</th>
          </tr>
          </thead>
          <tbody>
          <template v-for="(process,index) in egProcessList">
            <tr :key="process.recordId">
              <td><input type="checkbox" v-model="process.check" v-on:change="setRecordIds"/></td>
              <td>{{ index + 1 }}</td>
              <td>{{process.category}}</td>
            </tr>
          </template>
          </tbody>
        </table>
        <div class="row">
          <div class="col-12 text-right">
            <button type="button" class="btn btn-primary" v-on:click="approval">确定</button>
          </div>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
import wx from 'weixin-js-sdk';
import { getWxMsg } from '@/assets/js/utils/wechatScanUtils';
import { getErpList, getCompany } from '@/assets/js/utils/userAuth';
export default {
  name: "scanWork",
  created:function(){
    if (process.env.NODE_ENV === "production"){
      getWxMsg(window.location.href.split('#')[0], wx);
    }
  },
  mounted() {
    this.userMsg = {};
    this.erpList = [];
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.erpList = getErpList(sysUser);
      this.company = getCompany(sysUser);
      this.loadData();
      this.getCountItemList();
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return{
      erpList: [],
      userMsg: {},
      company: {},
      egProcessList: [],
      allCheck: false,
      processIds: '',
      produceBatchDetailId: '',
      countItemList: [],
      categorysFilter: "",
    }
  },
  methods:{
    loadData() {
      if (!this.company || !this.company.recordId){
        alert("无公司信息");
        return;
      }
      window.localStorage.setItem('company',JSON.stringify(this.company));
    },
    showCountItem() {
      this.$refs['countItems'].show();
    },
    wxScancode() {
      // 是否有过数权限
      if(!this.company.categorys){
        alert("你没有过数权限！");
        return;
      }
      if (!(process.env.NODE_ENV === "production")){
        // let flag = this.checkCode("230118");
        let flag = true;
        if (flag){
          this.$router.push({path: '/produce',
            query: {
              batchId: "167379",
              batchDetailId: "202692"
            }
          });
        }
        return;
      }
      wx.scanQRCode({
        needResult: 1,
        scanType: ['qrCode'],
        success: function (res) {
          const result = res.resultStr;
          if(result){
            let batch = "";
            if(result.indexOf("KS") >= 0){
              // 修理拿客诉单id
              batch = result.split("KSA");
            }else{
              // 正常生产
              batch = result.split("A");
            }
            if(batch && batch.length > 1){
              const batchNos = batch[1].split("B");
              this.produceBatchDetailId = batchNos[1];
              if(batchNos && batchNos.length > 1){
                let flag = this.checkCode(batchNos[1]);
                if (flag){
                  this.$router.push({path: '/produce',
                    query: {
                      batchId: batchNos[0],
                      batchDetailId: batchNos[1]
                    }
                  });
                }
              }
            }else{
              alert("请刷新重试" + result);
            }
          }
        },
        error: function () {
          console.log('系统错误');
        }
      })
    },
    checkCode(produceBatchDetailId) {
      let flag = false;
      if (this.company && this.company.countOverFlag && this.company.countOverFlag == "1"){
        // 判断该员工是否有跳过排单的权限
        flag = true;
        return flag;
      }else {
        // 判断是否在当日排单
        let detail = {};
        detail.produceBatchDetailId = produceBatchDetailId;
        detail.company = this.company;
        this.$parent.enableLoadFlag(true);
        this.$axios.fetchPost('f/wechat/produce/checkRcode', detail).then((result) => {
          if (result && result.data) {
            if(result.data.countOverFlag == 0) {
              if(result.data.result == 'fail') {
                alert(result.data.message);
              } else {
                flag = true;
              }
            } else if(result.data.countOverFlag == 1) {
              this.egProcessList = result.data.egProcessList;
              if (this.egProcessList && this.egProcessList.length > 0){
                this.egProcessList[0].check = true;
              }
              this.$refs['applyProcess'].show();
            }
          }
          this.$parent.enableLoadFlag(false);
          return flag;
        }).catch(err => {console.log(err)})
      }
    },
    selectionAllCheck() {
      for (let obj of this.egProcessList) {
        obj.check = this.allCheck;
      }
      this.setRecordIds();
    },
    setRecordIds() {
      let recordIds = null;
      for (let obj of this.egProcessList) {
        if (obj.check) {
          if (recordIds) {
            recordIds = recordIds + "," + obj.recordId;
          } else {
            recordIds = obj.recordId;
          }
        }
      }
      this.processIds = recordIds;
    },
    approval() {
      if(this.clickFlag)
      {
        alert("请勿多次点击！");
        return;
      }
      let approval = {};
      approval.produceBatchDetailId = this.produceBatchDetailId;
      approval.processIds = this.processIds;
      approval.company = this.company;
      approval.createdBy = {};
      approval.createdBy.recordId = this.company.userId;
      this.clickFlag = true;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost('f/wechat/produce/approvalProduce', approval).then((result) => {
        if (result && result.data) {
          if (result.data.result == "success"){
            if (result.data.messageTwo){
              alert(result.data.message + result.data.messageTwo);
            }else{
              alert(result.data.message);
            }
            this.$refs['applyProcess'].hide();
          }else {
            alert(result.data.message)
          }
        }
        this.clickFlag = false;
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)})
    },
    countList: function() {
      this.$router.push('/countList');
    },
    getCountItemList() {
      if (this.company && this.company.categorys){
        this.countItemList = this.company.categorys.split(",");
      }
      return null;
    }
  }
}
</script>
