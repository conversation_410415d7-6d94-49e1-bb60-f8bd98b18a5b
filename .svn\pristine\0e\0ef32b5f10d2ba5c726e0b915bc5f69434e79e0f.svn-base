<template>
  <div>
    <div class="bg-white rounded-sm p-3 mb-3 font-size-lg">
      <div class="text-muted text-truncate pb-3">{{report && report.departmentName ? report.departmentName : organizationName}}</div>
      <div v-if="report.flag == '1'">
        日报时间
        <div class="pt-1">
          <div v-if="report.recordId">
            <span class="form-control">{{report.day}}</span>
          </div>
          <div v-else>
            <input id="date1" class="form-control"/>
          </div>
        </div>
      </div>
      <div v-else-if="report.flag == '2'">
        <div v-if="report.recordId">
          <span class="form-control" v-if="report.day">{{(report.day).substring(0,4)}}第{{(report.day).substring(4,6)}}周报告</span>
        </div>
        <div class="row" v-else>
          <div class="col-6">
            周报年份
            <div class="pt-1">
              <select class="form-control" v-model="report.year" v-on:change="getWeek">
                <template v-for="item in yearList">
                  <option :key="item.id" :value="item.id">
                    {{item.name}}
                  </option>
                </template>
              </select>
            </div>
          </div>
          <div class="col-6">
            周报周数
            <div class="pt-1">
              <select class="form-control" v-model="report.week" v-on:change="setWeek">
                <template v-for="item in weekList">
                  <option :key="item.id" :value="item.id">
                    {{item.name}}
                  </option>
                </template>
              </select>
            </div>
          </div>
        </div>
      </div>
      <div v-else-if="report.flag == '3'">
        <div v-if="report.recordId">
          <span class="form-control" v-if="report.day">{{(report.day).substring(0,4)}}第{{(report.day).substring(4,6)}}月报告</span>
        </div>
        <div class="row" v-else>
          <div class="col-6">
            月报年份
            <div class="pt-1">
              <select class="form-control" v-model="report.year" v-on:change="getMonth">
                <template v-for="item in yearList">
                  <option :key="item.id" :value="item.id">
                    {{item.name}}
                  </option>
                </template>
              </select>
            </div>
          </div>
          <div class="col-6">
            月报月份
            <div class="pt-1">
              <select class="form-control" v-model="report.month" v-on:change="setMonth">
                <template v-for="item in monthList">
                  <option :key="item.id" :value="item.id">
                    {{item.name}}
                  </option>
                </template>
              </select>
            </div>
          </div>
        </div>
      </div>
      <div class="pt-3">
        工作成效
        <textarea class="form-control" v-model="content"></textarea>
      </div>
      <div class="pt-3">
        总结心得
        <textarea class="form-control" v-model="contentTwo"></textarea>
      </div>
      <div class="pt-3">
        计划内容
        <textarea class="form-control" v-model="contentThree"></textarea>
      </div>
      <div class="pt-3">
        共享人员
        <div v-on:click="openFilter">
          <label class="form-control form-control-sm">
            <template v-for="item in empList">
              <label v-if="isIdInString(item.recordId)" :key="item.recordId">
                {{item.name}}&nbsp;
              </label>
            </template>
          </label>
        </div>
      </div>
      <div class="pt-3">
        直接上级
        <span class="form-control">{{userMsg.superEmpName}}<span v-if="userMsg.superSuperEmpName">-{{userMsg.superSuperEmpName}}</span></span>
      </div>
    </div>
    <b-modal ref="filter" hide-footer hide-header>
      <div>
        <div class="pb-3 border-bottom">
          <div class="text-muted">共享人员</div>
          <input class="form-control form-control-sm" style="width: 10rem;" v-model="empFilter"/>
        </div>
        <div class="row pt-3" style="max-height: 30vh;overflow-y: auto;">
          <template v-for="item in empList">
            <div class="col-4" :key="item.recordId" v-if="item.name.indexOf(empFilter) !== -1">
              <button :class="['btn text-left', (isIdInString(item.recordId) ? 'btn-primary' : 'btn-outline-secondary'), 'w-100 mb-2']" v-on:click="setEmpVal(item)">{{item.name}}</button>
            </div>
          </template>
        </div>
        <div class="row pt-10">
          <div class="col-12 text-right">
            <button class="btn btn-outline-secondary" v-on:click="cancelFilter">清空</button>
            <button class="btn btn-outline-primary ml-3" v-on:click="applyFilter">应用</button>
          </div>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
import $ from "jquery"
export default {
  name: "addReport",
  props: {
    report: Object,
    organizationName: String
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.employeeId){
        return;
      }
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data() {
    return{
      userMsg: {},
      empList: [],
      yearList: [],
      weekList: [],
      monthList: [],
      month: "",
      week: "",
      year: "",
      shareIds: "",
      content: "",
      contentTwo: "",
      contentThree: "",
      empFilter: "",
    }
  },
  methods: {
    loadData() {
      if (!this.report || !this.report.flag){
        alert("请刷新重试");
        this.$router.push("/work");
        return;
      }
      this.getEmpList();
    },
    getEmpList () {
      const query = {};
      query.departId = this.userMsg.departId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/getEmpList",query).then(result => {
        this.empList = result.data;
        if (this.report && this.report.recordId) {
          this.shareIds = this.report.shareIds;
          this.content = this.report.content;
          this.contentTwo = this.report.contentTwo;
          this.contentThree = this.report.contentThree;
        }else{
          if (this.report.flag == '1'){
            this.getDataT();
          }else {
            this.setYearList();
          }
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    getDataT: function (){
      let deadlineDate = new Date();
      deadlineDate.setMonth(deadlineDate.getMonth());
      let deadlineYear = deadlineDate.getFullYear();
      let deadlineMonth = deadlineDate.getMonth() + 1;
      deadlineMonth = (deadlineMonth < 10 ? '0' + deadlineMonth : deadlineMonth);
      let deadlineDay = deadlineDate.getDate() >= 10 ? deadlineDate.getDate() : '0' + deadlineDate.getDate();
      let dayTime = deadlineYear + '-' + deadlineMonth + '-' + deadlineDay;

      this.report.day = dayTime;
      this.initQueryDate('date1', dayTime);
    },
    initQueryDate(id, startDates) {
      const _this = this;
      if ($('#' + id + '').is(':visible')) {
        $('#' + id + '').daterangepicker({
          'singleDatePicker': true,
          'showDropdowns': true,
          'timePicker': false,
          'timePicker24Hour': false,
          'startDate': startDates, // 设置开始日期
          'opens': 'center',
          'drops': 'down',
          'locale': {
            'format': 'YYYY-MM-DD',
            'separator': ' - ',
            'applyLabel': '确定',
            'cancelLabel': '取消',
            'fromLabel': 'From',
            'toLabel': '到',
            'customRangeLabel': 'Custom',
            'weekLabel': 'W',
            'daysOfWeek': [
              '日',
              '一',
              '二',
              '三',
              '四',
              '五',
              '六'
            ],
            'monthNames': [
              '一月',
              '二月',
              '三月',
              '四月',
              '五月',
              '六月',
              '七月',
              '八月',
              '九月',
              '十月',
              '十一月',
              '十二月'
            ],
            'firstDay': 1
          }
        }, function (start) {
          if (id === 'date1') {
            _this.report.day = start.format('YYYY-MM-DD');
          }
        })
      } else {
        if (this.temp > 50) {
          this.temp = 0;
        }
        this.temp++;
        // 递归 等待dom渲染完毕
        const _this = this;
        setTimeout(function () { _this.initQueryDate(id, startDates); }, 500)
      }
    },
    setYearList() {
      const list = [];
      const date = new Date();
      const y = date.getFullYear();
      for(let i = 0; i <= 10; i++){
        const year = {};
        year.id = y - i;
        year.name = y - i + "年";
        // 一年中的月
        const months = [];
        for(let k = 0; k < 12; k++){
          const mon = {};
          mon.id = k + 1;
          mon.name = mon.id + "月";
          if(mon.id < 10){
            mon.queryDate = year.id + "-0" + mon.id + '-01'
          }else{
            mon.queryDate = year.id + '-' + mon.id + '-01'
          }
          if(year.id == y && k == date.getMonth()){
            this.month = k + 1;
          }
          months.push(mon);
        }
        year.monthList = months;

        // 一年第一个周一
        const firstDay = new Date(year.id, 0, 1);
        while (firstDay.getDay() != 1) {
          firstDay.setDate(firstDay.getDate() + 1);
        }
        const to = new Date(year.id, 0, 1);
        while (to.getDay() != 1) {
          to.setDate(to.getDate() + 1);
        }
        // 下一年的周一
        const lastDay = new Date(year.id + 1, 0, 1);
        while (lastDay.getDay() != 1) {
          lastDay.setDate(lastDay.getDate() + 1);
        }
        // 保存周集合
        const weeks = [];
        let j = 0;
        for(let from = firstDay; from < lastDay; ){
          const week = {};
          week.id = j + 1;
          week.name = "第" + week.id + "周";
          to.setDate(to.getDate() + 7)
          if(date > from && date < to){
            this.week = week.id;
          }
          // 计算每周的开始结束日期
          const startTime = year.id + "-" + ((from.getMonth() + 1) > 9 ? (from.getMonth() + 1) : '0' + (from.getMonth() + 1)) + "-" + (from.getDate() > 9 ? from.getDate() : '0' + from.getDate());
          let endTime = null;
          let time = year.id + "年第" + week.id + "周 " + (from.getMonth() + 1) + "月" + from.getDate() + "日 - ";

          from.setDate(from.getDate() + 6);
          if (from < lastDay) {
            time += (from.getMonth() + 1) + "月" + from.getDate() + "日";
            endTime = year.id+"-"+((from.getMonth() + 1) > 9 ? (from.getMonth() + 1) : '0'+(from.getMonth() + 1)) + "-" + (from.getDate() > 9 ? from.getDate() : '0'+from.getDate());
            if(startTime > endTime){
              endTime = year.id+1+"-"+((from.getMonth() + 1) > 9 ? (from.getMonth() + 1) : '0'+(from.getMonth() + 1)) + "-" + (from.getDate() > 9 ? from.getDate() : '0'+from.getDate());
            }
            from.setDate(from.getDate() + 1);
          } else {
            time += (lastDay.getMonth() + 1) + "月" + lastDay.getDate() + "";
            endTime = year.id+"-"+((lastDay.getMonth() + 1) > 9 ? (lastDay.getMonth() + 1) : '0'+(lastDay.getMonth() + 1)) + "-" + (lastDay.getDate() > 9 ? lastDay.getDate() : '0'+lastDay.getDate());
            if(startTime > endTime){
              endTime = year.id+1+"-"+((lastDay.getMonth() + 1) > 9 ? (lastDay.getMonth() + 1) : '0'+(lastDay.getMonth() + 1)) + "-" + (lastDay.getDate() > 9 ? lastDay.getDate() : '0'+lastDay.getDate());
            }
            lastDay.setDate(lastDay.getDate() - 1);
          }
          week.time = time;
          week.startTime = startTime;
          week.endTime = endTime;
          weeks.push(week);
          j++
        }
        year.weekList = weeks;
        list.push(year);
      }
      this.yearList = list;
    },
    getWeek() {
      this.weekList = [];
      for(let i in this.yearList){
        if(this.report.year == this.yearList[i].id){
          this.weekList = this.yearList[i].weekList;
          break;
        }
      }
    },
    getMonth() {
      this.monthList = [];
      for(let i in this.yearList){
        if(this.report.year == this.yearList[i].id){
          this.monthList = this.yearList[i].monthList;
          break;
        }
      }
    },
    setWeek() {
      for(let i=0;i<this.weekList.length;i++){
        if(this.report.week == this.weekList[i].id){
          this.report.day = this.weekList[i].startTime;
          break;
        }
      }
    },
    setMonth() {
      for(let i=0;i<this.monthList.length;i++){
        if(this.report.month == this.monthList[i].id){
          this.report.day = this.monthList[i].queryDate
          break;
        }
      }
    },
    openFilter() {
      this.$refs['filter'].show();
    },
    cancelFilter() {
      this.shareIds = "";
      this.$refs['filter'].hide();
    },
    applyFilter() {
      this.$refs['filter'].hide();
    },
    setEmpVal(item) {
      this.clickEmp(item.recordId);
    },
    isIdInString(targetId) {
      let idString = this.shareIds;
      if (!idString) {
        return false;
      }
      const idArray = idString.split(',').map(id => id.trim());
      return idArray.includes(targetId);
    },
    clickEmp(id, flag) {
      let idString = this.shareIds;
      let resIds = "";
      if (this.isIdInString(id, flag)){
        const parts = idString.split(',');
        for (const part of parts) {
          if (part.trim() !== id) {
            resIds = resIds ? resIds + "," + part.trim() : part.trim();
          }
        }
      }else {
        resIds = idString ? idString + "," + id : id;
      }
      this.shareIds = resIds;
    }
  }
}
</script>
