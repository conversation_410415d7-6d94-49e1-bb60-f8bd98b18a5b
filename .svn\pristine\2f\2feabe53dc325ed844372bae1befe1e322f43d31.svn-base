<template>
  <div>
    <div class="bg-white rounded-sm p-3 mb-3">
      <div class="d-flex justify-content-between align-items-center text-muted">
        <div>提交人：{{audit && audit.recordId ? audit.name : userMsg.empName}}</div>
        <div>创建时间：{{audit.createdDate}}</div>
      </div>
      <section>
        <CUSTOMMAIN :audit="audit" :showAuditData = showAuditData></CUSTOMMAIN>
      </section>
    </div>
  </div>
</template>
<script>
import CUSTOMMAIN from "@/view/pages/wx/kybsoft/work/audit/details/childPage/customPage/custom_main";
export default {
  name: "showAudit",
  props: {
    audit: Object,
    configList: Array,
    showAuditData: [Object, Array]
  },
  components: {
    CUSTOMMAIN
  },
  data() {
    return{
      showHtml: "",
      temp: 0,
    }
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.employeeId){
        return;
      }
    }else {
      alert("请重新进入公众号");
    }
  },
}
</script>