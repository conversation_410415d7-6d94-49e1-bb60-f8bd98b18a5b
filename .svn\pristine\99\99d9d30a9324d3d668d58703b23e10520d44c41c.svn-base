<template>
  <div class="bg-white rounded-sm p-2">
    <div class="d-flex justify-content-between align-items-center pb-2 border-bottom">
      <div class="font-weight-bolder">附件</div>
      <button class="btn btn-primary px-2 py-1" v-on:click="chooseImg" v-if="editStatus == '2'">上传附件</button>
    </div>
    <div v-if="localIds && localIds.length > 0 && editStatus == '2'">
      <template v-for="(item,index) in localIds">
        <div :key="index">
          <div class="d-flex justify-content-between align-items-center pt-2 pb-2 border-bottom">
            <div><img :src="item" class="w-25" v-on:click="preview(item)"></div>
            <button class="btn btn-outline-primary px-3 py-1" v-on:click="deleteImg(item,index)">删除</button>
          </div>
        </div>
      </template>
    </div>
    <div v-if="fileEntity && fileEntity.attachList && fileEntity.attachList.length > 0">
      <template v-for="(item,index) in fileEntity.attachList">
        <div :key="index">
          <div class="d-flex justify-content-between align-items-center pt-2 pb-2 border-bottom">
            <div class="text-truncate text-primary" style="width: 20rem;" v-on:click="showFile(item)">{{item.name}}</div>
            <button class="btn btn-outline-danger px-2 py-1" v-on:click="deleteImg(item,index)" v-if="editStatus == '2'">删除</button>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import wx from 'weixin-js-sdk';
import { getWxMsg, showPreview } from '@/assets/js/utils/wechatImgUtils';
export default {
  name: "fileManage",
  props: {
    fileEntity: Object,
    entityFlag: String,
    editStatus: String
  },
  created:function(){
    if (process.env.NODE_ENV === "production"){
      getWxMsg(window.location.href.split('#')[0], wx);
    }
  },
  data() {
    return {
      localIds: [],
      serverList: [],
    }
  },
  methods: {
    showFile(file){
      const url = file.downloadUrl;
      if (!url) {
        alert("文件已失效，请刷新重试");
        return;
      }
      const flag = showPreview(file);
      if (flag){
        if (flag == '1'){
          this.localIds.push(url);
          this.preview(url);
        }else if (flag == '2'){
          window.location.href = url;
        }else if (flag == '3'){
          window.location.href = "https://view.officeapps.live.com/op/view.aspx?src=" + encodeURIComponent(url);
        }
      }
    },
    preview:function(item){
      if(window.wx)
      {
        window.wx.previewImage({
          current: item, // 当前显示图片的http链接
          urls: this.localIds // 需要预览的图片http链接列表
        });
      }else{
        window.location.href = item;
      }
    },
    chooseImg:function(){
      wx.chooseImage({
        count: 9, // 默认9
        sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
          if(this.localIds && this.localIds.length > 0){
            for(let i=0; i< res.localIds.length; i++){
              this.localIds.push(res.localIds[i])
            }
          }else{
            this.localIds = res.localIds // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
          }
          // 上传图片
          this.serverList = []
          this.localIdsCopy = []
          for (let i = 0; i < this.localIds.length; i++) {
            this.localIdsCopy.push(this.localIds[i])
          }
          this.uploadImage()
        }
      });
    },
    uploadImage:function(){
      const vm = this;
      if(vm.localIdsCopy && vm.localIdsCopy.length > 0){
        wx.uploadImage({
          localId: vm.localIdsCopy.pop(), // 需要上传的图片的本地ID，由chooseImage接口获得
          isShowProgressTips: 1, // 默认为1，显示进度提示
          success: function (res) {
            vm.serverList.push(res.serverId)
            vm.uploadImage()
          }
        });
      }
    },
    deleteImg:function(item,index){
      if (this.fileEntity && this.fileEntity.recordId)
      {
        let url = "";
        switch (this.entityFlag)
        {
          case "audit":
            url = "f/wechat/kybsoftOA/delAuditFile"
            break;
          case "dayThing":
            url = "f/wechat/kybsoftOA/delDayThingFile"
            break;
          case "report":
            url = "f/wechat/kybsoftOA/delReportFile"
            break;
          case "task":
            url = "f/wechat/kybsoftOA/delTaskFile";
            break;
          }
        this.$parent.enableLoadFlag(true);
        this.$axios.fetchPost(url,item).then(result => {
          if (result && result.data){
              alert("删除成功！");
              this.fileEntity.attachList.splice(index,1);
          }
          this.$parent.enableLoadFlag(false);
        }).catch(err => {console.log(err);});
      }else{
        this.localIds.splice(index,1)
      }
    },
  }
}
</script>
