import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "@/core/services/store";
import axios from '@/core/services';

Vue.config.productionTip = false;
Vue.prototype.$axios = axios;

import '@/view/pages/utils/daterangepicker.css';
import 'daterangepicker';

import '@/view/pages/utils/common.css';

// Global 3rd party plugins
import "popper.js";
import "tooltip.js";
import PerfectScrollbar from "perfect-scrollbar";
window.PerfectScrollbar = PerfectScrollbar;
import ClipboardJS from "clipboard";
window.ClipboardJS = ClipboardJS;

// Vue 3rd party plugins
import vuetify from "@/core/plugins/vuetify";
import "@/core/plugins/portal-vue";
import "@/core/plugins/bootstrap-vue";
import "@/core/plugins/perfect-scrollbar";
import "@/core/plugins/highlight-js";
import "@/core/plugins/inline-svg";
import "@/core/plugins/apexcharts";
import "@/core/plugins/metronic";
import "@mdi/font/css/materialdesignicons.css";
import {LOGOUT, SETAUTH} from "@/core/services/store/auth.module";
import {SHOW_PAGE_LOADING, HIDE_PAGE_LOADING} from "@/core/services/store/loading.module";

router.beforeEach((to, from, next) => {
  if (to.name == "business"){
    window.localStorage.setItem('wxEntry', "1");// 设置为大生态圈的状态
  }else if (to.name == "scanWork"){
    window.localStorage.setItem('wxEntry', "2");// 设置为生产过数的状态
  }else if (to.name == "logistics"){
    window.localStorage.setItem('wxEntry', "3");// 设置为物流系统的状态
  }else if (to.name == "userAuth"){
    window.localStorage.setItem('wxEntry', "4");// 设置为账户中心的状态
  }else if (to.name == "README"){
    window.localStorage.setItem('wxEntry', "5");// 设置为简介的状态
  }else if (to.name == "work"){
    window.localStorage.setItem('wxEntry', "6");// 设置为小助手的状态
  }
  if (!store.getters.isAuthenticated){
    if (to.path != "/callback"){
      // 前往授权页面
      if (process.env.NODE_ENV === "production"){
        // 线上环境
        const appId = 'wx7837906843c7ea03';
        const redirectUri = encodeURIComponent(window.location.origin + '/callback');
        const scope = 'snsapi_userinfo';
        const state = 'STATE';
        const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`;
        window.location.href = authUrl;
      }else {
        // 本地测试
        router.push("/callback");
      }
    }else {
      // 前往路由页面
      if (to.path != from.path){
        next();
      }
    }
  }else {
    // 二次验证用户数据
    if (store.getters.currentUser && store.getters.currentUser.recordId
        && store.getters.currentUser.phone && store.getters.currentUser.defaultDb && store.getters.currentUser.openId){
      let query = {};
      query.recordId = store.getters.currentUser.recordId;
      query.phone = store.getters.currentUser.phone;
      query.defaultDb = store.getters.currentUser.defaultDb;
      query.groupManageId = store.getters.currentUser.groupManageId;
      query.openId = store.getters.currentUser.openId;
      query.cloud = '4';
      axios.fetchPost("hr/user/getUser", query).then(result => {
        if(result.data && result.data.recordId && result.data.openId && result.data.defaultDb){
          // 重新设置用户
          store.dispatch(SETAUTH, result.data);
          if (to.path != from.path){
            next();
          }
        }else{
          // 退出，跳往登录页面
          store.dispatch(LOGOUT).then(() => router.push({ name: store.getters.currentEntry }));
        }
      }).catch(() => {
        // 退出，提示请刷新重试
        store.dispatch(LOGOUT).then(() => router.push({ name: store.getters.currentEntry }));
      });
    }else {
      store.dispatch(LOGOUT).then(() => router.push({ name: store.getters.currentEntry }));
    }
  }
  // Scroll page to top on every route change
  setTimeout(() => {
    window.scrollTo(0, 0);
  }, 100);
});

new Vue({
  router,
  store,
  vuetify,
  render: h => h(App)
}).$mount("#app");
