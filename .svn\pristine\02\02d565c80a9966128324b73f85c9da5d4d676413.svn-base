<template>
  <div class="bg-white manufacturing-center rounded-xl">
    <h2 class="title">制造中心</h2>
    <p class="subtitle">应用领域：汽车电子、高功率电源、工业工控、户外照明、TV背光源、室内照明、消费电子</p>

    <div class="content-container">
      <div class="image-container"><img :src="imageUrl" class="w-100"></div>
      <div class="text-container">
        <p style="word-break: break-all;">{{ description }}</p>
      </div>
    </div>

    <ul class="features">
      <li v-for="(feature, index) in features" :key="index">
        <span class="font-weight-bolder">{{ feature }}</span>
      </li>
    </ul>

    <a :href="telLink" class="contact-btn">
      <i class="fa fa-phone mr-2"></i> 立即咨询
    </a>
  </div>
</template>

<script>
export default {
  name: 'ManufacturingCenter',
  data() {
    return {
      imageUrl: process.env.BASE_URL + 'media/apply/5.jpg',
      description: '领德制造中心占地 80,000 平米，现拥有全套国际生产标准的自动化生产设备、全套的高阶检测设备和综合的 ISO 质量管理体系，我们月生产产能已达 250,000 平米。经过二十年的发展和工业经验的积累，我们已成为一家有影响力、设备精良、管理严格、质量优良的专业线路板制造商，公司的行业知名度、优质客户认可度较高。',
      features: [
        '80000㎡制造中心占地面积',
        '250000㎡月产能/月',
        '20年发展和工业经验',
        '全套国际标准自动化生产设备',
        '完善的ISO质量管理体系'
      ],
      telLink: 'tel:13714723568'
    }
  }
}
</script>

<style scoped>
/* 主要容器 */
.manufacturing-center {
  margin: 0 auto;
  padding: 16px;
}

/* 标题部分 */
.title {
  text-align: center;
  font-size: 20px;
  font-weight: 700;
  color: #222;
  margin-bottom: 12px;
  position: relative;
}

/* 副标题 */
.subtitle {
  text-align: center;
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
}

/* 内容区域 - 图片和文字一行显示 */
.content-container {
  display: flex;
  flex-wrap: nowrap;
  gap: 16px;
  margin-bottom: 16px;
}

/* 图片区域 */
.image-container {
  border-radius: 10px;
  overflow: hidden;
}

/* 文字区域 */
.text-container {
  flex: 1;
  background-color: #fff;
  border-radius: 10px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}

.text-container p {
  font-size: 14px;
  color: #333;
  line-height: 1.7;
  margin-bottom: 0;
}

/* 优势列表 */
.features {
  list-style: none;
  margin-bottom: 16px;
  padding-left: 0;
}

.features li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-left: 22px;
  position: relative;
}

.features li::before {
  content: '•';
  color: #165DFF;
  font-size: 18px;
  position: absolute;
  left: 0;
  top: 1px;
}

.features li span {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
}

/* 联系按钮 */
.contact-btn {
  display: block;
  width: 100%;
  background-color: #165DFF;
  color: white;
  text-align: center;
  padding: 12px 0;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  box-shadow: 0 4px 12px rgba(22, 93, 255, 0.15);
  transition: all 0.3s;
}

.contact-btn:hover {
  background-color: #0E48CC;
  box-shadow: 0 6px 16px rgba(22, 93, 255, 0.2);
  color: white;
  text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .content-container {
    flex-direction: column;
  }

  .image-container, .text-container {
    flex: 100%;
  }

  .image-container {
    height: 200px;
  }
}

@media (max-width: 400px) {
  .text-container p {
    font-size: 13px;
  }

  .features li span {
    font-size: 13px;
  }
}
</style>
