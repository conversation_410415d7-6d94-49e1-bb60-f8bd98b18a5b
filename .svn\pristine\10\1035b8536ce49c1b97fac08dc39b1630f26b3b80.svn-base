<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">设备维保</h3>
    </div>
    <div class="card">
      <div class="card-body p-3">
        <div class="d-flex justify-content-center" style="padding-top: 3rem;padding-bottom: 3rem;">
          <button class="btn btn-primary btn-lg" style="border-radius: 50%;height: calc(3em + 10rem + 10px);width: calc(3em + 10rem + 10px);" v-on:click="wxRcanQRcode"><h1>扫一扫</h1></button>
        </div>
        <div class="pt-3">
          <div class="font-weight-bolder">申请维保</div>
          <div class="row pt-3 pb-3">
            <template v-for="item in applyList">
              <div :key="item.id" class="col-3 text-center p-1" v-on:click="router_page(item.routerName, item.num)">
                <a :class="['btn btn-icon', item.doFlag ? 'pulse pulse-primary' : '', item.color]"><i :class="item.iconClass"></i><span class="pulse-ring" v-if="item.doFlag"></span></a>
                <div class="pt-1">{{ item.name }}</div>
              </div>
            </template>
          </div>
        </div>
        <div class="pt-3 border-top" v-if="comFlag">
          <div class="font-weight-bolder">维保公司</div>
          <div class="row pt-3">
            <template v-for="item in comList">
              <div :key="item.id" class="col-3 text-center p-1" v-on:click="router_page(item.routerName, item.num)">
                <a :class="['btn btn-icon', item.doFlag ? 'pulse pulse-primary' : '', item.color]"><i :class="item.iconClass"></i><span class="pulse-ring" v-if="item.doFlag"></span></a>
                <div class="pt-1">{{ item.name }}</div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div style="height: 100px;"></div>
    <b-modal ref="addManualRepair" title="无码报修" hide-footer>
      <div>
        <div v-if="userMsg.icloudCompanyList && userMsg.icloudCompanyList.length > 0">
          <div><span style="color: red;">*</span>申请公司:</div>
          <select class="form-control" v-model="manualRepairT.applyCompanyId">
            <template v-for="item in userMsg.icloudCompanyList">
              <option :key="item.recordId" :value="item.recordId" v-if="!item.maintenanceCompany">
                {{item.name}}
              </option>
            </template>
          </select>
        </div>
        <div class="pt-3">
          <div><span style="color: red;">*</span>报修公司:</div>
          <select class="form-control" v-model="manualRepairT.repairCompanyId">
            <template v-for="item in repairCompanyList">
              <option :key="item.recordId" :value="item.recordId">
                {{item.name}}
              </option>
            </template>
          </select>
        </div>
        <div class="pt-3">
          <div><span style="color: red;">*</span>问题设备名称及规格:</div>
          <input type="text" class="form-control" placeholder="请输入设备名称及规格" v-model="manualRepairT.deviceName"/>
        </div>
        <div class="pt-3">
          <div class="text-danger">问题描述:</div>
          <textarea type="textarea" class="form-control" placeholder="请输入设备问题" v-model="manualRepairT.problem"/>
        </div>
        <div class="pt-3">
          <div>其它备注:</div>
          <textarea type="textarea" class="form-control" v-model="manualRepairT.remark"/>
        </div>
        <div class="d-flex justify-content-end pt-7">
          <button type="button" class="btn btn-primary w-100" v-on:click="saveConfirmButton">申请</button>
        </div>
      </div>
    </b-modal>
  </div>
</template>

<script>
import wx from "weixin-js-sdk";
import { getWxMsg } from '@/assets/js/utils/wechatScanUtils';
export default {
  name: "mainMantence",
  created:function(){
    if (process.env.NODE_ENV === "production"){
      getWxMsg(window.location.href.split('#')[0], wx);
    }
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.icloudCompanyList || this.userMsg.icloudCompanyList.length == 0){
        alert("请先去我的菜单中-企业信息-创建企业，无公司信息不可申请维保服务");
        return;
      }
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return {
      userMsg: {},
      applyList: [
        {id: 1, name: "无码报修", iconClass: "flaticon-light", color: "btn-light-primary", doFlag: "1", routerName: "t1"},
        {id: 2, name: "正在维保", iconClass: "flaticon-clock-1", color: "btn-light-success", routerName: "/b/mainMantence/waiteRepair", num: 2},
        {id: 3, name: "完成清单", iconClass: "flaticon2-protected", color: "btn-light-success", routerName: "/b/mainMantence/waiteRepair", num: 3},
        {id: 4, name: "评价投诉", iconClass: "flaticon-app", color: "btn-light-danger", doFlag: "1", routerName: "/b/mainMantence/waiteRepair", num: 8},
        {id: 5, name: "工时套餐", iconClass: "flaticon-attachment", color: "btn-light-danger", routerName: "/b/mainMantence/timePackage", num: 10},
      ],
      comFlag: false,
      comList: [
        {id: 1, name: "在线维保", iconClass: "flaticon2-bell-5", color: "btn-light-danger", doFlag: "1", routerName: "/b/mainMantence/waiteRepair", num: 1},
        {id: 2, name: "我的任务", iconClass: "flaticon2-gear", color: "btn-light-danger", doFlag: "1", routerName: "/b/mainMantence/waiteRepair", num: 5},
        {id: 3, name: "设备录入", iconClass: "flaticon-add", color: "btn-light-primary", routerName: "/b/mainMantence/inputEquipment", num: 6},
        {id: 4, name: "维保清单", iconClass: "flaticon-medal", color: "btn-light-primary", routerName: "/b/mainMantence/waiteRepair", num: 4},
        {id: 5, name: "设备管理", iconClass: "flaticon-avatar", color: "btn-light-primary", routerName: "/b/mainMantence/equipmentManage", num: 9},
        {id: 6, name: "工时报表", iconClass: "flaticon-analytics", color: "btn-light-primary", routerName: "/b/mainMantence/timeReport", num: 11},
      ],
      manualRepairT:{},
      repairCompanyList:[],
    }
  },
  methods: {
    //获取申请公司、维保人
    loadData:function(){
      if (this.userMsg && this.userMsg.icloudCompanyList && this.userMsg.icloudCompanyList.length > 0){
        for (let i=0;i<this.userMsg.icloudCompanyList.length;i++){
          if (this.userMsg.icloudCompanyList[i].maintenanceCompany && this.userMsg.icloudCompanyList[i].maintenanceCompany == '3'){
            this.comFlag = true;
            break;
          }
        }
      }
    },
    router_page(routerName, num) {
      if (routerName == "t1"){
        this.addManualRepair();
      }else {
        this.$router.push({path: routerName, query: {num : num}});
      }
    },
    addManualRepair:function(){
      this.manualRepairT = {};
      this.repairCompanyList = [];
      this.$parent.enableLoadFlag(true);
      // 查询维保公司
      this.$axios.fetchPost("f/wechat/repair/getRepairComList").then(result => {
        if(result.data) {
          this.repairCompanyList = result.data;
        }
        // 申请公司和报修公司都默认第一家
        for (let i=0;i<this.userMsg.icloudCompanyList.length;i++){
          if (!this.userMsg.icloudCompanyList[i].maintenanceCompany){
            this.manualRepairT.applyCompanyId = this.userMsg.icloudCompanyList[i].recordId;
            break;
          }
        }
        this.manualRepairT.repairCompanyId = this.repairCompanyList[0].recordId;
        this.$refs['addManualRepair'].show();
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
    saveConfirmButton:function()
    {
      if (!this.manualRepairT){
        alert("请录入维保申请数据!");
        return;
      }
      if(!this.manualRepairT.repairCompanyId){
        alert("请选择维保公司!");
        return;
      }
      if(!this.manualRepairT.applyCompanyId){
        alert("请选择申请公司!");
        return;
      }
      if(!this.manualRepairT.deviceName){
        alert("请录入设备及规格!");
        return;
      }
      if(!this.manualRepairT.problem){
        alert("请描述遇到的问题!");
        return;
      }
      this.manualRepairT.userId = this.userMsg.recordId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/repair/addProblemDescription",this.manualRepairT).then(result => {
        if (result.data === "success"){
          alert("申请维保服务成功!");
          this.$refs['addManualRepair'].hide();
        }else{
          alert(result.data);
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {alert(err)});
    },
    wxRcanQRcode: function () {
      wx.scanQRCode({
        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
        scanType: ['qrCode'], // 可以指定扫二维码qrCode还是一维码barCode，默认二者都有
        success: function (res) {
          const result = res.resultStr // 当needResult 为 1 时，扫码返回的结果
          // 进入明细
          if (result) {
            let resA = result.split("A");
            if (resA && resA.length > 1) {
              let resB = resA[1].split("B");
              if (resB && resB.length > 1) {
                let resC = resB[1].split("C");

                let item = {};
                item.groupId = resB[0];
                item.companyId = resC[0];
                item.materialId = resC[1];
                item.userId = this.userMsg.recordId
                this.$router.push({path: '/maintenanceCount',
                  query: {
                    groupId: resB[0],
                    companyId: resC[0],
                    materialId: resC[1],
                    userId:this.userMsg.recordId
                  }
                });
                return
              }
            } else {
              alert("请刷新重试" + result)
            }
          }
        },
        error: function () {
          console.log('系统错误')
        }
      })
    },
  }
}
</script>
