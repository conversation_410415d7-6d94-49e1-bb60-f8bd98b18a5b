<template>
  <div>
    <div class="bg-white rounded-sm p-3 mb-3">
      <div class="d-flex justify-content-between align-items-center text-muted">
        <div>提交人：{{audit && audit.recordId ? audit.name : userMsg.empName}}</div>
        <div v-if="audit.createdDate">创建时间：{{audit.createdDate}}</div>
      </div>
      <section v-if="showList">
        <template v-for="config in configList">
          <div class="row" :key="config.recordId" v-if="!config.detailStatus && config.pageLevel == 1">
            <template v-for="item in showList">
              <div :class="[item.rowFlag, 'pt-3']" :key="item.recordId" v-if="item.allId == config.recordId">
                <CONFIG :item="item" :val="item" :editStatus="editStatus"></CONFIG>
                <div v-if="item.inputType == 16">
                  <div class="d-flex justify-content-between align-items-center pb-3">
                    <div class="font-weight-bolder">{{item.name}}</div>
                    <button class="btn btn-outline-primary px-2 py-1" v-if="editStatus == 2" v-on:click="addDetail(item)">添加</button>
                  </div>
                  <template v-for="(auditTypeVal, index) in detailList">
                    <div class="pb-3 border-bottom" :key="index" v-if="auditTypeVal.configId == item.recordId">
                      <template v-for="auditType in configList">
                        <div class="row pb-2" :key="auditType.recordId" v-if="auditType.enName == item.enName && auditType.pageLevel == 1">
                          <template v-for="sheet in showList">
                            <div :class="[sheet.rowFlag, 'pt-1']" :key="sheet.recordId" v-if="sheet.allId == auditType.recordId">
                              <div v-if="auditTypeVal && auditTypeVal.list">
                                <template v-for="(val, valIndex) in auditTypeVal.list">
                                  <div :key="valIndex" v-if="val.configId == sheet.recordId">
                                    <CONFIG :item="sheet" :val="val" :editStatus="editStatus"></CONFIG>
                                  </div>
                                </template>
                              </div>
                            </div>
                          </template>
                        </div>
                      </template>
                      <div class="d-flex justify-content-end" v-if="editStatus == 2">
                        <button class="btn btn-outline-danger px-2 py-1" v-on:click="delDetail(index)">删除</button>
                      </div>
                    </div>
                  </template>
                </div>
                <div v-else-if="item.inputType == 17">
                  <div class="d-flex justify-content-between align-items-center">
                    <div class="font-weight-bolder">{{item.name}}：{{item.value}}</div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </template>
      </section>
    </div>
  </div>
</template>
<script>
import $ from "jquery";
import CONFIG from "@/view/pages/wx/kybsoft/work/audit/details/utils/config";
export default {
  name: "addAudit",
  components: {
    CONFIG,
  },
  props: {
    audit: Object,
    organizationName: String,
    editStatus: String,
    configList: Array,
    list: Array,
    expenseList: Array,
    restList: Array,
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.employeeId){
        return;
      }
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  data() {
    return{
      userMsg: {},
      detailList: [],
      idCounter: 0,
      showList: [],
      loadNames: "",
      temp: 0,
    }
  },
  methods: {
    loadData() {
      if (this.configList && this.configList.length > 0){
        // 赋值
        this.setConfigVal();
        for(let config of this.configList) {
          // 代表只初始第一层的数据，明细要设置状态隐藏
          if (!config.detailStatus){
            for(let item of this.showList) {
              if (item.inputType && item.pageLevel == 2 && item.allId == config.recordId){
                item.id = ++this.idCounter;
                this.initDate(item, item.value);
              }
            }
          }
        }
      } else {
        if (this.temp > 50) {
          this.temp = 0;
        }
        this.temp++;
        // 递归 等待dom渲染完毕
        const _this = this;
        setTimeout(function () { _this.loadData(); }, 500)
      }
    },
    setConfigVal() {
      this.showList = [];
      for(let sheet of this.configList) {
        if (sheet.pageLevel && sheet.pageLevel == 2){
          let showType = {};
          showType = this.setBindVal(showType, sheet);
          // 值绑定
          const name = this.getConfigVal(sheet.prompt,"obj");
          if (name){
            // 指定表
            showType.value = this.audit[name];
          }else if(this.list && this.list.length > 0) {
            for(let sheetTwo of this.list) {
              if (sheetTwo.configId == sheet.recordId){
                // 未指定表
                showType.value = sheetTwo.value;
                break;
              }
            }
          }
          // 加载处理
          const nameTwo = this.getConfigVal(sheet.enName,"load");
          if (nameTwo){
            // 加载赋值
            let flag = true;
            for(let sheetItem of this.showList) {
              if (sheetItem && sheetItem.value && sheetItem.description && sheetItem.description == sheet.enName){
                this.loadDictValueList(showType, nameTwo, sheetItem.value);
                flag = false;
              }
            }
            if (flag){
              this.loadDictValueList(showType, nameTwo, "");
            }
          }else {
            // 数据字典配置赋值
            for(let dict of this.configList) {
              if (dict.pageLevel == 3 && dict.allId == sheet.recordId){
                const dictVal = {};
                dictVal.id = dict.id ? dict.id : dict.recordId;
                dictVal.name = dict.value;
                showType.dictValueList.push(dictVal);
              }
            }
          }
          this.showList.push(showType);
        }
      }
      // 刷新值
      this.setDetailList();
    },
    childLoadMsg(item, value) {
      if (item.description && value){
        const name = this.getConfigVal(item.description,"load");
        if (name){
          for(let sheet of this.showList) {
            const nameTwo = this.getConfigVal(sheet.enName,"load");
            if (nameTwo && nameTwo == name){
              this.loadDictValueList(sheet, name, value);
            }
          }
        }
      }
    },
    setDetailList() {
      this.detailList = [];
      for(let sheet of this.showList) {
        if (sheet.inputType == 16) {
          const name = this.getConfigVal(sheet.enName,"mx");
          if (name){
            for(let sheetTwo of this.configList) {
              if (sheetTwo.pageLevel && sheetTwo.pageLevel == 1) {
                const nameTwo = this.getConfigVal(sheetTwo.enName,"mx");
                if (nameTwo && name == nameTwo){
                  const typeId = sheet.recordId;
                  const recordId = sheetTwo.recordId;
                  const nameThree = this.getConfigVal(sheetTwo.prompt,"table");
                  if (nameThree) {
                    // 指定表
                    if (nameThree == "expense" && this.expenseList && this.expenseList.length > 0){
                      for(let obj of this.expenseList) {
                        this.setDetailValueList(typeId, recordId, obj, 1, sheetTwo.prompt);
                      }
                    }else if (nameThree == "rest" && this.restList && this.restList.length > 0){
                      for(let obj of this.restList) {
                        this.setDetailValueList(typeId, recordId, obj, 1, sheetTwo.prompt);
                      }
                    }
                  }else if(this.list && this.list.length > 0) {
                    // 未指定表
                    for(let obj of this.list) {
                      if (obj.configId == typeId){
                        this.setDetailValueList(typeId, recordId, obj, 2, sheetTwo.prompt);
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    setDetailValueList(typeId, recordId, obj, flag, prompt) {
      const detail = {};
      detail.configId = typeId;
      detail.prompt = prompt;
      detail.list = [];
      for(let sheet of this.showList) {
        if (sheet.allId && sheet.allId == recordId){
          const sheetVal = {};
          sheetVal.configId = sheet.recordId;
          sheetVal.prompt = sheet.prompt;
          if (flag == 1){
            const name = this.getConfigVal(sheet.prompt,"obj");
            sheetVal.value = obj[name];
          }else if (flag == 2) {
            for(let configVal of this.list) {
              if (configVal.configId == sheet.recordId && configVal.detailId == obj.recordId){
                sheetVal.value = configVal.value;
                break;
              }
            }
          }else if (flag == 3) {
            sheetVal.value = "";
          }
          sheetVal.inputType = sheet.inputType;
          sheetVal.id = ++this.idCounter;
          detail.list.push(sheetVal);
          sheet.id = sheetVal.id;
          this.initDate(sheet, sheetVal.value);
        }
      }
      this.detailList.push(detail);
    },
    addDetail(item) {
      const name = this.getConfigVal(item.enName,"mx");
      if (name) {
        for(let sheet of this.configList) {
          if (sheet.pageLevel && sheet.pageLevel == 1){
            const nameTwo = this.getConfigVal(sheet.enName,"mx");
            if (nameTwo && name == nameTwo){
              const typeId = item.recordId;
              const recordId = sheet.recordId;
              this.setDetailValueList(typeId, recordId, "", 3, sheet.prompt)
            }
          }
        }
      }
    },
    delDetail(index) {
      this.detailList.splice(index, 1);
    },
    getConfigVal(name, type) {
      if (!name){
        return "";
      }
      const strings = name.split("-");
      if (strings && strings.length > 1 && strings[0] == type){
        return strings[1];
      }
      return "";
    },
    loadDictValueList (showType, nameTwo, value) {
      if (this.loadNames.includes("[" + nameTwo + "]")){
        return;
      }
      let url = "";
      const query = {};
      if (nameTwo == "emp"){
        query.departId = this.userMsg.departId;
        url = "f/wechat/kybsoftOA/getEmpList";
      }else if (nameTwo == "position"){
        query.departId = this.userMsg.departId;
        url = "hr/recruit/getRecruitingPositionList";
      }else if (nameTwo == "company"){
        query.departId = this.userMsg.departId;
        url = "f/wechat/kybsoftOA/getCompanyList";
      }else if (nameTwo == "saleCompany"){
        query.departId = this.userMsg.departId;
        url = "f/wechat/kybsoftOA/getSaleCompanyList";
      }else if (nameTwo == "category"){
        url = "f/wechat/kybsoftOA/getCategoryList";
      }else if (nameTwo == "saleCustomer" && value){
        query.companyId = value;
        url = "f/wechat/kybsoftOA/getSaleCustomerList";
      }else if (nameTwo == "dept" && value){
        query.departId = value;
        url = "f/wechat/kybsoftOA/getCompanyList";
      }
      if (!url){
        return;
      }
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost(url, query).then(result => {
        if (result && result.data) {
          result.data.forEach(obj => {
            obj.id = obj.id ? obj.id : obj.recordId
          });
          for (let sheet of this.showList){
            const sheetName = this.getConfigVal(sheet.enName,"load");
            if (sheetName == nameTwo){
              sheet.dictValueList = result.data;
            }
          }
          if (!value){
            this.loadNames = this.loadNames ? this.loadNames + "[" + nameTwo + "]" : "[" + nameTwo + "]";
          }
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    initDate(item, val) {
      if (!item.inputType || !item.id){
        return;
      }
      const id = "date" + item.id;
      const value = val ? val : new Date();
      if (item.inputType == 8){
        // 日期（时分秒）
        this.initQueryDateTime(id, value);
      }
      if (item.inputType == 9){
        // 日期（天）
        if (val){
          this.initQueryDateDay(id, val);
        }else {
          this.getDataT(id);
        }
      }
    },
    getDataT: function (id){
      let deadlineDate = new Date();
      deadlineDate.setMonth(deadlineDate.getMonth());
      let deadlineYear = deadlineDate.getFullYear();
      let deadlineMonth = deadlineDate.getMonth() + 1;
      deadlineMonth = (deadlineMonth < 10 ? '0' + deadlineMonth : deadlineMonth);
      let deadlineDay = deadlineDate.getDate() >= 10 ? deadlineDate.getDate() : '0' + deadlineDate.getDate();
      let dayTime = deadlineYear + '-' + deadlineMonth + '-' + deadlineDay;
      this.initQueryDateDay(id, dayTime);
    },
    setBindVal(showType, sheet){
      showType.dictValueList = [];
      showType.recordId = sheet.recordId;
      showType.allId = sheet.allId;
      showType.name = sheet.name;
      showType.pageLevel = sheet.pageLevel;
      showType.prompt = sheet.prompt;
      showType.remark = sheet.remark;
      showType.rowFlag = sheet.rowFlag;
      showType.inputType = sheet.inputType;
      showType.sourceId = sheet.sourceId;
      showType.disableFlag = sheet.disableFlag;
      showType.sortNum = sheet.sortNum;
      showType.unitType = sheet.unitType;
      showType.inputWidth = sheet.inputWidth;
      showType.configId = sheet.configId;
      showType.promptPlace = sheet.promptPlace;
      showType.promptName = sheet.promptName;
      showType.promptWidth = sheet.promptWidth;
      showType.listFlag = sheet.listFlag;
      showType.alias = sheet.alias;
      showType.enName = sheet.enName;
      showType.description = sheet.description;
      return showType;
    },
    initQueryDateDay: function (id, startDates) {
      if ($('#' + id + '').is(':visible')) {
        $('#' + id + '').daterangepicker({
          'singleDatePicker': true,
          'showDropdowns': true,
          'timePicker': false,
          'timePicker24Hour': false,
          'startDate': startDates, // 设置开始日期
          'opens': 'center',
          'drops': 'down',
          'locale': {
            'format': 'YYYY-MM-DD',
            'separator': ' - ',
            'applyLabel': '确定',
            'cancelLabel': '取消',
            'fromLabel': 'From',
            'toLabel': '到',
            'customRangeLabel': 'Custom',
            'weekLabel': 'W',
            'daysOfWeek': [
              '日',
              '一',
              '二',
              '三',
              '四',
              '五',
              '六'
            ],
            'monthNames': [
              '一月',
              '二月',
              '三月',
              '四月',
              '五月',
              '六月',
              '七月',
              '八月',
              '九月',
              '十月',
              '十一月',
              '十二月'
            ],
            'firstDay': 1
          }
        }, function () {})
      } else {
        if (this.temp > 50) {
          this.temp = 0;
        }
        this.temp++;
        // 递归 等待dom渲染完毕
        const _this = this;
        setTimeout(function () { _this.initQueryDateDay(id, startDates); }, 500)
      }
    },
    initQueryDateTime: function (id, startDates) {
      if ($('#' + id + '').is(':visible')) {
        $('#' + id + '').daterangepicker({
          'singleDatePicker': true,
          'showDropdowns': true,
          'timePicker': true,
          'timePicker24Hour': true,
          'startDate': startDates, // 设置开始日期
          'opens': 'center',
          'drops': 'down',
          'locale': {
            'format': 'YYYY-MM-DD HH:mm:ss',
            'separator': ' - ',
            'applyLabel': '确定',
            'cancelLabel': '取消',
            'fromLabel': 'From',
            'toLabel': '到',
            'customRangeLabel': 'Custom',
            'weekLabel': 'W',
            'daysOfWeek': [
              '日',
              '一',
              '二',
              '三',
              '四',
              '五',
              '六'
            ],
            'monthNames': [
              '一月',
              '二月',
              '三月',
              '四月',
              '五月',
              '六月',
              '七月',
              '八月',
              '九月',
              '十月',
              '十一月',
              '十二月'
            ],
            'firstDay': 1
          }
        }, function () {})
      } else {
        if (this.temp > 50) {
          this.temp = 0;
        }
        this.temp++;
        // 递归 等待dom渲染完毕
        const _this = this;
        setTimeout(function () { _this.initQueryDateTime(id, startDates); }, 500)
      }
    },
  }
}
</script>