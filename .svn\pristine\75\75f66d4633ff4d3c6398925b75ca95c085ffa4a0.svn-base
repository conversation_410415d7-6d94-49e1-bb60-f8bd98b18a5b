<template>
  <div class="d-flex flex-column flex-root">
    <div
      class="login login-1 login-signin-on d-flex flex-column flex-lg-row flex-row-fluid bg-white"
      id="kt_login"
    >
      <!--begin::Aside-->
      <div
        class="login-aside d-flex flex-row-auto bgi-size-cover bgi-no-repeat pr-3 pb-3 p-lg-15"
        :style="{ backgroundImage: `url(${backgroundImage})` }"
      >
        <!--begin: Aside Container -->
        <div class="d-flex flex-row-fluid flex-column justify-content-between">
          <!--begin: Aside content -->
          <div class="flex-column-fluid d-flex flex-column justify-content-center">
            <div class="d-flex justify-content-between align-items-center">
              <img :src="loaderLogo" class="max-h-50px" style="display: inline-block;"/>
              <span class="badge badge-secondary opacity-90">{{ (wxEntry && wxEntry == 1) ? '大生态圈' : 'PCB智造圈' }}</span>
            </div>
            <div class="font-weight-lighter opacity-80 pl-5" style="color: #e1e3e3">
              <div v-if="wxEntry && wxEntry == 1">
                <div>销售：在线下单、在线询价、在线查进度、发布需求</div>
                <div class="pt-1">采购：在线报价、招标大厅、板材超市</div>
                <div class="pt-1">设备维保、园区生态等</div>
              </div>
              <div v-else>
                <div>协同办公:OA审批、日程、任务、报告</div>
                <div class="pt-1">在线生产、物流数据采集工具、在线系统数据报表分析</div>
              </div>
            </div>
          </div>
          <!--end: Aside content -->
        </div>
        <!--end: Aside Container -->
      </div>
      <!--begin::Aside-->

      <!--begin::Content-->
      <div class="content d-flex flex-column flex-column-fluid">
        <div class="d-flex flex-column-fluid">
          <div class="container-fluid">
            <router-view></router-view>
          </div>
        </div>
      </div>
      <!--end::Content-->
    </div>
  </div>
</template>
<style lang="scss">
@import "@/assets/sass/pages/login/login-1.scss";
</style>
<script>
export default {
  name: "auth",
  methods: {},
  computed: {
    backgroundImage() {
      return process.env.BASE_URL + "media/bg/demo-7.jpg";
    },
  },
  mounted() {
    this.wxEntry = window.localStorage.getItem("wxEntry");
  },
  data() {
    return{
      wxEntry: null,
      loaderLogo: process.env.BASE_URL + "media/logos/logo2.png"
    }
  }
};
</script>
