<template>
  <div class="bg-white rounded-sm p-3 mb-3">
    <div class="d-flex justify-content-between align-items-center">
      <div class="d-flex align-items-center font-weight-bolder font-size-h5">
        <div>{{report.name}}</div>
        <div v-if="report.flag == 1 && report.day">{{report.day}}日报告</div>
        <div v-else-if="report.flag == 2 && report.day">{{(report.day).substring(0,4)}}第{{(report.day).substring(4,6)}}周报告</div>
        <div v-else-if="report.flag == 3 && report.day">{{(report.day).substring(0,4)}}第{{(report.day).substring(4,6)}}月报告</div>
      </div>
      <div class="text-muted">提交时间:&nbsp;{{report.createdDateStr}}</div>
    </div>
    <div class="text-muted text-truncate pt-1">{{report.departmentName}}</div>
    <div class="pt-3">
      <div class="text-muted font-size-lg">工作成效</div>
      <div class="pt-2" style="word-break: break-all;">
        {{report.content}}
      </div>
    </div>
    <div class="pt-3">
      <div class="text-muted font-size-lg">总结心得</div>
      <div class="pt-2" style="word-break: break-all;">
        {{report.contentTwo}}
      </div>
    </div>
    <div class="pt-3">
      <div class="text-muted font-size-lg">计划内容</div>
      <div class="pt-2" style="word-break: break-all;">
        {{report.contentThree}}
      </div>
    </div>
    <div class="d-flex pt-5" style="word-break: break-all;">
      <div><i class="fa fa-users" style="width: 26px;"></i>共享人员：</div>
      {{report.shares}}
    </div>
    <div class="d-flex pt-5" style="word-break: break-all;">
      <div><i class="fa fa-user" style="width: 26px;"></i>直接上级：</div>
      <div>
        {{report.superName}}<span v-if="report.leadershipsName">→{{report.leadershipsName}}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "showReport",
  props: {
    report: Object
  },
}
</script>
