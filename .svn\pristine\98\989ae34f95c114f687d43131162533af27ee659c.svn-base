function getErpList(sysUser) {
    if (sysUser.erpList && sysUser.erpList.length > 0) {
        return sysUser.erpList;
    }
    return null;
}

function getCompany(sysUser) {
    if (sysUser.erpList && sysUser.erpList.length > 0){
        const com = eval('(' + window.localStorage.getItem('company') + ')');
        if (com && com.recordId){
            return com;
        }else {
            for (let i=0;i<sysUser.erpList.length;i++){
                // 默认工厂为江西工厂
                if (sysUser.erpList[i].recordId && sysUser.erpList[i].recordId == "17"){
                    return sysUser.erpList[i];
                }
            }
            if (!(this.company && this.company.recordId)){
                return sysUser.erpList[0];
            }
        }
    }
    return null;
}

export { getErpList, getCompany };
