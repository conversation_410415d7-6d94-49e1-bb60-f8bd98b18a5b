<template>
  <div v-if="userMsg && userMsg.recordId">
    <div class="mb-5">
      <h3 class="text-white">个人中心</h3>
    </div>
    <section class="bg-white p-2 rounded-sm">
      <div class="d-flex align-items-center">
        <img :src="userMsg.downloadUrl" alt="用户头像" class="avatar-common">
        <div class="flex-grow-1 pl-3">
          <div class="d-flex justify-content-between align-items-center">
            <div class="font-weight-bolder font-size-h5">{{ userMsg.userName }}</div>
            <span class="badge alert-primary d-flex align-items-center"><i class="fa fa-medal mr-1 text-primary"></i>{{ userMsg.position }}</span>
          </div>
          <div>手机号码：{{userMsg.phone}}</div>
          <div class="text-muted">{{ userMsg.departmentName }}</div>
        </div>
      </div>
    </section>
    <!-- 功能列表 -->
    <section class="bg-white mt-6 rounded-sm">
      <div class="pl-3 pr-3">
        <ul>
          <template v-for="item in itemList">
            <li class="d-flex align-items-center border-bottom pb-4 pt-4" :key="item.id" v-on:click="router_page(item.routerName)">
              <div style="width: 3rem;"><i :class="['fa', item.iconClass]"></i></div>
              <div class="flex-grow-1">{{item.name}}</div>
              <div><i class="fa fa-angle-right"></i></div>
            </li>
          </template>
        </ul>
      </div>
    </section>
    <!-- 底部按钮 -->
    <div class="pt-7">
      <button class="btn btn-outline-primary bg-white text-dark w-100" v-on:click="logOut">退出登录</button>
    </div>
    <div style="height: 100px;"></div>
  </div>
</template>

<script>
import {LOGOUT} from "@/core/services/store/auth.module";
import {ADD_BODY_CLASSNAME, REMOVE_BODY_CLASSNAME} from "@/core/services/store/htmlclass.module";
export default {
  name: 'my',
  data() {
    return {
      userMsg: {},
      itemList: [
        {id: 1, name: "信息管理", iconClass: "fa-user-circle", routerName: "/my/message"},
        {id: 2, name: "头像管理", iconClass: "fa-star", routerName: "/my/logo"},
        {id: 3, name: "密码设置", iconClass: "fa-beer", routerName: "/my/password"},
        {id: 4, name: "手机号码变更", iconClass: "fa-paper-plane", routerName: "/my/phone"},
        {id: 5, name: "消息通知", iconClass: "fa-bell", routerName: "/my/notice"},
        {id: 6, name: "我的组织架构", iconClass: "fa-rocket", routerName: "/my/myGroup"},
        {id: 7, name: "我的审批任命", iconClass: "fa-home", routerName: "/my/myPosition"},
      ]
    };
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
    }else {
      alert("请重新进入公众号");
    }
  },
  methods: {
    logOut: function (){
      if (this.userMsg && this.userMsg.openId && this.userMsg.defaultDb){
        this.message = `账号退出中...`;
        this.$store.dispatch(ADD_BODY_CLASSNAME, "page-loading");
        this.$axios.fetchPost("f/wechat/kybsoft/logOut", this.userMsg).then(result => {
          if(result.data && result.data == "success"){
            this.$store.dispatch(LOGOUT);
            this.message = `账号已退出...`;
            this.$router.push("/jumpNode");
          }else {
            alert("请重新进入公众号");
            this.$store.dispatch(REMOVE_BODY_CLASSNAME, "page-loading");
          }
        }).catch(err => {console.log(err);});
      }else {
        alert("关键信息缺失，请重新进入公众号");
      }
    },
    router_page(name) {
      this.$router.push(name);
    }
  },
}
</script>
