<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">密码设置</h3>
    </div>
    <div class="card">
      <div class="card-body p-3">
        <div class="pt-3">
          <span class="text-danger">*</span>手机号码：
          <div class="input-group">
            <input class="form-control" v-model="userMsg.phone" type="text" placeholder="手机号码" name="phone" autocomplete="off" disabled/>
            <div class="input-group-append">
              <button type="button" class="btn btn-outline-secondary" v-on:click="validatePhone">发送</button>
            </div>
          </div>
        </div>
        <div class="pt-3">
          <span class="text-danger">*</span>验证码：
          <input class="form-control" v-model="userMsg.phoneCode" type="text" placeholder="验证码" name="phoneCode" autocomplete="off" />
        </div>
        <div class="pt-3">
          <span class="text-danger">*</span>新密码：
          <input class="form-control" v-model="newPassword" type="password" placeholder="新密码" name="phoneCode" autocomplete="new-password" />
        </div>
        <div class="pt-3">
          <span class="text-danger">*</span>确认密码：
          <input class="form-control" v-model="confirmPsd" type="password" placeholder="确认密码" name="phoneCode" autocomplete="new-password" />
        </div>
        <div class="pt-3 d-flex justify-content-end">
          <button class="btn btn-outline-primary" v-on:click="editPsd">修改密码</button>
        </div>
      </div>
    </div>
    <div style="height: 100px;"></div>
  </div>
</template>
<script>
export default {
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
    }else {
      alert("请重新进入公众号");
    }
  },
  data() {
    return {
      userMsg: {},
      newPassword: "",
      confirmPsd: "",
    };
  },
  methods: {
    validatePhone() {
      if (this.userMsg) {
        if (this.userMsg.phone && this.userMsg.phone.length === 11) {
          // 请求后台获取验证码
          this.$axios.fetchPost("/hr/user/validatePhone", this.userMsg).then(data => {
            if (data.data && data.data.res === "exist") {
              alert("该手机号码已经被另外一个账号使用，请联系管理员");
            } else if (data.data && data.data.res === "success"){
              this.userMsg.code = data.data.code;
              alert("短信发送成功");
              return;
            }
          }).catch(err => {console.log(err);});
        } else {
          alert("手机号码错误");
        }
      }
    },
    editPsd() {
      if (this.userMsg && this.userMsg.recordId){
        if (!this.newPassword){
          alert("请注意必须填写新密码");
          return;
        }
        if (!this.confirmPsd){
          alert("必须确认新密码");
          return;
        }
        if (this.newPassword !== this.confirmPsd) {
          alert("请确定两次密码的一致性");
          return;
        }
        if (!this.userMsg.phoneCode){
          alert("请注意必须填写验证码");
          return;
        }
        this.userMsg.password = this.newPassword;
        this.$parent.enableLoadFlag(true);
        this.$axios.fetchPost("hr/user/editPwdLine",this.userMsg).then(result => {
          if (result.data === "fail") {
            alert("短信验证码错误");
          } else if (result.data === "success") {
            this.newPassword = "";
            this.confirmPsd = "";
            alert("密码修改成功！");
          }
          this.$parent.enableLoadFlag(false);
        }).catch(err => {console.log(err);});
      }
    },
  }
}
</script>
