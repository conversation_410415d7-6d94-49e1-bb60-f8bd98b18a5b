<template>
  <div v-if="audit && audit.auditResult && audit.auditResult.trim()">
    <!-- 使用逗号分割字符串 -->
    <template v-for="(item, index) in audit.auditResult.replace('拼板规格:“无”', '').split(';')">
      <div class="pt-2" v-if="item.trim() && !item.trim().includes('无')" :key="index">
        <span class="text-danger font-weight-bolder" v-if="item.includes('变更')">变更项：</span>
        <!-- 显示处理后的内容 -->
        <span style="word-break: break-all;" :class="item.includes('变更') || item.includes('生产编号') ? 'text-danger font-weight-bolder' : ''">{{ item.trim() }}</span>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: "craft",
  props: {
    audit: Object,
  }
}
</script>