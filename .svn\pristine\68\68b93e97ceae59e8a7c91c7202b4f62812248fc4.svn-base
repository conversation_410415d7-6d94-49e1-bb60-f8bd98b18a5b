<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">帮助与反馈</h3>
    </div>
    <div class="card">
      <div class="card-body p-3">
        <div class="font-size-h5 pb-3 font-weight-bolder">需要帮助？</div>
        <p>我们提高多种方式帮助您解决问题</p>
        <div>
          <a href="#faq" class="btn btn-outline-primary rounded-lg">查看常见问题</a>
          <a href="#commit" class="btn btn-outline-secondary rounded-lg ml-3">提交反馈</a>
        </div>
      </div>
    </div>
    <div class="card mt-3" id="faq">
      <div class="card-body p-3">
        <div class="d-flex justify-content-between align-items-center">
          <div class="font-size-h5 font-weight-bolder">常见问题</div>
<!--          <div>-->
<!--            <button class="btn btn-outline-primary btn-sm">查看全部</button>-->
<!--          </div>-->
        </div>
        <div class="pt-3">
          <div class="border-bottom pb-1">
            <div class="font-size-lg">如何承接平台的业务？</div>
            <div class="pt-1 text-muted">
              点击&nbsp;<span class="badge badge-primary">我的</span>&nbsp;，找到&nbsp;<span class="badge badge-primary">企业信息</span>&nbsp;，
              建立自己的企业档案，如果自己企业已建档，可以搜索加入，有了企业档案之后就可承接业务。
            </div>
          </div>
          <div class="border-bottom pb-1 pt-1">
            <div class="font-size-h5">如何查看订单进度？</div>
            <div class="font-size-lg pt-1 text-muted">
              点击&nbsp;<span class="badge badge-primary">交易中心</span>&nbsp;，找到想要查看的订单记录，
              可以看到从下单到签收的全流程，如果需要看生产工序，可以点击生产查看。
            </div>
          </div>
          <div class="border-bottom pb-1 pt-1">
            <div class="font-size-h5">如何发布自己的需求？</div>
            <div class="font-size-lg pt-1 text-muted">
              点击&nbsp;<span class="badge badge-primary">发布</span>&nbsp;，发布自己需要销售或者采购的产品，
              等待平台后台审核，即可上架成功，发布动态可以在&nbsp;<span class="badge badge-primary">我的</span>&nbsp;历史发布查看
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card mt-3">
      <div class="card-body p-3">
        <COOPERATION></COOPERATION>
      </div>
    </div>
    <div class="card mt-3" id="commit">
      <div class="card-body p-3">
        <div class="font-size-h5 font-weight-bolder">提交反馈</div>
        <div class="pt-3 font-size-lg">
          反馈内容
          <textarea class="form-control" v-model="content"></textarea>
        </div>
        <div class="pt-3 font-size-lg">
          手机号码
          <input class="form-control" v-model="phone" v-on:blur="verificationPhone">
        </div>
        <div class="pt-7">
          <button class="btn btn-primary w-100" v-on:click="saveFeedback">提交反馈</button>
        </div>
      </div>
    </div>
    <div style="height: 150px;"></div>
  </div>
</template>
<script>
import COOPERATION from "@/view/pages/wx/auth/utils/cooperation.vue";
export default {
  mounted() {
    this.setupSmoothScroll();
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
    }else {
      alert("请重新进入公众号");
    }
  },
  components: {
    COOPERATION
  },
  data()
  {
    return{
      userMsg:{},
      content: "",
      phone: ""
    }
  },
  methods: {
    setupSmoothScroll() {
      const links = document.querySelectorAll('a[href^="#"]:not([href="#"])'); // 排除纯#链接
      links.forEach(link => {
        link.addEventListener('click', (e) => {
          e.preventDefault();
          const targetId = link.getAttribute('href');
          const targetElement = document.querySelector(targetId);
          if (targetElement) {
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
            history.pushState(null, null, targetId);
          }
        });
      });
    },
    verificationPhone(){
      if (this.phone) {
        const trimmedPhone = this.phone.trim();
        if (trimmedPhone.length !== 11) {
          alert("手机号码应为11位数字，请重新输入！");
          this.phone = null;
          return;
        }
        const isNumber = /^[0-9]*$/.test(trimmedPhone);
        if (!isNumber) {
          alert("手机号码只能包含数字，请去除非数字字符！");
          this.phone = null;
          return;
        }
        const pattern = /^1[3-9]\d{9}$/;
        if (!pattern.test(trimmedPhone)) {
          alert("请输入有效的中国大陆手机号码！");
          this.phone = null;
          return;
        }
        this.phone = trimmedPhone;
      }
    },
    saveFeedback()
    {
      if (!this.content) {
        alert("请输入反馈内容");
        return;
      }
      if (!this.phone) {
        alert("请输入电话");
        return;
      }
      let feedback = {};
      feedback.content = this.content;
      feedback.phone = this.phone;
      feedback.userId = this.userMsg.recordId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/business/saveFeedback", feedback).then(result => {
        if(result.data && result.data == "success"){
          alert("反馈成功，我们将会在24小时内和您联系，感谢您的使用！");
          this.$router.push("/bus_person");
        }else {
          alert("反馈失败，请刷新重试");
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
  }
}
</script>