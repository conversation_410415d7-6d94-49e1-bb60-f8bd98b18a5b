<template>
  <a href="#" class="nav-link d-flex flex-column align-items-center justify-content-center text-center transition-all duration-300 px-3" :class="currentTabId == idKey ? 'active' : ''">
    <i :class="getIcon()"></i>
    <span class="text-xs mt-1">{{ name }}</span>
  </a>
</template>

<script>
export default {
  name: "BusinessMenu",
  props: {
    tabClass: String,
    name: String,
    iconClass: String,
    currentTabId: String,
    idKey: String
  },
  methods: {
    getIcon() {
      if (this.iconClass && this.currentTabId && this.idKey && (this.currentTabId == this.idKey)){
        return this.iconClass + " text-primary";
      }
      if (this.idKey == "3"){
        return this.iconClass + " fa-2x";
      }
      return this.iconClass;
    }
  }
}
</script>
<style scoped>
.nav-link {
  min-height: 60px; /* 设置最小高度 */
  padding: 0 !important; /* 移除内边距，让内容填充整个高度 */
}

.active-tab i {
  transform: scale(1.1);
}

/* 调试样式：为图标和文字容器添加背景色 */
.flex-1 {
  /* 取消注释以下两行以查看容器边界 */
  /* background-color: rgba(0, 255, 0, 0.1); */
  /* border: 1px solid rgba(0, 0, 0, 0.1); */
}

/* 优化小屏幕显示 */
@media (max-width: 360px) {
  .text-xs {
    font-size: 0.65rem;
  }
}
</style>
