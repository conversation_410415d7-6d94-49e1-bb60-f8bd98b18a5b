<template>
  <div>
    <div class="d-flex justify-content-between align-items-center bg-white rounded-sm p-3 mb-3">
      <div>
        <div class="font-weight-bolder font-size-h5">任务详情</div>
        <div class="text-muted" v-if="task.recordId">任务ID：T-{{task.recordId}}</div>
      </div>
      <div v-if="task.recordId && editStatus == '1'">
        <div v-if="task.status == 2"><span class="badge badge-success">已结束</span></div>
        <div v-else-if="task.createdBy.recordId == userMsg.employeeId || isIdInString(userMsg.employeeId, task.shareIds)">
          <!--未结束的任务，创建任务的人或者管理者可以修改或者结束-->
          <button class="btn btn-sm btn-outline-primary px-2 py-1" v-on:click="editFlag('2')">
            <div class="d-flex align-items-center"><i class="fa fa-edit"></i>修改</div>
          </button>
          <button class="btn btn-sm btn-outline-danger px-2 py-1 ml-3" v-on:click="showFinishTaskModal">
            <div class="d-flex align-items-center"><i class="fa fa-flag-checkered"></i>结束</div>
          </button>
        </div>
      </div>
      <div v-else>
        <button class="btn btn-secondary px-3 py-2" v-on:click="editFlag('1')">取消</button>&nbsp;
        <button class="btn btn-primary px-3 py-2" v-on:click="saveTask">保存</button>
      </div>
    </div>
    <div v-if="editStatus == '2'">
      <ADDTASK :task="task" ref="addTask"></ADDTASK>
    </div>
    <div v-else-if="task.recordId">
      <SHOWTASK :task="task"></SHOWTASK>
    </div>
    <div class="pb-3">
      <FILEMANAGE :fileEntity="task" entityFlag="task" :editStatus="editStatus" ref="fileManage"></FILEMANAGE>
    </div>
    <div v-if="task.recordId && editStatus == '1'">
      <COMMENT :commentEntity="task" entityFlag="task"></COMMENT>
    </div>
    <b-modal v-model="showConfirmModal" title="结束任务" ok-title="确认" cancel-title="取消" @ok="finishTask">
      <span class="text-danger font-weight-bolder font-size-lg">确定要结束该任务吗？</span>
    </b-modal>
    <div style="height: 100px;"></div>
  </div>
</template>

<script>
import ADDTASK from '@/view/pages/wx/kybsoft/work/task/details/childPage/addTask';
import SHOWTASK from '@/view/pages/wx/kybsoft/work/task/details/childPage/showTask';
import COMMENT from '@/view/pages/wx/kybsoft/work/utils/comment';
import FILEMANAGE from "@/view/pages/wx/kybsoft/work/utils/fileManage"
export default {
  name: "taskDetails",
  components: {
    ADDTASK,
    SHOWTASK,
    COMMENT,
    FILEMANAGE
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      if (!this.userMsg.employeeId){
        return;
      }
      this.taskId = this.$route.query.taskId;
      this.inFlag = this.$route.query.inFlag;
      if (this.taskId){
        this.loadData();
      }else {
        this.organizationId = this.$route.query.organizationId;
        if (this.organizationId){
          // 按id加载来源
          this.editStatus = "2";
        }else {
          alert("请重新进入公众号");
        }
      }
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return{
      userMsg: {},
      taskId: "",
      task:{} ,
      editStatus: "1", // 1:查看，2:修改
      showConfirmModal:false,
      organizationId: "",
      inFlag: ''
    }
  },
  methods:{
    isIdInString(targetId, idString) {
      const idArray = idString.split(',').map(id => id.trim());
      return idArray.includes(targetId);
    },
    enableLoadFlag(flag) {
      this.$parent.enableLoadFlag(flag);
    },
    editFlag(flag) {
      if (!this.task || !this.task.recordId) {
        this.$router.push("/submit");
      }
      this.editStatus = flag;
    },
    loadData () {
      const task = {};
      task.recordId = this.taskId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/getTask", task).then(result => {
        if (result && result.data){
          this.task = result.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    showFinishTaskModal() {
      this.showConfirmModal = true;
    },
    finishTask() {
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/finishTask",this.task).then(result => {
        if (result && result.data){
          alert('任务结束成功！');
          this.successGo();
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    saveTask() {
      let task = this.$refs.addTask.task;
      const title = this.$refs.addTask.title;
      const content = this.$refs.addTask.content;
      const completeTime = this.$refs.addTask.completeTime;
      const emergencyLevel = this.$refs.addTask.emergencyLevel;
      const principalIds = this.$refs.addTask.principalIds;
      const shareIds = this.$refs.addTask.shareIds;
      const participateIds = this.$refs.addTask.participateIds;
      const serverList = this.$refs.fileManage.serverList;
      const empList = this.$refs.addTask.empList;
      const empMap = new Map();
      empList.forEach(emp => empMap.set(emp.recordId, emp));
      if (task && task.recordId){
        let comment = "";
        if ((!title && task.title) || (title && !task.title) || title !== task.title) {
          comment = "标题：" + task.title + "变更为" + title;
        }
        if ((!content && task.content) || (content && !task.content) || content !== task.content) {
          const msg = "内容：" + task.content + "变更为" + content;
          comment = comment ? comment + ";" + msg : msg;
        }
        if ((!completeTime && task.completeTime) || (completeTime && !task.completeTime) || completeTime !== task.completeTime) {
          const msg = "截止时间：" + task.completeTime + "变更为" + completeTime;
          comment = comment ? comment + ";" + msg : msg;
        }
        if ((!emergencyLevel && task.emergencyLevel) || (emergencyLevel && !task.emergencyLevel) || emergencyLevel !== task.emergencyLevel) {
          const msg = "优先级：" + this.getLevelStr(task.emergencyLevel) + "变更为" + this.getLevelStr(emergencyLevel);
          comment = comment ? comment + ";" + msg : msg;
        }
        if ((!principalIds && task.principals.recordId) || (principalIds && !task.principals.recordId) || principalIds !== task.principals.recordId) {
          let empNameList1 = "";
          if (task.principals.recordId) {
            empNameList1 = task.principals.recordId.split(',')
                .filter(recordId => recordId && empMap.has(recordId))
                .map(recordId => empMap.get(recordId).name).join(',');
          }
          let empNameList2 = "";
          if (principalIds) {
            empNameList2 = principalIds.split(',')
                .filter(recordId => recordId && empMap.has(recordId))
                .map(recordId => empMap.get(recordId).name).join(',');
          }
          const msg = "负责人：" + empNameList1 + "变更为" + empNameList2;
          comment = comment ? comment + ";" + msg : msg;
        }
        if ((!shareIds && task.shareIds) || (shareIds && !task.shareIds) || shareIds !== task.shareIds) {
          let empNameList1 = "";
          if (task.shareIds) {
            empNameList1 = task.shareIds.split(',')
                .filter(recordId => recordId && empMap.has(recordId))
                .map(recordId => empMap.get(recordId).name).join(',');
          }
          let empNameList2 = "";
          if (shareIds) {
            empNameList2 = shareIds.split(',')
                .filter(recordId => recordId && empMap.has(recordId))
                .map(recordId => empMap.get(recordId).name).join(',');
          }
          const msg = "管理人：" + empNameList1 + "变更为" + empNameList2;
          comment = comment ? comment + ";" + msg : msg;
        }
        if ((!participateIds && task.participateIds) || (participateIds && !task.participateIds) || participateIds !== task.participateIds) {
          let empNameList1 = "";
          if (task.participateIds) {
            empNameList1 = task.participateIds.split(',')
                .filter(recordId => recordId && empMap.has(recordId))
                .map(recordId => empMap.get(recordId).name).join(',');
          }
          let empNameList2 = "";
          if (participateIds) {
            empNameList2 = participateIds.split(',')
                .filter(recordId => recordId && empMap.has(recordId))
                .map(recordId => empMap.get(recordId).name).join(',');
          }
          const msg = "参与人：" + empNameList1 + "变更为" + empNameList2;
          comment = comment ? comment + ";" + msg : msg;
        }
        if (comment){
          task.comment = comment;
        }
      }
      task.title = title ? title : "";
      task.content = content ? content : "";
      task.completeTime = completeTime ? completeTime : "";
      task.emergencyLevel = emergencyLevel ? emergencyLevel : "";
      task.principalIds = principalIds ? principalIds : "";
      task.shareIds = shareIds ? shareIds : "";
      task.participateIds = participateIds ? participateIds : "";
      //负责人
      task.principals = {};
      task.principals.recordId = task.principalIds;
      if (serverList && serverList.length > 0) {
        task.serverList = serverList;
      }
      if(!task.title) {
        alert("请填写任务标题！");
        return;
      }
      if(!task.content) {
        alert("请填写任务内容！");
        return;
      }
      if(!task.principalIds) {
        alert("请选择任务负责人！");
        return;
      }
      if (!task.emergencyLevel) {
        alert("请选择任务紧急程度！");
        return;
      }
      task.employeeId = this.userMsg.employeeId;
      //转换为参与人员集合
      let participateList = [];
      if (task.participateIds) {
        participateList = task.participateIds.split(',')
            .filter(recordId => recordId && empMap.has(recordId))
            .map(recordId => empMap.get(recordId));
      }
      task.participateList = participateList;
      //转换为管理人员集合
      let sharesList = [];
      if (task.shareIds) {
        sharesList = task.shareIds.split(',')
            .filter(recordId => recordId && empMap.has(recordId))
            .map(recordId => empMap.get(recordId));
      }
      task.sharesList = sharesList;
      task.createdBy = {};
      task.createdBy.recordId = this.userMsg.employeeId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost("f/wechat/kybsoftOA/saveTask", task).then(result => {
        if (result && result.data){
          if (result.data == "success"){
            alert("提交成功");
            if (this.task && this.task.recordId) {
              this.successGo();
            }else{
              this.$router.push("/submit");
            }
          }else {
            alert(result.data);
          }
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err);});
    },
    getLevelStr(emergencyLevel) {
      if (emergencyLevel && emergencyLevel == "1"){
        return "低";
      }else if (emergencyLevel && emergencyLevel == "2"){
        return "中";
      }else if (emergencyLevel && emergencyLevel == "3"){
        return "高";
      }
      return "";
    },
    successGo() {
      if (this.inFlag && this.inFlag == '2'){
        this.$router.push("/worktable");
        return;
      }
      this.$router.push("/work");
    }
  }
}
</script>
