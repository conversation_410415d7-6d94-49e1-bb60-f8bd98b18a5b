<template>
  <div>
    <div class="pb-1" v-if="audit && audit.auditResult && audit.auditResult.trim() && audit.auditType == '3'">
      <!-- 使用逗号分割字符串 -->
      <template v-for="(item, index) in audit.auditResult.split(';')">
        <div class="pt-1" v-if="item.trim() && !item.trim().includes('无')" :key="index">
          <!-- 显示处理后的内容 -->
          <span style="word-break: break-all;" :class="item.includes('变更') ? 'text-danger font-weight-bolder' : ''">{{ item.trim() }}</span>
        </div>
      </template>
    </div>
    <div v-if="showAuditData && showAuditData.length > 0 && showAuditData[0].contract">
      <div>
        <div class="font-weight-bolder">提交公司：{{showAuditData[0].contract.supplierName}}</div>
        <div>终端客户：{{showAuditData[0].contract.customerName}}</div>
        <div class="d-flex justify-content-between">
          <div>合同编号：{{showAuditData[0].contract.no}}</div>
          <div>客户订单号：{{showAuditData[0].contract.customerPo}}</div>
        </div>
        <div class="d-flex justify-content-between">
          <div>下单日期：{{showAuditData[0].contract.orderDate}}</div>
          <div class="text-primary font-weight-bolder">业务员：{{showAuditData[0].contract.userName}}</div>
        </div>
        <div style="word-break: break-all;">
          <div>送货地址：{{showAuditData[0].contract.deliveryPlace}}</div>
        </div>
        <div class="d-flex">
          客户需求：
          <div>{{showAuditData[0].contract.freightWay}}</div>
          <div class="pl-2">{{showAuditData[0].contract.deliveryWay}}</div>
          <div class="pl-2">{{showAuditData[0].contract.taxDescript}}</div>
          <div class="pl-2">{{showAuditData[0].contract.payWay}}</div>
        </div>
        <div v-if="isShowFeeFlag">
          <div class="d-flex justify-content-between">
            <div class="font-weight-bolder">总面积：{{showAuditData[0].contract.area}}</div>
            <div class="text-primary font-weight-bolder">总金额：{{showAuditData[0].contract.currencyType}}&nbsp;{{showAuditData[0].contract.totalAmt}}</div>
          </div>
          <div class="d-flex justify-content-between text-primary">
            <div>加工成本：{{processFeeSum}}(￥{{(processFeeSum / showAuditData[0].contract.area).toFixed(2)}})</div>
            <div>材料成本：{{materialFeeSum}}(￥{{(materialFeeSum / showAuditData[0].contract.area).toFixed(2)}})</div>
          </div>
          <div class="d-flex justify-content-between text-primary">
            <div>营运费：{{saleFeeSum + manageFeeSum}}</div>
            <div>总成本：{{netCostFeeSum}}</div>
            <div class="font-weight-bolder">利润率：{{((showAuditData[0].contract.totalAmt - netCostFeeSum) / showAuditData[0].contract.totalAmt * 100).toFixed(2)}}%</div>
          </div>
        </div>
      </div>
      <div>
        <div class="d-flex justify-content-between align-items-center pb-1">
          <div class="font-weight-bolder">订单明细</div>
          <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlag">{{ flag && flag == '1' ? '收起' : '展开' }}</button>
        </div>
        <div v-if="flag == '1'">
          <section>
            <ORDERDETAILUTIL :showAuditData = "showAuditData" :isShowFeeFlag = "isShowFeeFlag"></ORDERDETAILUTIL>
          </section>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ORDERDETAILUTIL from '@/view/pages/wx/kybsoft/work/audit/details/childPage/customPage/detail/detailUtil/orderDetailUtil'
export default {
  name: "order",
  props: {
    showAuditData: [Object, Array],
    audit: Object
  },
  components: {
    ORDERDETAILUTIL
  },
  data() {
    return{
      flag: "2",
      processFeeSum: 0,
      materialFeeSum: 0,
      saleFeeSum: 0,
      manageFeeSum: 0,
      netCostFeeSum: 0,
      isShowFeeFlag: false,
    }
  },
  mounted() {
    this.loadSumDate();
  },
  methods: {
    changeFlag() {
      if (this.flag == "1"){
        this.flag = "2";
      }else {
        this.flag = "1";
      }
    },
    loadSumDate() {
      for (let i=0;i<this.showAuditData.length;i++){
        let item = this.showAuditData[i];
        this.processFeeSum = Number(this.processFeeSum) + Number(item.processFee);
        this.materialFeeSum = Number(this.materialFeeSum) + Number(item.materialFee);
        this.saleFeeSum = Number(this.saleFeeSum) + Number(item.saleFee);
        this.manageFeeSum = Number(this.manageFeeSum) + Number(item.manageFee);
        this.netCostFeeSum = Number(this.netCostFeeSum) + Number(item.netCostFee);
      }
      this.isShowFee();
    },
    isShowFee() {
      if (this.audit && this.audit.auditType == '23'){
        this.isShowFeeFlag = true;
        return;
      }
      this.isShowFeeFlag = false;
    }
  }
}
</script>