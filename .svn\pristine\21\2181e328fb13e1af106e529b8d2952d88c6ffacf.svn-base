<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">企业信息</h3>
    </div>
    <div class="card card-custom">
      <div class="card-body p-3">
        <div class="d-flex justify-content-between align-items-center pb-3">
          <div>
            <div v-if="localIds && localIds.length > 0">
              <template v-for="(item,index) in localIds">
                <div :key="index" class="image-container-common"><img :src="item" v-on:click="preview(item)"></div>
              </template>
            </div>
            <div class="image-container-common" v-if="!(localIds && localIds.length > 0) && company.logo"><img :src="company.logoStr"></div>
          </div>
          <button class="btn btn-outline-primary" v-on:click="chooseImg()">更换LOGO</button>
        </div>
        <div class="pb-3">
          企业名称
          <input class="form-control form-control-solid" v-model="company.name" type="text" autocomplete="off" required>
        </div>
        <div class="pb-3">
          税号
          <input class="form-control form-control-solid" v-model="company.licence" type="text" autocomplete="off" required>
        </div>
        <div class="row pb-3">
          <div class="col-6">
            主要联系人
            <input class="form-control form-control-solid" v-model="company.linkMan" type="text" autocomplete="off">
          </div>
          <div class="col-6">
            手机号码
            <input class="form-control form-control-solid" v-model="company.linkPhone" type="text" autocomplete="off">
          </div>
        </div>
        <div class="pb-3">
          送货地址
          <textarea class="form-control form-control-solid" v-model="company.address" type="text" autocomplete="off"></textarea>
        </div>
        <div class="pb-3">
          公司简介
          <textarea class="form-control form-control-solid" v-model="company.introduction" type="text" autocomplete="off"></textarea>
        </div>
        <div class="d-flex justify-content-between align-items-center pb-3">
          营业执照复印件加盖公司公章
          <button class="btn btn-outline-primary" v-on:click="chooseImgTwo()">上传</button>
        </div>
        <div v-if="localTwoIds && localTwoIds.length > 0">
          <template v-for="(item,index) in localTwoIds">
            <div :key="index">
              <img alt="Pic" :src="item" v-on:click="previewTwo(item)" style="height: 150px;"/>
            </div>
          </template>
        </div>
        <div v-if="!(localTwoIds && localTwoIds.length > 0) && company.imgUrl">
          <img alt="Pic" :src="company.imgUrlStr" style="height: 150px;" v-on:click="previewLine(company.imgUrlStr)"/>
        </div>
        <div class="row pt-7 pb-3">
          <div class="col-12 text-center">
            <button class="btn btn-sm btn-primary w-100" v-on:click="saveCompany">保&emsp;存</button>
          </div>
        </div>
      </div>
    </div>
    <div style="height: 100px;"></div>
  </div>
</template>

<script>
import { getWxMsg } from '@/assets/js/utils/wechatImgUtils';
import wx from "weixin-js-sdk";
export default {
  name: "companyDetails",
  created:function(){
    if (process.env.NODE_ENV === "production"){
      getWxMsg(window.location.href.split('#')[0], wx);
    }
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.companyId = this.$route.query.companyId;
      if (this.companyId){
        this.loadData();
      }else {
        alert("请刷新重试");
      }
    }else {
      alert("请重新进入公众号");
    }
  },
  data(){
    return{
      userMsg: {},
      companyId: "",
      company: {},
      localIds: [],
      serverList: [],
      localIdsCopy: [],
      localTwoIds: [],
      serverTwoList: [],
      localIdsTwoCopy: []
    }
  },
  methods: {
    loadData() {
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPostString('f/wechat/business/getCompany', this.companyId).then((result) => {
        if (result && result.data) {
          this.company = result.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)})
    },
    manage() {
      this.$router.push({path: "/b/company/companyDetails/manageEmp",
        query: {companyId: this.companyId, name: this.company.name}
      });
    },
    chooseImg:function(){
      const _this = this;
      wx.chooseImage({
        count: 1, // 默认9
        sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
          if (res.localIds && res.localIds.length > 0){
            _this.localIds = [];
            _this.localIds.push(res.localIds[0]);
            // 上传图片
            _this.serverList = [];
            _this.localIdsCopy = [];
            for (let i = 0; i < _this.localIds.length; i++) {
              _this.localIdsCopy.push(_this.localIds[i]);
            }
            _this.uploadImage();
          }
        }
      });
    },
    uploadImage:function(){
      const _this = this;
      if(_this.localIdsCopy && _this.localIdsCopy.length > 0){
        alert("开始上传");
        wx.uploadImage({
          localId: _this.localIdsCopy[0], // 需要上传的图片的本地ID，由chooseImage接口获得
          isShowProgressTips: 1, // 默认为1，显示进度提示
          success: function (res) {
            _this.serverList.push(res.serverId);
            alert("结束上传");
          }
        });
      }
    },
    chooseImgTwo:function(){
      const _this = this;
      wx.chooseImage({
        count: 1, // 默认9
        sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
          if (res.localIds && res.localIds.length > 0){
            _this.localTwoIds = [];
            _this.localTwoIds.push(res.localIds[0]);
            // 上传图片
            _this.serverTwoList = [];
            _this.localIdsTwoCopy = [];
            for (let i = 0; i < _this.localTwoIds.length; i++) {
              _this.localIdsTwoCopy.push(_this.localTwoIds[i]);
            }
            _this.uploadImageTwo();
          }
        }
      });
    },
    uploadImageTwo:function(){
      const _this = this;
      if(_this.localIdsTwoCopy && _this.localIdsTwoCopy.length > 0){
        alert("开始上传");
        wx.uploadImage({
          localId: _this.localIdsTwoCopy[0], // 需要上传的图片的本地ID，由chooseImage接口获得
          isShowProgressTips: 1, // 默认为1，显示进度提示
          success: function (res) {
            _this.serverTwoList.push(res.serverId);
            alert("结束上传");
          }
        });
      }
    },
    preview:function(item){
      const _this = this;
      wx.previewImage({
        current: item, // 当前显示图片的http链接
        urls: _this.localIds // 需要预览的图片http链接列表
      });
    },
    previewTwo:function(item){
      const _this = this;
      wx.previewImage({
        current: item, // 当前显示图片的http链接
        urls: _this.localTwoIds // 需要预览的图片http链接列表
      });
    },
    previewLine:function(item){
      const localIdsLine = [];
      localIdsLine.push(item);
      wx.previewImage({
        current: item, // 当前显示图片的http链接
        urls: localIdsLine // 需要预览的图片http链接列表
      });
    },
    saveCompany() {
      if (!(this.company.name && this.company.legalPerson && this.company.licence)){
        alert("请填写完整信息");
        return;
      }
      this.company.code = this.company.name;
      this.company.serverList = this.serverList;
      this.company.serverTwoList = this.serverTwoList;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPostString('f/wechat/business/saveCompany', this.company).then((result) => {
        if (result && result.data) {
          if (result.data == "exit"){
            alert("公司信息已经注册！");
          }else if (result.data == "exitErp"){
            alert("该ERP公司已经绑定！");
          }else if (result.data == "success"){
            alert("公司保存成功！");
            this.$router.push("/b/company");
          }else {
            alert(result.data);
          }
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)})
    }
  }
}
</script>