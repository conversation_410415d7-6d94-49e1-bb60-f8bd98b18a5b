<template>
<div>
  <div class="mb-5">
    <h3 class="text-white">我的组织架构</h3>
  </div>
  <template v-for="item in orgList">
    <div class="mb-7" :key="item.id">
      <div class="card mb-6" v-if="item.recordId == userMsg.oaDepartId">
        <div class="card-body p-3">
          <div class="font-weight-bolder" style="font-size: 16px;">主组织</div>
          <div style="font-size: 15px;">
            {{ item.departmentName }}
          </div>
          <div class="d-flex justify-content-between" style="font-size: 13px;">
            <div>编码：{{ item.no }}</div>
            <span class="badge badge-success" v-if="item.status && item.status == '1'">有效</span>
            <span class="badge badge-danger" v-else>无效</span>
          </div>
        </div>
      </div>
    </div>
  </template>
  <div class="card">
    <div class="card-body p-3">
      <div class="font-weight-bolder" style="font-size: 13px;">兼职组织</div>
      <div class="pl-1 pr-1">
        <template v-for="item in orgList">
          <div class="alert-secondary rounded-xl mt-3 p-3" v-if="item.recordId !== userMsg.oaDepartId" :key="item.recordId">
            {{item.departmentName}}
            <div class="d-flex justify-content-between text-muted" style="font-size: 13px;">
              <div>编码:&nbsp;{{ item.no }}</div>
              <span class="badge badge-success" v-if="item.status && item.status == '1'">有效</span>
              <span class="badge badge-danger" v-else>无效</span>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
  <div style="height: 100px;"></div>
</div>
</template>

<script>
export default {
  name: "myGroup",
  data() {
    return {
      userMsg: {},
      orgList: []
    }
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  methods: {
    loadData() {
      this.orgList = [];
      const query = {};
      query.recordId = this.userMsg.employeeId;
      this.$parent.enableLoadFlag(true);
      this.$axios.fetchPost('hr/user/getGroupOrganizateList', query).then((result) => {
        if (result && result.data) {
          this.orgList = result.data;
        }
        this.$parent.enableLoadFlag(false);
      }).catch(err => {console.log(err)})
    }
  }
}
</script>
