<template>
  <div class="text-white shadow-md rounded-lg" style="background-color: rgba(0, 43, 91, 0.6)">
    <div class="container mx-auto px-2 py-2 d-flex align-items-center justify-content-between">
      <!-- 通知图标 -->
      <div class="d-flex align-items-center mr-2 flex-shrink-0">
        <i class="fa fa-bell text-secondary text-lg mr-1"></i>
        <span class="font-weight-bold text-xs">通知</span>
      </div>

      <!-- 通知内容（滚动） -->
      <div class="flex-grow-1 ml-1 mr-2" style="max-width: 80%;">
        <div class="overflow-hidden">
          <div :class="{'notice-marquee': isMarquee}" class="d-flex align-items-center">
            <span class="text-xs ml-3 text-truncate">{{ noticeMsg || '加载中...' }}</span>
          </div>
        </div>
      </div>

      <!-- 更多按钮 -->
      <button class="hover:text-white transition-colors flex-shrink-0" @click="onMoreClick">
        <i class="fa fa-angle-right text-lg text-white"></i>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NoticeBar',
  data() {
    return {
      isMarquee: false,
      noticeMsg: "",
    }
  },
  mounted() {
    // 计算是否需要滚动（内容宽度超过容器宽度）
    this.$nextTick(() => {
      const container = this.$el.querySelector('.overflow-hidden');
      const content = this.$el.querySelector('.d-flex');

      if (content && container && content.offsetWidth > container.offsetWidth) {
        this.isMarquee = true;
      } else {
        this.isMarquee = false;
      }
    });
    //查询最新一笔通知
    this.loadData();
  },
  methods: {
    onMoreClick() {
      // 处理点击"更多"按钮的逻辑
      this.$router.push("/noticeList");
    },
    loadData() {
      this.$axios.fetchPost("f/wechat/business/getCurrNotice",{}).then(result => {
        if (result && result.data){
          this.noticeMsg = result.data[0].noticeMsg;
        }
      }).catch(err => {console.log(err);});
    },
  },
}
</script>

<style scoped>
.notice-marquee {
  white-space: nowrap;
  overflow: hidden;
  box-sizing: border-box;
  animation: marquee 15s linear infinite;
  will-change: transform;
}

.notice-marquee:hover {
  animation-play-state: paused;
}

@keyframes marquee {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

.text-secondary {
  color: #FF9F1C !important;
}

.text-white-75 {
  color: rgba(255, 255, 255, 0.75) !important;
}

.text-white-80 {
  color: rgba(255, 255, 255, 0.8) !important;
}

@-webkit-keyframes marquee {
  0% { -webkit-transform: translateX(100%); }
  100% { -webkit-transform: translateX(-100%); }
}

/* 移动端适配优化 */
@media (max-width: 576px) {
  .text-xs {
    font-size: 11px !important;
  }

  .py-2 {
    padding-top: 0.3rem !important;
    padding-bottom: 0.3rem !important;
  }

  .mr-3 {
    margin-right: 0.5rem !important;
  }

  .fa-bell {
    font-size: 14px !important;
  }

  .fa-angle-right {
    font-size: 16px !important;
  }
}
</style>
