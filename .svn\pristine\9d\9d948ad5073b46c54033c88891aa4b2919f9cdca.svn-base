<template>
  <div>
    <div class="mb-5">
      <h3 class="text-white">我的审批任命</h3>
    </div>
    <template v-for="item in showList">
      <div class="mb-3" :key="item.id">
        <div class="card">
          <div class="card-body p-3">
            <div style="font-size: 15px;">
              {{ item.departName }}
            </div>
            <div style="font-size: 15px;">
              {{ item.name }}
            </div>
            <div class="d-flex justify-content-between text-muted" style="font-size: 13px;">
              <div>任命时间：{{item.joinTime}}</div>
              <span class="badge badge-success" v-if="item.status && item.status == '1'">有效</span>
              <span class="badge badge-danger" v-else>无效</span>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div style="height: 100px;"></div>
  </div>
</template>

<script>
export default {
  name: "myPosition",
  data() {
    return {
      userMsg: {},
      showList: []
    }
  },
  mounted() {
    this.userMsg = {};
    const sysUser = this.$store.getters.currentUser;
    if (sysUser && sysUser.recordId){
      this.userMsg = sysUser;
      this.loadData();
    }else {
      alert("请重新进入公众号");
    }
  },
  methods: {
    loadData() {
      const query = {};
      query.recordId = this.userMsg.employeeId;
      query.defaultDb = this.userMsg.defaultDb;
      query.groupManageId = this.userMsg.groupManageId;
      this.$axios.fetchPost('hr/user/getGroupPositionList', query).then((result) => {
        if (result && result.data) {
          this.showList = result.data;
        }
      }).catch(err => {console.log(err)})
    }
  }
}
</script>
