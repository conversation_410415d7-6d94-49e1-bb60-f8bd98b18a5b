<template>
  <div class="p-1 bg-white rounded-lg">
    <div :id="carouselId" class="carousel slide" data-bs-ride="carousel">
      <!-- 指示器 -->
      <div class="carousel-indicators">
        <button
            v-for="(item, index) in slides"
            :key="index"
            :data-bs-target="'#' + carouselId"
            :data-bs-slide-to="index"
            :class="{ active: currentIndex === index }"
            @click="setActiveSlide(index)"
        ></button>
      </div>

      <!-- 轮播内容容器，设置固定高度 -->
      <div class="carousel-inner h-64 md:h-96 rounded-lg overflow-hidden"> <!-- 固定高度：64rem=400px，md=600px -->
        <div
            v-for="(item, index) in slides"
            :key="index"
            class="carousel-item"
            :class="{ active: currentIndex === index }"
        >
          <!-- 图片自适应容器，保持比例并填满 -->
          <img
              :src="item.src"
              class="d-block w-100 h-full object-cover">
          <div class="carousel-caption d-md-block bg-gradient-to-t from-black/80 to-transparent p-6 bottom-0 left-0 right-0">
            <h5 class="text-xl font-bold mb-3 text-white"> {{ item.title }} </h5>
            <p class="text-base text-white/90 mb-0"> {{ item.description }} </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Carousel',
  props: {
    slides: { type: Array, required: true },
    interval: { type: Number, default: 3000 },
    carouselId: { type: String, default: 'carouselExample' }
  },
  data() {
    return { currentIndex: 0, intervalId: null }
  },
  mounted() {
    this.initializeCarousel()
    this.startCarousel()
  },
  beforeDestroy() { this.stopCarousel() },
  methods: {
    // 幻灯片控制方法（保持不变）
    setActiveSlide(index) { this.currentIndex = index },
    prevSlide() { this.currentIndex = (this.currentIndex - 1 + this.slides.length) % this.slides.length },
    nextSlide() { this.currentIndex = (this.currentIndex + 1) % this.slides.length },
    startCarousel() { this.intervalId = setInterval(() => this.nextSlide(), this.interval) },
    stopCarousel() { clearInterval(this.intervalId) },
    initializeCarousel() {
      if (window.bootstrap) {
        const el = document.getElementById(this.carouselId)
        el && new window.bootstrap.Carousel(el, { interval: false }) // 禁用Bootstrap原生自动轮播，使用我们的逻辑
      }
    }
  }
}
</script>

<style scoped>
/* 指示器样式（保持不变） */
.carousel-indicators button {
  width: 12px; height: 12px; border-radius: 50%; background: rgba(255,255,255,0.5); border: none; margin: 0 6px; transition: all 0.3s ease;
}
.carousel-indicators button.active { background: #fff; transform: scale(1.2); box-shadow: 0 0 8px rgba(255,255,255,0.8); }

/* 标题描述样式（保持不变） */
.carousel-caption { font-family: 'Segoe UI', sans-serif; }
.carousel-caption h5 { margin-bottom: 0.5rem; }
</style>
