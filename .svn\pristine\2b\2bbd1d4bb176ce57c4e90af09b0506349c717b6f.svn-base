/**
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2020 <PERSON><PERSON><PERSON> <<EMAIL>>
 */

import Alias from './<PERSON>as';
import Aria from './Aria';
import Declarative from './Declarative';
import DefaultSubmit from './DefaultSubmit';
import Dependency from './Dependency';
import Excluded from './Excluded';
import FieldStatus from './FieldStatus';
import Framework from './Framework';
import Icon from './Icon';
import Message from './Message';
import Sequence from './Sequence';
import SubmitButton from './SubmitButton';
import Tooltip from './Tooltip';
import Trigger from './Trigger';

export default {
    Alias,
    Aria,
    Declarative,
    DefaultSubmit,
    Dependency,
    Excluded,
    FieldStatus,
    Framework,
    Icon,
    Message,
    Sequence,
    SubmitButton,
    Tooltip,
    Trigger,
};
